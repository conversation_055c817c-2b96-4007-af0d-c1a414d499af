{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bfeb395e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Function: f(x) = 2x + -3\n", "Slope: 2\n", "Y-intercept: -3\n", "X-intercept: 1.500\n", "Function is increasing\n", "------------------------------\n", "Function: f(x) = -1x + 2\n", "Slope: -1\n", "Y-intercept: 2\n", "X-intercept: 2.000\n", "Function is decreasing\n", "------------------------------\n", "Function: f(x) = 0.5x + 1\n", "Slope: 0.5\n", "Y-intercept: 1\n", "X-intercept: -2.000\n", "Function is increasing\n", "------------------------------\n", "Function: f(x) = 0x + -1\n", "Slope: 0\n", "Y-intercept: -1\n", "No x-intercept (horizontal line)\n", "Function is constant\n", "------------------------------\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Define linear function\n", "def linear_function(x, m, b):\n", "    \"\"\"\n", "    Linear function: f(x) = mx + b\n", "    \n", "    Parameters:\n", "    x: input values\n", "    m: slope\n", "    b: y-intercept\n", "    \"\"\"\n", "    return m * x + b\n", "\n", "# Create x values\n", "x = np.linspace(-5, 5, 100)\n", "\n", "# Define different linear functions\n", "functions = [\n", "    {\"m\": 2, \"b\": -3, \"label\": \"f(x) = 2x - 3\", \"color\": \"blue\"},\n", "    {\"m\": -1, \"b\": 2, \"label\": \"g(x) = -x + 2\", \"color\": \"red\"},\n", "    {\"m\": 0.5, \"b\": 1, \"label\": \"h(x) = 0.5x + 1\", \"color\": \"green\"},\n", "    {\"m\": 0, \"b\": -1, \"label\": \"k(x) = -1\", \"color\": \"orange\"}\n", "]\n", "\n", "# Create the plot\n", "plt.figure(figsize=(10, 8))\n", "plt.grid(True, alpha=0.3)\n", "\n", "for func in functions:\n", "    y = linear_function(x, func[\"m\"], func[\"b\"])\n", "    plt.plot(x, y, label=func[\"label\"], color=func[\"color\"], linewidth=2)\n", "\n", "plt.xlabel(\"x\", fontsize=12)\n", "plt.ylabel(\"f(x)\", fontsize=12)\n", "plt.title(\"Linear Functions Comparison\", fontsize=14, fontweight='bold')\n", "plt.legend(fontsize=10)\n", "plt.axhline(y=0, color='black', linewidth=0.5)\n", "plt.axvline(x=0, color='black', linewidth=0.5)\n", "plt.xlim(-5, 5)\n", "plt.ylim(-8, 8)\n", "plt.show()\n", "\n", "# Calculate key properties\n", "def analyze_linear_function(m, b):\n", "    \"\"\"Analyze properties of a linear function\"\"\"\n", "    print(f\"Function: f(x) = {m}x + {b}\")\n", "    print(f\"Slope: {m}\")\n", "    print(f\"Y-intercept: {b}\")\n", "    \n", "    if m != 0:\n", "        x_intercept = -b / m\n", "        print(f\"X-intercept: {x_intercept:.3f}\")\n", "    else:\n", "        print(\"No x-intercept (horizontal line)\")\n", "    \n", "    if m > 0:\n", "        print(\"Function is increasing\")\n", "    elif m < 0:\n", "        print(\"Function is decreasing\")\n", "    else:\n", "        print(\"Function is constant\")\n", "    print(\"-\" * 30)\n", "\n", "# Analyze our example functions\n", "for func in functions:\n", "    analyze_linear_function(func[\"m\"], func[\"b\"])"]}, {"cell_type": "code", "execution_count": 2, "id": "49251837", "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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********************************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", "text/plain": ["<Figure size 1200x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Function: f(x) = 1x² + 0x + 0\n", "Vertex: (0.000, 0.000)\n", "Opens upward (has minimum)\n", "Axis of symmetry: x = 0.000\n", "Y-intercept: (0, 0)\n", "Roots: x₁ = 0.000, x₂ = 0.000\n", "----------------------------------------\n", "Function: f(x) = -2x² + 4x + 1\n", "Vertex: (1.000, 3.000)\n", "Opens downward (has maximum)\n", "Axis of symmetry: x = 1.000\n", "Y-intercept: (0, 1)\n", "Roots: x₁ = -0.225, x₂ = 2.225\n", "----------------------------------------\n", "Function: f(x) = 0.5x² + -2x + 3\n", "Vertex: (2.000, 1.000)\n", "Opens upward (has minimum)\n", "Axis of symmetry: x = 2.000\n", "Y-intercept: (0, 3)\n", "Complex roots: x₁ = (2+1.4142135623730951j), x₂ = (2-1.4142135623730951j)\n", "----------------------------------------\n", "Function: f(x) = -1x² + 0x + 4\n", "Vertex: (-0.000, 4.000)\n", "Opens downward (has maximum)\n", "Axis of symmetry: x = -0.000\n", "Y-intercept: (0, 4)\n", "Roots: x₁ = -2.000, x₂ = 2.000\n", "----------------------------------------\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import cmath\n", "\n", "# Define quadratic function\n", "def quadratic_function(x, a, b, c):\n", "    \"\"\"\n", "    Quadratic function: f(x) = ax² + bx + c\n", "    \n", "    Parameters:\n", "    x: input values\n", "    a, b, c: coefficients\n", "    \"\"\"\n", "    return a * x**2 + b * x + c\n", "\n", "# Function to find vertex\n", "def find_vertex(a, b, c):\n", "    \"\"\"Find vertex of parabola\"\"\"\n", "    x_vertex = -b / (2 * a)\n", "    y_vertex = quadratic_function(x_vertex, a, b, c)\n", "    return x_vertex, y_vertex\n", "\n", "# Function to find roots using quadratic formula\n", "def find_roots(a, b, c):\n", "    \"\"\"Find roots of quadratic equation\"\"\"\n", "    discriminant = b**2 - 4*a*c\n", "    \n", "    if discriminant >= 0:\n", "        root1 = (-b + np.sqrt(discriminant)) / (2*a)\n", "        root2 = (-b - np.sqrt(discriminant)) / (2*a)\n", "        return root1, root2, \"real\"\n", "    else:\n", "        root1 = (-b + cmath.sqrt(discriminant)) / (2*a)\n", "        root2 = (-b - cmath.sqrt(discriminant)) / (2*a)\n", "        return root1, root2, \"complex\"\n", "\n", "# Create x values\n", "x = np.linspace(-5, 5, 100)\n", "\n", "# Define different quadratic functions\n", "functions = [\n", "    {\"a\": 1, \"b\": 0, \"c\": 0, \"label\": \"f(x) = x²\", \"color\": \"blue\"},\n", "    {\"a\": -2, \"b\": 4, \"c\": 1, \"label\": \"g(x) = -2x² + 4x + 1\", \"color\": \"red\"},\n", "    {\"a\": 0.5, \"b\": -2, \"c\": 3, \"label\": \"h(x) = 0.5x² - 2x + 3\", \"color\": \"green\"},\n", "    {\"a\": -1, \"b\": 0, \"c\": 4, \"label\": \"k(x) = -x² + 4\", \"color\": \"orange\"}\n", "]\n", "\n", "# Create the plot\n", "plt.figure(figsize=(12, 10))\n", "plt.grid(True, alpha=0.3)\n", "\n", "for func in functions:\n", "    y = quadratic_function(x, func[\"a\"], func[\"b\"], func[\"c\"])\n", "    plt.plot(x, y, label=func[\"label\"], color=func[\"color\"], linewidth=2)\n", "    \n", "    # <PERSON> vertex\n", "    x_v, y_v = find_vertex(func[\"a\"], func[\"b\"], func[\"c\"])\n", "    plt.plot(x_v, y_v, 'o', color=func[\"color\"], markersize=8, \n", "             markerfacecolor='white', markeredgewidth=2)\n", "\n", "plt.xlabel(\"x\", fontsize=12)\n", "plt.ylabel(\"f(x)\", fontsize=12)\n", "plt.title(\"Quadratic Functions Comparison\", fontsize=14, fontweight='bold')\n", "plt.legend(fontsize=10)\n", "plt.axhline(y=0, color='black', linewidth=0.5)\n", "plt.axvline(x=0, color='black', linewidth=0.5)\n", "plt.xlim(-5, 5)\n", "plt.ylim(-5, 10)\n", "plt.show()\n", "\n", "# Analyze quadratic functions\n", "def analyze_quadratic(a, b, c):\n", "    \"\"\"Analyze properties of a quadratic function\"\"\"\n", "    print(f\"Function: f(x) = {a}x² + {b}x + {c}\")\n", "    \n", "    # Vertex\n", "    x_v, y_v = find_vertex(a, b, c)\n", "    print(f\"Vertex: ({x_v:.3f}, {y_v:.3f})\")\n", "    \n", "    # Direction\n", "    if a > 0:\n", "        print(\"Opens upward (has minimum)\")\n", "    else:\n", "        print(\"Opens downward (has maximum)\")\n", "    \n", "    # Axis of symmetry\n", "    print(f\"Axis of symmetry: x = {x_v:.3f}\")\n", "    \n", "    # Y-intercept\n", "    print(f\"Y-intercept: (0, {c})\")\n", "    \n", "    # Roots\n", "    root1, root2, root_type = find_roots(a, b, c)\n", "    if root_type == \"real\":\n", "        print(f\"Roots: x₁ = {root1:.3f}, x₂ = {root2:.3f}\")\n", "    else:\n", "        print(f\"Complex roots: x₁ = {root1}, x₂ = {root2}\")\n", "    \n", "    print(\"-\" * 40)\n", "\n", "# Analyze our example functions\n", "for func in functions:\n", "    analyze_quadratic(func[\"a\"], func[\"b\"], func[\"c\"])"]}, {"cell_type": "code", "execution_count": 3, "id": "77004bcb", "metadata": {}, "outputs": [{"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 1200x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Function: f(x) = 1 × 2^x\n", "Base: 2\n", "Type: Exponential growth\n", "Y-intercept: (0, 1)\n", "Horizontal asymptote: y = 0\n", "----------------------------------------\n", "Function: f(x) = 1 × 0.5^x\n", "Base: 0.5\n", "Type: Exponential decay\n", "Y-intercept: (0, 1)\n", "Horizontal asymptote: y = 0\n", "----------------------------------------\n", "Function: f(x) = 1 × e^(1x)\n", "Growth/decay rate: 1\n", "Type: Exponential growth\n", "Doubling time: 0.693\n", "Y-intercept: (0, 1)\n", "Horizontal asymptote: y = 0\n", "----------------------------------------\n", "Function: f(x) = 2 × e^(-0.5x)\n", "Growth/decay rate: -0.5\n", "Type: Exponential decay\n", "Half-life: 1.386\n", "Y-intercept: (0, 2)\n", "Horizontal asymptote: y = 0\n", "----------------------------------------\n", "APPLICATION: Population Growth Model\n", "P(t) = P₀ × e^(rt)\n", "Where:\n", "  P(t) = population at time t\n", "  P₀ = initial population\n", "  r = growth rate\n", "  t = time\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["After 10 years: 1649\n", "Doubling time: 13.9 years\n", "--------------------------------------------------\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Define exponential function\n", "def exponential_function(x, a, b):\n", "    \"\"\"\n", "    Exponential function: f(x) = a * b^x\n", "    \n", "    Parameters:\n", "    x: input values\n", "    a: coefficient\n", "    b: base\n", "    \"\"\"\n", "    return a * (b ** x)\n", "\n", "# Natural exponential function\n", "def natural_exponential(x, a, k):\n", "    \"\"\"\n", "    Natural exponential: f(x) = a * e^(kx)\n", "    \n", "    Parameters:\n", "    x: input values\n", "    a: coefficient\n", "    k: growth/decay rate\n", "    \"\"\"\n", "    return a * np.exp(k * x)\n", "\n", "# Calculate doubling time and half-life\n", "def doubling_time(k):\n", "    \"\"\"Calculate doubling time for exponential growth\"\"\"\n", "    return np.log(2) / k\n", "\n", "def half_life(k):\n", "    \"\"\"Calculate half-life for exponential decay\"\"\"\n", "    return np.log(2) / abs(k)\n", "\n", "# Create x values\n", "x = np.linspace(-3, 3, 100)\n", "\n", "# Define different exponential functions\n", "functions = [\n", "    {\"type\": \"exp\", \"a\": 1, \"b\": 2, \"label\": \"f(x) = 2^x\", \"color\": \"blue\"},\n", "    {\"type\": \"exp\", \"a\": 1, \"b\": 0.5, \"label\": \"g(x) = (0.5)^x\", \"color\": \"red\"},\n", "    {\"type\": \"nat\", \"a\": 1, \"k\": 1, \"label\": \"h(x) = e^x\", \"color\": \"green\"},\n", "    {\"type\": \"nat\", \"a\": 2, \"k\": -0.5, \"label\": \"k(x) = 2e^(-0.5x)\", \"color\": \"orange\"}\n", "]\n", "\n", "# Create the plot\n", "plt.figure(figsize=(12, 10))\n", "plt.grid(True, alpha=0.3)\n", "\n", "for func in functions:\n", "    if func[\"type\"] == \"exp\":\n", "        y = exponential_function(x, func[\"a\"], func[\"b\"])\n", "    else:  # natural exponential\n", "        y = natural_exponential(x, func[\"a\"], func[\"k\"])\n", "    \n", "    plt.plot(x, y, label=func[\"label\"], color=func[\"color\"], linewidth=2)\n", "\n", "plt.xlabel(\"x\", fontsize=12)\n", "plt.ylabel(\"f(x)\", fontsize=12)\n", "plt.title(\"Exponential Functions Comparison\", fontsize=14, fontweight='bold')\n", "plt.legend(fontsize=10)\n", "plt.axhline(y=0, color='black', linewidth=0.5)\n", "plt.axvline(x=0, color='black', linewidth=0.5)\n", "plt.axhline(y=1, color='gray', linewidth=0.5, linestyle='--', alpha=0.7)\n", "plt.xlim(-3, 3)\n", "plt.ylim(0, 8)\n", "plt.show()\n", "\n", "# Analyze exponential functions\n", "def analyze_exponential(a, b=None, k=None, func_type=\"exp\"):\n", "    \"\"\"Analyze properties of exponential functions\"\"\"\n", "    if func_type == \"exp\":\n", "        print(f\"Function: f(x) = {a} × {b}^x\")\n", "        print(f\"Base: {b}\")\n", "        if b > 1:\n", "            print(\"Type: Exponential growth\")\n", "        elif 0 < b < 1:\n", "            print(\"Type: Exponential decay\")\n", "    else:  # natural exponential\n", "        print(f\"Function: f(x) = {a} × e^({k}x)\")\n", "        print(f\"Growth/decay rate: {k}\")\n", "        if k > 0:\n", "            print(\"Type: Exponential growth\")\n", "            print(f\"Doubling time: {doubling_time(k):.3f}\")\n", "        elif k < 0:\n", "            print(\"Type: Exponential decay\")\n", "            print(f\"Half-life: {half_life(k):.3f}\")\n", "    \n", "    print(f\"Y-intercept: (0, {a})\")\n", "    print(f\"Horizontal asymptote: y = 0\")\n", "    print(\"-\" * 40)\n", "\n", "# Applications: Population growth model\n", "def population_model():\n", "    \"\"\"Example: Population growth model\"\"\"\n", "    print(\"APPLICATION: Population Growth Model\")\n", "    print(\"P(t) = P₀ × e^(rt)\")\n", "    print(\"Where:\")\n", "    print(\"  P(t) = population at time t\")\n", "    print(\"  P₀ = initial population\")\n", "    print(\"  r = growth rate\")\n", "    print(\"  t = time\")\n", "    \n", "    # Example parameters\n", "    P0 = 1000  # initial population\n", "    r = 0.05   # 5% growth rate\n", "    t = np.linspace(0, 20, 100)\n", "    \n", "    P = P0 * np.exp(r * t)\n", "    \n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(t, P, 'b-', linewidth=2, label=f'P(t) = {P0}e^({r}t)')\n", "    plt.xlabel('Time (years)')\n", "    plt.ylabel('Population')\n", "    plt.title('Population Growth Model')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.legend()\n", "    plt.show()\n", "    \n", "    print(f\"After 10 years: {P0 * np.exp(r * 10):.0f}\")\n", "    print(f\"Doubling time: {doubling_time(r):.1f} years\")\n", "    print(\"-\" * 50)\n", "\n", "# Analyze our example functions\n", "for func in functions:\n", "    if func[\"type\"] == \"exp\":\n", "        analyze_exponential(func[\"a\"], b=func[\"b\"], func_type=\"exp\")\n", "    else:\n", "        analyze_exponential(func[\"a\"], k=func[\"k\"], func_type=\"nat\")\n", "\n", "# Show application\n", "population_model()"]}, {"cell_type": "code", "execution_count": 4, "id": "b88f7f58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "FUNCTION ANALYSIS: LINEAR\n", "Parameters: (2, -1)\n", "==================================================\n", "Slope: 2.000\n", "Y Intercept: -1.000\n", "X Intercept: 0.500\n", "Domain: (-∞, ∞)\n", "Range: (-∞, ∞)\n", "Increasing: 1.000\n", "\n", "==================================================\n", "FUNCTION ANALYSIS: QUADRATIC\n", "Parameters: (1, -2, 1)\n", "==================================================\n", "Vertex: (1.0, 0.0)\n", "Axis Of Symmetry: 1.000\n", "Y Intercept: 1.000\n", "Discriminant: 0.000\n", "Opens Upward: 1.000\n", "Domain: (-∞, ∞)\n", "Range: [0.0, ∞)\n", "\n", "==================================================\n", "FUNCTION ANALYSIS: EXPONENTIAL\n", "Parameters: (1, 0.5)\n", "==================================================\n", "Coefficient: 1.000\n", "Growth Rate: 0.500\n", "Y Intercept: 1.000\n", "Horizontal Asymptote: 0.000\n", "Domain: (-∞, ∞)\n", "Range: (0, ∞)\n", "Exponential Growth: 1.000\n", "Doubling Time: 1.386\n", "Half Life: None\n", "\n", "==================================================\n", "FUNCTION ANALYSIS: LOGARITHMIC\n", "Parameters: (2, 1)\n", "==================================================\n", "Coefficient: 2.000\n", "Vertical Shift: 1.000\n", "X Intercept: 0.607\n", "Vertical Asymptote: 0.000\n", "Domain: (0, ∞)\n", "Range: (-∞, ∞)\n", "Increasing: 1.000\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.optimize import fsolve\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "class FunctionAnalyzer:\n", "    \"\"\"Comprehensive function analysis tool\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.x_range = np.linspace(-5, 5, 1000)\n", "    \n", "    def linear(self, x, m, b):\n", "        \"\"\"Linear function: f(x) = mx + b\"\"\"\n", "        return m * x + b\n", "    \n", "    def quadratic(self, x, a, b, c):\n", "        \"\"\"Quadratic function: f(x) = ax² + bx + c\"\"\"\n", "        return a * x**2 + b * x + c\n", "    \n", "    def exponential(self, x, a, k):\n", "        \"\"\"Exponential function: f(x) = ae^(kx)\"\"\"\n", "        return a * np.exp(k * x)\n", "    \n", "    def logarithmic(self, x, a, b):\n", "        \"\"\"Logarithmic function: f(x) = a*ln(x) + b\"\"\"\n", "        return a * np.log(np.abs(x) + 1e-10) + b  # Avoid log(0)\n", "    \n", "    def analyze_function(self, func_type, params, x_range=None):\n", "        \"\"\"Analyze a function and return key properties\"\"\"\n", "        if x_range is None:\n", "            x_range = self.x_range\n", "        \n", "        analysis = {\"type\": func_type, \"parameters\": params}\n", "        \n", "        if func_type == \"linear\":\n", "            m, b = params\n", "            y = self.linear(x_range, m, b)\n", "            analysis.update({\n", "                \"slope\": m,\n", "                \"y_intercept\": b,\n", "                \"x_intercept\": -b/m if m != 0 else None,\n", "                \"domain\": \"(-∞, ∞)\",\n", "                \"range\": \"(-∞, ∞)\" if m != 0 else f\"{{{b}}}\",\n", "                \"increasing\": m > 0,\n", "                \"function\": lambda x: self.linear(x, m, b)\n", "            })\n", "        \n", "        elif func_type == \"quadratic\":\n", "            a, b, c = params\n", "            y = self.quadratic(x_range, a, b, c)\n", "            vertex_x = -b / (2*a)\n", "            vertex_y = self.quadratic(vertex_x, a, b, c)\n", "            discriminant = b**2 - 4*a*c\n", "            \n", "            analysis.update({\n", "                \"vertex\": (vertex_x, vertex_y),\n", "                \"axis_of_symmetry\": vertex_x,\n", "                \"y_intercept\": c,\n", "                \"discriminant\": discriminant,\n", "                \"opens_upward\": a > 0,\n", "                \"domain\": \"(-∞, ∞)\",\n", "                \"range\": f\"[{vertex_y}, ∞)\" if a > 0 else f\"(-∞, {vertex_y}]\",\n", "                \"function\": lambda x: self.quadratic(x, a, b, c)\n", "            })\n", "        \n", "        elif func_type == \"exponential\":\n", "            a, k = params\n", "            x_pos = x_range[x_range >= -10]  # Avoid overflow\n", "            y = self.exponential(x_pos, a, k)\n", "            \n", "            analysis.update({\n", "                \"coefficient\": a,\n", "                \"growth_rate\": k,\n", "                \"y_intercept\": a,\n", "                \"horizontal_asymptote\": 0,\n", "                \"domain\": \"(-∞, ∞)\",\n", "                \"range\": \"(0, ∞)\" if a > 0 else \"(-∞, 0)\",\n", "                \"exponential_growth\": k > 0,\n", "                \"doubling_time\": np.log(2)/k if k > 0 else None,\n", "                \"half_life\": np.log(2)/abs(k) if k < 0 else None,\n", "                \"function\": lambda x: self.exponential(x, a, k)\n", "            })\n", "        \n", "        elif func_type == \"logarithmic\":\n", "            a, b = params\n", "            x_pos = x_range[x_range > 0]\n", "            y = self.logarithmic(x_pos, a, b)\n", "            \n", "            analysis.update({\n", "                \"coefficient\": a,\n", "                \"vertical_shift\": b,\n", "                \"x_intercept\": np.exp(-b/a) if a != 0 else None,\n", "                \"vertical_asymptote\": 0,\n", "                \"domain\": \"(0, ∞)\",\n", "                \"range\": \"(-∞, ∞)\",\n", "                \"increasing\": a > 0,\n", "                \"function\": lambda x: self.logarithmic(x, a, b)\n", "            })\n", "        \n", "        return analysis\n", "    \n", "    def plot_comparison(self, functions_list):\n", "        \"\"\"Plot multiple functions for comparison\"\"\"\n", "        plt.figure(figsize=(14, 10))\n", "        colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']\n", "        \n", "        for i, (func_type, params, label) in enumerate(functions_list):\n", "            color = colors[i % len(colors)]\n", "            \n", "            if func_type == \"linear\":\n", "                y = self.linear(self.x_range, *params)\n", "                plt.plot(self.x_range, y, color=color, linewidth=2, label=label)\n", "            \n", "            elif func_type == \"quadratic\":\n", "                y = self.quadratic(self.x_range, *params)\n", "                plt.plot(self.x_range, y, color=color, linewidth=2, label=label)\n", "            \n", "            elif func_type == \"exponential\":\n", "                x_range = np.linspace(-3, 3, 1000)\n", "                y = self.exponential(x_range, *params)\n", "                # Clip extreme values for better visualization\n", "                y = np.clip(y, -50, 50)\n", "                plt.plot(x_range, y, color=color, linewidth=2, label=label)\n", "            \n", "            elif func_type == \"logarithmic\":\n", "                x_range = np.linspace(0.1, 5, 1000)\n", "                y = self.logarithmic(x_range, *params)\n", "                plt.plot(x_range, y, color=color, linewidth=2, label=label)\n", "        \n", "        plt.grid(True, alpha=0.3)\n", "        plt.axhline(y=0, color='black', linewidth=0.5)\n", "        plt.axvline(x=0, color='black', linewidth=0.5)\n", "        plt.xlabel('x', fontsize=12)\n", "        plt.ylabel('f(x)', fontsize=12)\n", "        plt.title('Function Types Comparison', fontsize=14, fontweight='bold')\n", "        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    def print_analysis(self, analysis):\n", "        \"\"\"Print formatted analysis results\"\"\"\n", "        print(f\"\\n{'='*50}\")\n", "        print(f\"FUNCTION ANALYSIS: {analysis['type'].upper()}\")\n", "        print(f\"Parameters: {analysis['parameters']}\")\n", "        print(f\"{'='*50}\")\n", "        \n", "        for key, value in analysis.items():\n", "            if key not in ['type', 'parameters', 'function']:\n", "                if isinstance(value, tuple):\n", "                    print(f\"{key.replace('_', ' ').title()}: {value}\")\n", "                elif isinstance(value, (int, float)):\n", "                    print(f\"{key.replace('_', ' ').title()}: {value:.3f}\")\n", "                else:\n", "                    print(f\"{key.replace('_', ' ').title()}: {value}\")\n", "\n", "# Usage example\n", "analyzer = FunctionAnalyzer()\n", "\n", "# Define functions to analyze\n", "functions_to_analyze = [\n", "    (\"linear\", (2, -1), \"f(x) = 2x - 1\"),\n", "    (\"quadratic\", (1, -2, 1), \"g(x) = x² - 2x + 1\"),\n", "    (\"exponential\", (1, 0.5), \"h(x) = e^(0.5x)\"),\n", "    (\"logarithmic\", (2, 1), \"k(x) = 2ln(x) + 1\")\n", "]\n", "\n", "# Analyze each function\n", "for func_type, params, label in functions_to_analyze:\n", "    analysis = analyzer.analyze_function(func_type, params)\n", "    analyzer.print_analysis(analysis)\n", "\n", "# Create comparison plot\n", "analyzer.plot_comparison(functions_to_analyze)\n", "\n", "# Interactive function explorer\n", "def explore_function():\n", "    \"\"\"Interactive function exploration\"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"INTERACTIVE FUNCTION EXPLORER\")\n", "    print(\"=\"*60)\n", "    \n", "    while True:\n", "        print(\"\\nChoose function type:\")\n", "        print(\"1. Linear (mx + b)\")\n", "        print(\"2. Quadratic (ax² + bx + c)\")\n", "        print(\"3. Exponential (ae^(kx))\")\n", "        print(\"4. Logarithmic (a*ln(x) + b)\")\n", "        print(\"5. Exit\")\n", "        \n", "        choice = input(\"\\nEnter choice (1-5): \")\n", "        \n", "        if choice == '5':\n", "            break\n", "        \n", "        try:\n", "            if choice == '1':\n", "                m = float(input(\"Enter slope (m): \"))\n", "                b = float(input(\"Enter y-intercept (b): \"))\n", "                analysis = analyzer.analyze_function(\"linear\", (m, b))\n", "            \n", "            elif choice == '2':\n", "                a = float(input(\"Enter coefficient of x² (a): \"))\n", "                b = float(input(\"Enter coefficient of x (b): \"))\n", "                c = float(input(\"Enter constant term (c): \"))\n", "                analysis = analyzer.analyze_function(\"quadratic\", (a, b, c))\n", "            \n", "            elif choice == '3':\n", "                a = float(input(\"Enter coefficient (a): \"))\n", "                k = float(input(\"Enter growth rate (k): \"))\n", "                analysis = analyzer.analyze_function(\"exponential\", (a, k))\n", "            \n", "            elif choice == '4':\n", "                a = float(input(\"Enter coefficient (a): \"))\n", "                b = float(input(\"Enter vertical shift (b): \"))\n", "                analysis = analyzer.analyze_function(\"logarithmic\", (a, b))\n", "            \n", "            else:\n", "                print(\"Invalid choice!\")\n", "                continue\n", "            \n", "            analyzer.print_analysis(analysis)\n", "            \n", "        except ValueError:\n", "            print(\"Please enter valid numbers!\")\n", "        except Exception as e:\n", "            print(f\"Error: {e}\")\n", "\n", "# Uncomment to run interactive explorer\n", "# explore_function()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}