---
output:
  word_document: default
  html_document: default
---
<div class="max-w-6xl mx-auto p-6 bg-white shadow-lg">

<div class="text-center mb-8 border-b-2 border-gray-200 pb-6">

#  Comprehensive ODE Tutorial

## Part 1: Mathematical Foundations

### Chapter 1: Review of Algebra and Functions

<div class="flex justify-center items-center mt-4 text-sm text-gray-500">

Educational Content <span class="mx-3">•</span> Python & R Integration <span class="mx-3">•</span> Interactive Visualizations

</div>

</div>

### Chapter Contents

<div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">

<a href="#introduction" class="text-blue-600 hover:text-blue-800 transition">1.1 Introduction</a> <a href="#linear-functions" class="text-blue-600 hover:text-blue-800 transition">1.2 Linear Functions</a> <a href="#quadratic-functions" class="text-blue-600 hover:text-blue-800 transition">1.3 Quadratic Functions</a> <a href="#exponential-functions" class="text-blue-600 hover:text-blue-800 transition">1.4 Exponential Functions</a> <a href="#logarithmic-functions" class="text-blue-600 hover:text-blue-800 transition">1.5 Logarithmic Functions</a> <a href="#function-behavior" class="text-blue-600 hover:text-blue-800 transition">1.6 Function Behavior Analysis</a> <a href="#programming-tools" class="text-blue-600 hover:text-blue-800 transition">1.7 Programming Tools</a> <a href="#exercises" class="text-blue-600 hover:text-blue-800 transition">1.8 Exercises</a>

</div>

<div id="introduction" class="section mb-12">

##  1.1 Introduction

<div class="definition-box bg-green-50 p-6 rounded-lg mb-6">

### Why Review Algebra and Functions?

Before diving into differential equations, we must establish a solid foundation in algebraic functions. Differential equations describe how quantities change, and understanding the basic function types—linear, quadratic, exponential, and logarithmic—is crucial because:

- Many differential equations have solutions that are combinations of these function types
- Modeling real-world phenomena often begins with recognizing these fundamental patterns
- Graphical interpretation helps us understand solution behavior
- Programming implementations require solid mathematical understanding

</div>

<div class="bg-gray-50 p-6 rounded-lg">

### Learning Objectives

By the end of this chapter, you will be able to:

<div class="grid grid-cols-1 md:grid-cols-2 gap-4">

- Identify and work with linear, quadratic, exponential, and logarithmic functions
- Graph and interpret function behavior
- Analyze domain, range, and key characteristics

<!-- -->

- Implement functions in Python and R
- Create visualizations for function analysis
- Apply these concepts to mathematical modeling

</div>

</div>

</div>

<div id="linear-functions" class="section section-break">

##  1.2 Linear Functions

<div class="definition-box bg-green-50 p-6 rounded-lg mb-6">

### Definition

A **linear function** is a function of the form:

<div class="math-display text-center text-lg">

$$f(x) = mx + b$$

</div>

where $m$ is the **slope** and $b$ is the **y-intercept**.

</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">

<div class="bg-white border rounded-lg p-6">

### Key Properties

- **Slope ($m$):** Rate of change, $m = \frac{\Delta y}{\Delta x}$
- **Y-intercept ($b$):** Value when $x = 0$
- **Domain:** All real numbers $(-\infty, \infty)$
- **Range:** All real numbers $(-\infty, \infty)$
- **Graph:** Straight line

</div>

<div class="bg-white border rounded-lg p-6">

### Special Cases

- **$m > 0$:** Increasing function
- **$m < 0$:** Decreasing function
- **$m = 0$:** Constant function $f(x) = b$
- **$b = 0$:** Function passes through origin

</div>

</div>

<div class="example-box bg-blue-50 p-6 rounded-lg mb-6">

### Example 1.1: Linear Function Analysis

Consider the function $f(x) = 2x - 3$

<div class="grid grid-cols-1 md:grid-cols-2 gap-4">

- **Slope:** $m = 2$ (increasing)
- **Y-intercept:** $b = -3$
- **X-intercept:** Solve $2x - 3 = 0 \Rightarrow x = \frac{3}{2}$

<!-- -->

- **At $x = 0$:** $f(0) = -3$
- **At $x = 2$:** $f(2) = 1$
- **Rate of change:** 2 units up per 1 unit right

</div>

</div>

<div class="bg-white border rounded-lg p-6 mb-6">

### Linear Functions Visualization

<div class="chart-container">

</div>

</div>

<div class="bg-gray-900 rounded-lg p-6 mb-6">

### Python Implementation

``` language-python
import numpy as np
import matplotlib.pyplot as plt

# Define linear function
def linear_function(x, m, b):
    """
    Linear function: f(x) = mx + b
    
    Parameters:
    x: input values
    m: slope
    b: y-intercept
    """
    return m * x + b

# Create x values
x = np.linspace(-5, 5, 100)

# Define different linear functions
functions = [
    {"m": 2, "b": -3, "label": "f(x) = 2x - 3", "color": "blue"},
    {"m": -1, "b": 2, "label": "g(x) = -x + 2", "color": "red"},
    {"m": 0.5, "b": 1, "label": "h(x) = 0.5x + 1", "color": "green"},
    {"m": 0, "b": -1, "label": "k(x) = -1", "color": "orange"}
]

# Create the plot
plt.figure(figsize=(10, 8))
plt.grid(True, alpha=0.3)

for func in functions:
    y = linear_function(x, func["m"], func["b"])
    plt.plot(x, y, label=func["label"], color=func["color"], linewidth=2)

plt.xlabel("x", fontsize=12)
plt.ylabel("f(x)", fontsize=12)
plt.title("Linear Functions Comparison", fontsize=14, fontweight='bold')
plt.legend(fontsize=10)
plt.axhline(y=0, color='black', linewidth=0.5)
plt.axvline(x=0, color='black', linewidth=0.5)
plt.xlim(-5, 5)
plt.ylim(-8, 8)
plt.show()

# Calculate key properties
def analyze_linear_function(m, b):
    """Analyze properties of a linear function"""
    print(f"Function: f(x) = {m}x + {b}")
    print(f"Slope: {m}")
    print(f"Y-intercept: {b}")
    
    if m != 0:
        x_intercept = -b / m
        print(f"X-intercept: {x_intercept:.3f}")
    else:
        print("No x-intercept (horizontal line)")
    
    if m > 0:
        print("Function is increasing")
    elif m < 0:
        print("Function is decreasing")
    else:
        print("Function is constant")
    print("-" * 30)

# Analyze our example functions
for func in functions:
    analyze_linear_function(func["m"], func["b"])
```

</div>

<div class="bg-blue-900 rounded-lg p-6 mb-6">

### R Implementation

``` language-r
# Linear Functions in R
library(ggplot2)
library(dplyr)

# Define linear function
linear_function <- function(x, m, b) {
  return(m * x + b)
}

# Create x values
x <- seq(-5, 5, length.out = 100)

# Define different linear functions
functions_data <- data.frame(
  x = rep(x, 4),
  y = c(
    linear_function(x, 2, -3),    # f(x) = 2x - 3
    linear_function(x, -1, 2),    # g(x) = -x + 2
    linear_function(x, 0.5, 1),   # h(x) = 0.5x + 1
    linear_function(x, 0, -1)     # k(x) = -1
  ),
  Function = rep(c("f(x) = 2x - 3", "g(x) = -x + 2", 
                   "h(x) = 0.5x + 1", "k(x) = -1"), each = 100)
)

# Create the plot
ggplot(functions_data, aes(x = x, y = y, color = Function)) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = 0, color = "black", size = 0.5, alpha = 0.7) +
  geom_vline(xintercept = 0, color = "black", size = 0.5, alpha = 0.7) +
  labs(
    title = "Linear Functions Comparison",
    x = "x",
    y = "f(x)",
    color = "Function"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    legend.position = "bottom"
  ) +
  scale_color_manual(values = c("blue", "red", "green", "orange")) +
  xlim(-5, 5) +
  ylim(-8, 8)

# Function to analyze linear function properties
analyze_linear <- function(m, b) {
  cat("Function: f(x) =", m, "x +", b, "\n")
  cat("Slope:", m, "\n")
  cat("Y-intercept:", b, "\n")
  
  if (m != 0) {
    x_intercept <- -b / m
    cat("X-intercept:", round(x_intercept, 3), "\n")
  } else {
    cat("No x-intercept (horizontal line)\n")
  }
  
  if (m > 0) {
    cat("Function is increasing\n")
  } else if (m < 0) {
    cat("Function is decreasing\n")
  } else {
    cat("Function is constant\n")
  }
  cat(rep("-", 30), "\n")
}

# Analyze example functions
analyze_linear(2, -3)
analyze_linear(-1, 2)
analyze_linear(0.5, 1)
analyze_linear(0, -1)
```

</div>

</div>

<div id="quadratic-functions" class="section section-break">

##  1.3 Quadratic Functions

<div class="definition-box bg-green-50 p-6 rounded-lg mb-6">

### Definition

A **quadratic function** is a function of the form:

<div class="math-display text-center text-lg">

$$f(x) = ax^2 + bx + c$$

</div>

where $a \neq 0$, and $a$, $b$, $c$ are constants.

</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">

<div class="bg-white border rounded-lg p-6">

### Key Properties

- **Graph:** Parabola
- **Vertex:** $\left(-\frac{b}{2a}, f\left(-\frac{b}{2a}\right)\right)$
- **Axis of symmetry:** $x = -\frac{b}{2a}$
- **Y-intercept:** $(0, c)$
- **Domain:** All real numbers

</div>

<div class="bg-white border rounded-lg p-6">

### Shape and Direction

- **$a > 0$:** Opens upward (minimum at vertex)
- **$a < 0$:** Opens downward (maximum at vertex)
- **$|a|$ larger:** Narrower parabola
- **$|a|$ smaller:** Wider parabola

</div>

</div>

<div class="bg-yellow-50 p-6 rounded-lg mb-6">

### Finding Roots (X-intercepts)

The roots of a quadratic equation $ax^2 + bx + c = 0$ are found using the quadratic formula:

<div class="math-display text-center text-lg">

$$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$$

</div>

The discriminant $\Delta = b^2 - 4ac$ determines the nature of roots:

- $\Delta > 0$: Two distinct real roots
- $\Delta = 0$: One repeated real root
- $\Delta < 0$: Two complex conjugate roots

</div>

<div class="example-box bg-blue-50 p-6 rounded-lg mb-6">

### Example 1.2: Quadratic Function Analysis

Consider the function $f(x) = -2x^2 + 4x + 1$

<div class="grid grid-cols-1 md:grid-cols-2 gap-4">

<div>

Coefficients:

- $a = -2$ (opens downward)
- $b = 4$
- $c = 1$

</div>

<div>

Key Features:

- Vertex: $x = -\frac{4}{2(-2)} = 1$
- $f(1) = -2(1)^2 + 4(1) + 1 = 3$
- Vertex: $(1, 3)$ (maximum)

</div>

</div>

</div>

<div class="bg-white border rounded-lg p-6 mb-6">

### Quadratic Functions Visualization

<div class="chart-container">

</div>

</div>

<div class="bg-gray-900 rounded-lg p-6 mb-6">

### Python Implementation

``` language-python
import numpy as np
import matplotlib.pyplot as plt
import cmath

# Define quadratic function
def quadratic_function(x, a, b, c):
    """
    Quadratic function: f(x) = ax² + bx + c
    
    Parameters:
    x: input values
    a, b, c: coefficients
    """
    return a * x**2 + b * x + c

# Function to find vertex
def find_vertex(a, b, c):
    """Find vertex of parabola"""
    x_vertex = -b / (2 * a)
    y_vertex = quadratic_function(x_vertex, a, b, c)
    return x_vertex, y_vertex

# Function to find roots using quadratic formula
def find_roots(a, b, c):
    """Find roots of quadratic equation"""
    discriminant = b**2 - 4*a*c
    
    if discriminant >= 0:
        root1 = (-b + np.sqrt(discriminant)) / (2*a)
        root2 = (-b - np.sqrt(discriminant)) / (2*a)
        return root1, root2, "real"
    else:
        root1 = (-b + cmath.sqrt(discriminant)) / (2*a)
        root2 = (-b - cmath.sqrt(discriminant)) / (2*a)
        return root1, root2, "complex"

# Create x values
x = np.linspace(-5, 5, 100)

# Define different quadratic functions
functions = [
    {"a": 1, "b": 0, "c": 0, "label": "f(x) = x²", "color": "blue"},
    {"a": -2, "b": 4, "c": 1, "label": "g(x) = -2x² + 4x + 1", "color": "red"},
    {"a": 0.5, "b": -2, "c": 3, "label": "h(x) = 0.5x² - 2x + 3", "color": "green"},
    {"a": -1, "b": 0, "c": 4, "label": "k(x) = -x² + 4", "color": "orange"}
]

# Create the plot
plt.figure(figsize=(12, 10))
plt.grid(True, alpha=0.3)

for func in functions:
    y = quadratic_function(x, func["a"], func["b"], func["c"])
    plt.plot(x, y, label=func["label"], color=func["color"], linewidth=2)
    
    # Mark vertex
    x_v, y_v = find_vertex(func["a"], func["b"], func["c"])
    plt.plot(x_v, y_v, 'o', color=func["color"], markersize=8, 
             markerfacecolor='white', markeredgewidth=2)

plt.xlabel("x", fontsize=12)
plt.ylabel("f(x)", fontsize=12)
plt.title("Quadratic Functions Comparison", fontsize=14, fontweight='bold')
plt.legend(fontsize=10)
plt.axhline(y=0, color='black', linewidth=0.5)
plt.axvline(x=0, color='black', linewidth=0.5)
plt.xlim(-5, 5)
plt.ylim(-5, 10)
plt.show()

# Analyze quadratic functions
def analyze_quadratic(a, b, c):
    """Analyze properties of a quadratic function"""
    print(f"Function: f(x) = {a}x² + {b}x + {c}")
    
    # Vertex
    x_v, y_v = find_vertex(a, b, c)
    print(f"Vertex: ({x_v:.3f}, {y_v:.3f})")
    
    # Direction
    if a > 0:
        print("Opens upward (has minimum)")
    else:
        print("Opens downward (has maximum)")
    
    # Axis of symmetry
    print(f"Axis of symmetry: x = {x_v:.3f}")
    
    # Y-intercept
    print(f"Y-intercept: (0, {c})")
    
    # Roots
    root1, root2, root_type = find_roots(a, b, c)
    if root_type == "real":
        print(f"Roots: x₁ = {root1:.3f}, x₂ = {root2:.3f}")
    else:
        print(f"Complex roots: x₁ = {root1}, x₂ = {root2}")
    
    print("-" * 40)

# Analyze our example functions
for func in functions:
    analyze_quadratic(func["a"], func["b"], func["c"])
```

</div>

<div class="bg-blue-900 rounded-lg p-6 mb-6">

### R Implementation

``` language-r
# Quadratic Functions in R
library(ggplot2)
library(dplyr)

# Define quadratic function
quadratic_function <- function(x, a, b, c) {
  return(a * x^2 + b * x + c)
}

# Function to find vertex
find_vertex <- function(a, b, c) {
  x_vertex <- -b / (2 * a)
  y_vertex <- quadratic_function(x_vertex, a, b, c)
  return(c(x_vertex, y_vertex))
}

# Function to find roots
find_roots <- function(a, b, c) {
  discriminant <- b^2 - 4*a*c
  
  if (discriminant >= 0) {
    root1 <- (-b + sqrt(discriminant)) / (2*a)
    root2 <- (-b - sqrt(discriminant)) / (2*a)
    return(list(roots = c(root1, root2), type = "real"))
  } else {
    root1 <- complex(real = -b/(2*a), imaginary = sqrt(-discriminant)/(2*a))
    root2 <- complex(real = -b/(2*a), imaginary = -sqrt(-discriminant)/(2*a))
    return(list(roots = c(root1, root2), type = "complex"))
  }
}

# Create x values
x <- seq(-5, 5, length.out = 100)

# Define different quadratic functions
functions_list <- list(
  list(a = 1, b = 0, c = 0, label = "f(x) = x²", color = "blue"),
  list(a = -2, b = 4, c = 1, label = "g(x) = -2x² + 4x + 1", color = "red"),
  list(a = 0.5, b = -2, c = 3, label = "h(x) = 0.5x² - 2x + 3", color = "green"),
  list(a = -1, b = 0, c = 4, label = "k(x) = -x² + 4", color = "orange")
)

# Create data for plotting
plot_data <- data.frame()
vertex_data <- data.frame()

for (i in 1:length(functions_list)) {
  func <- functions_list[[i]]
  y <- quadratic_function(x, func$a, func$b, func$c)
  
  temp_data <- data.frame(
    x = x,
    y = y,
    Function = func$label,
    Color = func$color
  )
  plot_data <- rbind(plot_data, temp_data)
  
  # Add vertex data
  vertex <- find_vertex(func$a, func$b, func$c)
  vertex_temp <- data.frame(
    x = vertex[1],
    y = vertex[2],
    Function = func$label,
    Color = func$color
  )
  vertex_data <- rbind(vertex_data, vertex_temp)
}

# Create the plot
ggplot(plot_data, aes(x = x, y = y, color = Function)) +
  geom_line(size = 1.2) +
  geom_point(data = vertex_data, aes(x = x, y = y, color = Function), 
             size = 3, shape = 21, fill = "white", stroke = 2) +
  geom_hline(yintercept = 0, color = "black", size = 0.5, alpha = 0.7) +
  geom_vline(xintercept = 0, color = "black", size = 0.5, alpha = 0.7) +
  labs(
    title = "Quadratic Functions Comparison",
    subtitle = "Vertices marked with circles",
    x = "x",
    y = "f(x)",
    color = "Function"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    legend.position = "bottom"
  ) +
  scale_color_manual(values = c("blue", "red", "green", "orange")) +
  xlim(-5, 5) +
  ylim(-5, 10)

# Function to analyze quadratic properties
analyze_quadratic <- function(a, b, c) {
  cat("Function: f(x) =", a, "x² +", b, "x +", c, "\n")
  
  # Vertex
  vertex <- find_vertex(a, b, c)
  cat("Vertex: (", round(vertex[1], 3), ",", round(vertex[2], 3), ")\n")
  
  # Direction
  if (a > 0) {
    cat("Opens upward (has minimum)\n")
  } else {
    cat("Opens downward (has maximum)\n")
  }
  
  # Axis of symmetry
  cat("Axis of symmetry: x =", round(vertex[1], 3), "\n")
  
  # Y-intercept
  cat("Y-intercept: (0,", c, ")\n")
  
  # Roots
  roots_info <- find_roots(a, b, c)
  if (roots_info$type == "real") {
    cat("Roots: x₁ =", round(Re(roots_info$roots[1]), 3), 
        ", x₂ =", round(Re(roots_info$roots[2]), 3), "\n")
  } else {
    cat("Complex roots: x₁ =", roots_info$roots[1], 
        ", x₂ =", roots_info$roots[2], "\n")
  }
  
  cat(rep("-", 40), "\n")
}

# Analyze example functions
for (func in functions_list) {
  analyze_quadratic(func$a, func$b, func$c)
}
```

</div>

</div>

<div id="exponential-functions" class="section section-break">

##  1.4 Exponential Functions

<div class="definition-box bg-green-50 p-6 rounded-lg mb-6">

### Definition

An **exponential function** is a function of the form:

<div class="math-display text-center text-lg">

$$f(x) = ab^x$$

</div>

where $a > 0$, $b > 0$, $b \neq 1$. The special case where $b = e$ (Euler's number ≈ 2.718) is called the **natural exponential function**: $f(x) = ae^x$.

</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">

<div class="bg-white border rounded-lg p-6">

### Key Properties

- **Domain:** All real numbers $(-\infty, \infty)$
- **Range:** $(0, \infty)$ if $a > 0$
- **Y-intercept:** $(0, a)$
- **Horizontal asymptote:** $y = 0$
- **Always positive** (if $a > 0$)

</div>

<div class="bg-white border rounded-lg p-6">

### Behavior

- **$b > 1$:** Exponential growth
- **$0 < b < 1$:** Exponential decay
- **Larger $|a|$:** Steeper curve
- **$a < 0$:** Reflection across x-axis

</div>

</div>

<div class="bg-yellow-50 p-6 rounded-lg mb-6">

### Important Exponential Properties

<div class="grid grid-cols-1 md:grid-cols-2 gap-6">

<div>

Exponential Laws:

- $b^x \cdot b^y = b^{x+y}$
- $\frac{b^x}{b^y} = b^{x-y}$
- $(b^x)^y = b^{xy}$
- $b^0 = 1$

</div>

<div>

Special Values:

- $e \approx 2.71828...$
- $e^{\ln x} = x$
- $\ln(e^x) = x$
- $\frac{d}{dx}e^x = e^x$

</div>

</div>

</div>

<div class="example-box bg-blue-50 p-6 rounded-lg mb-6">

### Example 1.3: Exponential Growth and Decay

<div class="grid grid-cols-1 md:grid-cols-2 gap-6">

<div>

Growth: $f(x) = 2e^{0.5x}$

- $a = 2$, base $= e$, rate $= 0.5$
- Y-intercept: $(0, 2)$
- Doubling time: $\ln(2)/0.5 \approx 1.39$

</div>

<div>

Decay: $g(x) = 3e^{-0.2x}$

- $a = 3$, base $= e$, rate $= -0.2$
- Y-intercept: $(0, 3)$
- Half-life: $\ln(2)/0.2 \approx 3.47$

</div>

</div>

</div>

<div class="bg-white border rounded-lg p-6 mb-6">

### Exponential Functions Visualization

<div class="chart-container">

</div>

</div>

<div class="bg-gray-900 rounded-lg p-6 mb-6">

### Python Implementation

``` language-python
import numpy as np
import matplotlib.pyplot as plt

# Define exponential function
def exponential_function(x, a, b):
    """
    Exponential function: f(x) = a * b^x
    
    Parameters:
    x: input values
    a: coefficient
    b: base
    """
    return a * (b ** x)

# Natural exponential function
def natural_exponential(x, a, k):
    """
    Natural exponential: f(x) = a * e^(kx)
    
    Parameters:
    x: input values
    a: coefficient
    k: growth/decay rate
    """
    return a * np.exp(k * x)

# Calculate doubling time and half-life
def doubling_time(k):
    """Calculate doubling time for exponential growth"""
    return np.log(2) / k

def half_life(k):
    """Calculate half-life for exponential decay"""
    return np.log(2) / abs(k)

# Create x values
x = np.linspace(-3, 3, 100)

# Define different exponential functions
functions = [
    {"type": "exp", "a": 1, "b": 2, "label": "f(x) = 2^x", "color": "blue"},
    {"type": "exp", "a": 1, "b": 0.5, "label": "g(x) = (0.5)^x", "color": "red"},
    {"type": "nat", "a": 1, "k": 1, "label": "h(x) = e^x", "color": "green"},
    {"type": "nat", "a": 2, "k": -0.5, "label": "k(x) = 2e^(-0.5x)", "color": "orange"}
]

# Create the plot
plt.figure(figsize=(12, 10))
plt.grid(True, alpha=0.3)

for func in functions:
    if func["type"] == "exp":
        y = exponential_function(x, func["a"], func["b"])
    else:  # natural exponential
        y = natural_exponential(x, func["a"], func["k"])
    
    plt.plot(x, y, label=func["label"], color=func["color"], linewidth=2)

plt.xlabel("x", fontsize=12)
plt.ylabel("f(x)", fontsize=12)
plt.title("Exponential Functions Comparison", fontsize=14, fontweight='bold')
plt.legend(fontsize=10)
plt.axhline(y=0, color='black', linewidth=0.5)
plt.axvline(x=0, color='black', linewidth=0.5)
plt.axhline(y=1, color='gray', linewidth=0.5, linestyle='--', alpha=0.7)
plt.xlim(-3, 3)
plt.ylim(0, 8)
plt.show()

# Analyze exponential functions
def analyze_exponential(a, b=None, k=None, func_type="exp"):
    """Analyze properties of exponential functions"""
    if func_type == "exp":
        print(f"Function: f(x) = {a} × {b}^x")
        print(f"Base: {b}")
        if b > 1:
            print("Type: Exponential growth")
        elif 0 < b < 1:
            print("Type: Exponential decay")
    else:  # natural exponential
        print(f"Function: f(x) = {a} × e^({k}x)")
        print(f"Growth/decay rate: {k}")
        if k > 0:
            print("Type: Exponential growth")
            print(f"Doubling time: {doubling_time(k):.3f}")
        elif k < 0:
            print("Type: Exponential decay")
            print(f"Half-life: {half_life(k):.3f}")
    
    print(f"Y-intercept: (0, {a})")
    print(f"Horizontal asymptote: y = 0")
    print("-" * 40)

# Applications: Population growth model
def population_model():
    """Example: Population growth model"""
    print("APPLICATION: Population Growth Model")
    print("P(t) = P₀ × e^(rt)")
    print("Where:")
    print("  P(t) = population at time t")
    print("  P₀ = initial population")
    print("  r = growth rate")
    print("  t = time")
    
    # Example parameters
    P0 = 1000  # initial population
    r = 0.05   # 5% growth rate
    t = np.linspace(0, 20, 100)
    
    P = P0 * np.exp(r * t)
    
    plt.figure(figsize=(10, 6))
    plt.plot(t, P, 'b-', linewidth=2, label=f'P(t) = {P0}e^({r}t)')
    plt.xlabel('Time (years)')
    plt.ylabel('Population')
    plt.title('Population Growth Model')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.show()
    
    print(f"After 10 years: {P0 * np.exp(r * 10):.0f}")
    print(f"Doubling time: {doubling_time(r):.1f} years")
    print("-" * 50)

# Analyze our example functions
for func in functions:
    if func["type"] == "exp":
        analyze_exponential(func["a"], b=func["b"], func_type="exp")
    else:
        analyze_exponential(func["a"], k=func["k"], func_type="nat")

# Show application
population_model()
```

</div>

</div>

<div id="logarithmic-functions" class="section section-break">

##  1.5 Logarithmic Functions

<div class="definition-box bg-green-50 p-6 rounded-lg mb-6">

### Definition

A **logarithmic function** is the inverse of an exponential function:

<div class="math-display text-center text-lg">

$$f(x) = \log_b(x) \text{ where } b^{f(x)} = x$$

</div>

Common logarithms: $\log_{10}(x) = \log(x)$ and natural logarithms: $\log_e(x) = \ln(x)$

</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">

<div class="bg-white border rounded-lg p-6">

### Key Properties

- **Domain:** $(0, \infty)$
- **Range:** All real numbers $(-\infty, \infty)$
- **X-intercept:** $(1, 0)$
- **Vertical asymptote:** $x = 0$
- **Increasing function** (if $b > 1$)

</div>

<div class="bg-white border rounded-lg p-6">

### Logarithm Laws

- $\log_b(xy) = \log_b(x) + \log_b(y)$
- $\log_b\left(\frac{x}{y}\right) = \log_b(x) - \log_b(y)$
- $\log_b(x^r) = r\log_b(x)$
- $\log_b(1) = 0$, $\log_b(b) = 1$

</div>

</div>

<div class="bg-blue-50 p-6 rounded-lg mb-6">

### Programming Integration

Both Python and R provide comprehensive support for mathematical functions. Here's how to work with the functions we've covered:

<div class="grid grid-cols-1 md:grid-cols-2 gap-6">

<div>

Python Libraries:

- `numpy` - Mathematical functions
- `matplotlib` - Plotting
- `scipy` - Scientific computing
- `sympy` - Symbolic mathematics

</div>

<div>

R Packages:

- `base` - Built-in functions
- `ggplot2` - Advanced plotting
- `dplyr` - Data manipulation
- `pracma` - Practical mathematics

</div>

</div>

</div>

<div class="bg-white border rounded-lg p-6 mb-6">

### All Function Types Comparison

<div class="chart-container">

</div>

</div>

<div class="bg-gray-900 rounded-lg p-6 mb-6">

### Complete Function Analysis Tool (Python)

``` language-python
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import fsolve
import warnings
warnings.filterwarnings('ignore')

class FunctionAnalyzer:
    """Comprehensive function analysis tool"""
    
    def __init__(self):
        self.x_range = np.linspace(-5, 5, 1000)
    
    def linear(self, x, m, b):
        """Linear function: f(x) = mx + b"""
        return m * x + b
    
    def quadratic(self, x, a, b, c):
        """Quadratic function: f(x) = ax² + bx + c"""
        return a * x**2 + b * x + c
    
    def exponential(self, x, a, k):
        """Exponential function: f(x) = ae^(kx)"""
        return a * np.exp(k * x)
    
    def logarithmic(self, x, a, b):
        """Logarithmic function: f(x) = a*ln(x) + b"""
        return a * np.log(np.abs(x) + 1e-10) + b  # Avoid log(0)
    
    def analyze_function(self, func_type, params, x_range=None):
        """Analyze a function and return key properties"""
        if x_range is None:
            x_range = self.x_range
        
        analysis = {"type": func_type, "parameters": params}
        
        if func_type == "linear":
            m, b = params
            y = self.linear(x_range, m, b)
            analysis.update({
                "slope": m,
                "y_intercept": b,
                "x_intercept": -b/m if m != 0 else None,
                "domain": "(-∞, ∞)",
                "range": "(-∞, ∞)" if m != 0 else f"{{{b}}}",
                "increasing": m > 0,
                "function": lambda x: self.linear(x, m, b)
            })
        
        elif func_type == "quadratic":
            a, b, c = params
            y = self.quadratic(x_range, a, b, c)
            vertex_x = -b / (2*a)
            vertex_y = self.quadratic(vertex_x, a, b, c)
            discriminant = b**2 - 4*a*c
            
            analysis.update({
                "vertex": (vertex_x, vertex_y),
                "axis_of_symmetry": vertex_x,
                "y_intercept": c,
                "discriminant": discriminant,
                "opens_upward": a > 0,
                "domain": "(-∞, ∞)",
                "range": f"[{vertex_y}, ∞)" if a > 0 else f"(-∞, {vertex_y}]",
                "function": lambda x: self.quadratic(x, a, b, c)
            })
        
        elif func_type == "exponential":
            a, k = params
            x_pos = x_range[x_range >= -10]  # Avoid overflow
            y = self.exponential(x_pos, a, k)
            
            analysis.update({
                "coefficient": a,
                "growth_rate": k,
                "y_intercept": a,
                "horizontal_asymptote": 0,
                "domain": "(-∞, ∞)",
                "range": "(0, ∞)" if a > 0 else "(-∞, 0)",
                "exponential_growth": k > 0,
                "doubling_time": np.log(2)/k if k > 0 else None,
                "half_life": np.log(2)/abs(k) if k < 0 else None,
                "function": lambda x: self.exponential(x, a, k)
            })
        
        elif func_type == "logarithmic":
            a, b = params
            x_pos = x_range[x_range > 0]
            y = self.logarithmic(x_pos, a, b)
            
            analysis.update({
                "coefficient": a,
                "vertical_shift": b,
                "x_intercept": np.exp(-b/a) if a != 0 else None,
                "vertical_asymptote": 0,
                "domain": "(0, ∞)",
                "range": "(-∞, ∞)",
                "increasing": a > 0,
                "function": lambda x: self.logarithmic(x, a, b)
            })
        
        return analysis
    
    def plot_comparison(self, functions_list):
        """Plot multiple functions for comparison"""
        plt.figure(figsize=(14, 10))
        colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']
        
        for i, (func_type, params, label) in enumerate(functions_list):
            color = colors[i % len(colors)]
            
            if func_type == "linear":
                y = self.linear(self.x_range, *params)
                plt.plot(self.x_range, y, color=color, linewidth=2, label=label)
            
            elif func_type == "quadratic":
                y = self.quadratic(self.x_range, *params)
                plt.plot(self.x_range, y, color=color, linewidth=2, label=label)
            
            elif func_type == "exponential":
                x_range = np.linspace(-3, 3, 1000)
                y = self.exponential(x_range, *params)
                # Clip extreme values for better visualization
                y = np.clip(y, -50, 50)
                plt.plot(x_range, y, color=color, linewidth=2, label=label)
            
            elif func_type == "logarithmic":
                x_range = np.linspace(0.1, 5, 1000)
                y = self.logarithmic(x_range, *params)
                plt.plot(x_range, y, color=color, linewidth=2, label=label)
        
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='black', linewidth=0.5)
        plt.axvline(x=0, color='black', linewidth=0.5)
        plt.xlabel('x', fontsize=12)
        plt.ylabel('f(x)', fontsize=12)
        plt.title('Function Types Comparison', fontsize=14, fontweight='bold')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.tight_layout()
        plt.show()
    
    def print_analysis(self, analysis):
        """Print formatted analysis results"""
        print(f"\n{'='*50}")
        print(f"FUNCTION ANALYSIS: {analysis['type'].upper()}")
        print(f"Parameters: {analysis['parameters']}")
        print(f"{'='*50}")
        
        for key, value in analysis.items():
            if key not in ['type', 'parameters', 'function']:
                if isinstance(value, tuple):
                    print(f"{key.replace('_', ' ').title()}: {value}")
                elif isinstance(value, (int, float)):
                    print(f"{key.replace('_', ' ').title()}: {value:.3f}")
                else:
                    print(f"{key.replace('_', ' ').title()}: {value}")

# Usage example
analyzer = FunctionAnalyzer()

# Define functions to analyze
functions_to_analyze = [
    ("linear", (2, -1), "f(x) = 2x - 1"),
    ("quadratic", (1, -2, 1), "g(x) = x² - 2x + 1"),
    ("exponential", (1, 0.5), "h(x) = e^(0.5x)"),
    ("logarithmic", (2, 1), "k(x) = 2ln(x) + 1")
]

# Analyze each function
for func_type, params, label in functions_to_analyze:
    analysis = analyzer.analyze_function(func_type, params)
    analyzer.print_analysis(analysis)

# Create comparison plot
analyzer.plot_comparison(functions_to_analyze)

# Interactive function explorer
def explore_function():
    """Interactive function exploration"""
    print("\n" + "="*60)
    print("INTERACTIVE FUNCTION EXPLORER")
    print("="*60)
    
    while True:
        print("\nChoose function type:")
        print("1. Linear (mx + b)")
        print("2. Quadratic (ax² + bx + c)")
        print("3. Exponential (ae^(kx))")
        print("4. Logarithmic (a*ln(x) + b)")
        print("5. Exit")
        
        choice = input("\nEnter choice (1-5): ")
        
        if choice == '5':
            break
        
        try:
            if choice == '1':
                m = float(input("Enter slope (m): "))
                b = float(input("Enter y-intercept (b): "))
                analysis = analyzer.analyze_function("linear", (m, b))
            
            elif choice == '2':
                a = float(input("Enter coefficient of x² (a): "))
                b = float(input("Enter coefficient of x (b): "))
                c = float(input("Enter constant term (c): "))
                analysis = analyzer.analyze_function("quadratic", (a, b, c))
            
            elif choice == '3':
                a = float(input("Enter coefficient (a): "))
                k = float(input("Enter growth rate (k): "))
                analysis = analyzer.analyze_function("exponential", (a, k))
            
            elif choice == '4':
                a = float(input("Enter coefficient (a): "))
                b = float(input("Enter vertical shift (b): "))
                analysis = analyzer.analyze_function("logarithmic", (a, b))
            
            else:
                print("Invalid choice!")
                continue
            
            analyzer.print_analysis(analysis)
            
        except ValueError:
            print("Please enter valid numbers!")
        except Exception as e:
            print(f"Error: {e}")

# Uncomment to run interactive explorer
# explore_function()
```

</div>

</div>

<div id="function-behavior" class="section section-break">

##  1.6 Function Behavior Analysis

<div class="bg-blue-50 p-6 rounded-lg mb-6">

### Key Concepts for Function Analysis

<div class="grid grid-cols-1 md:grid-cols-2 gap-6">

- **Domain:** Set of all possible input values
- **Range:** Set of all possible output values
- **Intercepts:** Points where graph crosses axes
- **Asymptotes:** Lines the graph approaches

<!-- -->

- **Increasing/Decreasing:** Function behavior trends
- **Concavity:** Curve direction (up or down)
- **Extrema:** Maximum and minimum points
- **Symmetry:** Even, odd, or neither

</div>

</div>

</div>

<div id="programming-tools" class="section section-break">

##  1.7 Programming Tools Setup

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">

<div class="bg-gray-50 p-6 rounded-lg">

### Python Setup

``` language-python
# Essential imports for ODE work
import numpy as np
import matplotlib.pyplot as plt
import scipy as sp
from scipy.integrate import odeint, solve_ivp
import sympy as sym

# Configure plotting
plt.style.use('seaborn-v0_8')
plt.rcParams['figure.figsize'] = (10, 6)
plt.rcParams['font.size'] = 12
```

</div>

<div class="bg-blue-50 p-6 rounded-lg">

### R Setup

``` language-r
# Essential packages for ODE work
library(deSolve)    # ODE solvers
library(ggplot2)    # Advanced plotting
library(dplyr)      # Data manipulation
library(pracma)     # Practical mathematics

# Set plot theme
theme_set(theme_minimal())
```

</div>

</div>

</div>

<div id="exercises" class="section section-break">

##  1.8 Practice Exercises

<div class="space-y-6">

<div class="example-box bg-blue-50 p-6 rounded-lg">

### Exercise 1: Function Identification

Identify the type of each function and find key properties:

1.  $f(x) = 3x - 7$
2.  $g(x) = 2x^2 - 5x + 1$
3.  $h(x) = 4e^{-0.3x}$
4.  $k(x) = \ln(2x) + 3$

</div>

<div class="example-box bg-green-50 p-6 rounded-lg">

### Exercise 2: Programming Challenge

Create a program that:

1.  Takes function parameters as input
2.  Plots the function over a specified domain
3.  Finds and marks key points (intercepts, vertex, etc.)
4.  Displays function properties in a formatted table

</div>

<div class="example-box bg-yellow-50 p-6 rounded-lg">

### Exercise 3: Real-World Modeling

Model these scenarios with appropriate functions:

1.  A ball thrown upward (height vs. time)
2.  Radioactive decay of a substance
3.  Population growth in a city
4.  Sound intensity vs. distance from source

</div>

</div>

</div>

<div class="section section-break">

<div class="bg-gradient-to-r from-blue-50 to-green-50 p-8 rounded-lg">

##  Chapter Summary

<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">

<div>

### What We Covered

- [x] Linear functions and their properties
- [x] Quadratic functions and parabolas
- [x] Exponential growth and decay
- [x] Logarithmic functions as inverses
- [x] Function behavior analysis
- [x] Programming implementations

</div>

<div>

### Next Steps

- → Chapter 2: Review of Trigonometry
- → Chapter 3: Basics of Calculus
- → Chapter 4: Mathematical Modeling
- → Apply these concepts to differential equations

</div>

</div>

<div class="bg-white p-6 rounded-lg border-l-4 border-blue-500">

### Key Takeaway

Understanding these fundamental function types is crucial for differential equations because many solutions involve combinations of linear, exponential, and trigonometric functions. The programming skills developed here will be essential for numerical solutions and visualizations throughout the course.

</div>

</div>

</div>

**Comprehensive ODE Tutorial - Chapter 1**

Part of the 300-page ODE Tutorial Series \| Python & R Integration \| Interactive Learning

</div>
