<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 5: Introduction to Programming in Python & R - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        .code-container {
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background: rgba(0,0,0,0.9);
        }
        pre {
            position: relative;
            margin: 0;
        }
        .language-tab {
            display: inline-block;
            padding: 8px 16px;
            margin-right: 4px;
            background: #374151;
            color: white;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            cursor: pointer;
            font-weight: 500;
        }
        .language-tab.active {
            background: #1f2937;
        }
        .code-content {
            background: #1f2937;
            color: #e5e7eb;
            padding: 16px;
            border-radius: 0 8px 8px 8px;
            overflow-x: auto;
        }
        .hidden {
            display: none;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 leading-relaxed">
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12 bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-4xl font-bold text-blue-800 mb-4">
                <i class="fas fa-code mr-3"></i>
                Chapter 5: Introduction to Programming in Python & R
            </h1>
            <p class="text-xl text-gray-600 mb-4">
                Comprehensive Programming Foundations for Differential Equations
            </p>
            <div class="flex justify-center space-x-8 text-sm text-gray-500">
                <span><i class="fab fa-python mr-2"></i>Python 3.8+</span>
                <span><i class="fas fa-chart-line mr-2"></i>R 4.0+</span>
                <span><i class="fas fa-calculator mr-2"></i>Numerical Methods</span>
                <span><i class="fas fa-chart-bar mr-2"></i>Data Visualization</span>
            </div>
        </header>

        <!-- Table of Contents -->
        <section class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">
                <i class="fas fa-list mr-2"></i>Table of Contents
            </h2>
            <div class="grid md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold text-blue-600 mb-2">Programming Fundamentals</h3>
                    <ul class="space-y-1 text-sm text-gray-600 ml-4">
                        <li>• Basic Syntax and Data Types</li>
                        <li>• Data Structures</li>
                        <li>• Control Flow</li>
                        <li>• Functions and Modules</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-600 mb-2">ODE-Specific Tools</h3>
                    <ul class="space-y-1 text-sm text-gray-600 ml-4">
                        <li>• Essential Libraries</li>
                        <li>• Numerical Methods</li>
                        <li>• Plotting and Visualization</li>
                        <li>• Symbolic Computation</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Section 1: Programming Basics -->
        <section class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-3xl font-bold text-blue-800 mb-6">
                <i class="fas fa-play-circle mr-3"></i>1. Programming Fundamentals
            </h2>

            <!-- Basic Syntax -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4">1.1 Basic Syntax and Data Types</h3>
                <p class="text-gray-600 mb-4">
                    Understanding the fundamental syntax differences between Python and R is crucial for implementing 
                    numerical methods for differential equations.
                </p>

                <div class="comparison-grid">
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fab fa-python mr-2"></i>Python
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Basic data types and operations
import numpy as np
import matplotlib.pyplot as plt

# Numbers
a = 42          # integer
b = 3.14159     # float
c = 2 + 3j      # complex number

# Strings
name = "Differential Equations"
print(f"Welcome to {name}")

# Lists (dynamic arrays)
coefficients = [1, -2, 1]  # for y'' - 2y' + y = 0
initial_conditions = [1.0, 0.0]  # y(0) = 1, y'(0) = 0

# Boolean operations
is_linear = True
is_homogeneous = coefficients[-1] == 0

# Display types
print(f"a: {type(a)}, b: {type(b)}, c: {type(c)}")
print(f"Coefficients: {coefficients}")
print(f"Linear ODE: {is_linear}")
</code>
                            </pre>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fas fa-chart-line mr-2"></i>R
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Basic data types and operations
library(ggplot2)
library(deSolve)

# Numbers
a <- 42L        # integer (L suffix)
b <- 3.14159    # numeric (double)
c <- 2 + 3i     # complex number

# Strings
name <- "Differential Equations"
cat("Welcome to", name, "\n")

# Vectors (basic data structure)
coefficients <- c(1, -2, 1)  # for y'' - 2y' + y = 0
initial_conditions <- c(1.0, 0.0)  # y(0) = 1, y'(0) = 0

# Logical operations
is_linear <- TRUE
is_homogeneous <- coefficients[length(coefficients)] == 0

# Display types
cat("a:", class(a), ", b:", class(b), ", c:", class(c), "\n")
cat("Coefficients:", coefficients, "\n")
cat("Linear ODE:", is_linear, "\n")
</code>
                            </pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Structures -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4">1.2 Data Structures for ODE Problems</h3>
                <p class="text-gray-600 mb-4">
                    Proper data structures are essential for storing solution arrays, parameter sets, and time grids 
                    in differential equation computations.
                </p>

                <div class="comparison-grid">
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fab fa-python mr-2"></i>Python Data Structures
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># NumPy Arrays - Essential for numerical computation
import numpy as np

# 1D arrays for time and solutions
t = np.linspace(0, 10, 1001)  # time grid
y = np.zeros_like(t)          # solution array

# 2D arrays for systems of ODEs
# For system: x' = -y, y' = x (harmonic oscillator)
solution_matrix = np.zeros((len(t), 2))  # [x, y] at each time

# Dictionaries for parameters
params = {
    'k': 1.0,      # spring constant
    'm': 1.0,      # mass
    'c': 0.1,      # damping coefficient
    'omega': np.sqrt(1.0/1.0)  # natural frequency
}

# Lists for multiple solutions/experiments
experiments = []
for c_val in [0.0, 0.1, 0.5, 1.0]:
    params['c'] = c_val
    experiments.append(params.copy())

# Structured arrays for complex data
dt = np.dtype([('time', 'f8'), ('position', 'f8'), ('velocity', 'f8')])
trajectory = np.zeros(100, dtype=dt)

print(f"Time grid shape: {t.shape}")
print(f"Solution matrix shape: {solution_matrix.shape}")
print(f"Number of experiments: {len(experiments)}")
</code>
                            </pre>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fas fa-chart-line mr-2"></i>R Data Structures
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Vectors and Matrices for numerical computation

# Vectors for time and solutions
t <- seq(0, 10, length.out = 1001)  # time grid
y <- numeric(length(t))             # solution vector

# Matrices for systems of ODEs
# For system: x' = -y, y' = x (harmonic oscillator)
solution_matrix <- matrix(0, nrow = length(t), ncol = 2)
colnames(solution_matrix) <- c("x", "y")

# Lists for parameters (like Python dictionaries)
params <- list(
  k = 1.0,      # spring constant
  m = 1.0,      # mass
  c = 0.1,      # damping coefficient
  omega = sqrt(1.0/1.0)  # natural frequency
)

# Data frames for structured data
experiments <- data.frame(
  c_val = c(0.0, 0.1, 0.5, 1.0),
  k = rep(1.0, 4),
  m = rep(1.0, 4)
)

# Data frame for trajectory data
trajectory <- data.frame(
  time = numeric(100),
  position = numeric(100),
  velocity = numeric(100)
)

cat("Time grid length:", length(t), "\n")
cat("Solution matrix dimensions:", dim(solution_matrix), "\n")
cat("Number of experiments:", nrow(experiments), "\n")
</code>
                            </pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Control Flow -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4">1.3 Control Flow for Numerical Methods</h3>
                <p class="text-gray-600 mb-4">
                    Control structures are fundamental for implementing iterative numerical methods like Euler's method 
                    and Runge-Kutta algorithms.
                </p>

                <div class="comparison-grid">
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fab fa-python mr-2"></i>Python Control Flow
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Control flow for numerical methods
import numpy as np

def euler_method(f, t0, y0, h, n_steps):
    """
    Implement Euler's method for solving ODEs
    f: function defining dy/dt = f(t, y)
    t0: initial time
    y0: initial condition
    h: step size
    n_steps: number of steps
    """
    # Initialize arrays
    t = np.zeros(n_steps + 1)
    y = np.zeros(n_steps + 1)
    
    # Set initial conditions
    t[0] = t0
    y[0] = y0
    
    # Euler's method loop
    for i in range(n_steps):
        # Calculate slope at current point
        slope = f(t[i], y[i])
        
        # Update using Euler's formula
        y[i+1] = y[i] + h * slope
        t[i+1] = t[i] + h
        
        # Optional: Check for convergence or stability
        if abs(y[i+1]) > 1e10:
            print(f"Warning: Solution may be unstable at step {i}")
            break
    
    return t[:i+2], y[:i+2]

# Example: dy/dt = -2y with y(0) = 1
def exponential_decay(t, y):
    return -2 * y

# Solve the ODE
t_sol, y_sol = euler_method(exponential_decay, 0, 1, 0.1, 50)

# Conditional logic for method selection
def solve_ode(method='euler', **kwargs):
    if method == 'euler':
        return euler_method(**kwargs)
    elif method == 'rk4':
        return runge_kutta_4(**kwargs)  # Would implement separately
    else:
        raise ValueError(f"Unknown method: {method}")

print(f"Solution computed with {len(t_sol)} points")
</code>
                            </pre>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fas fa-chart-line mr-2"></i>R Control Flow
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Control flow for numerical methods

euler_method <- function(f, t0, y0, h, n_steps) {
  # Implement Euler's method for solving ODEs
  # f: function defining dy/dt = f(t, y)
  # t0: initial time
  # y0: initial condition
  # h: step size
  # n_steps: number of steps
  
  # Initialize vectors
  t <- numeric(n_steps + 1)
  y <- numeric(n_steps + 1)
  
  # Set initial conditions
  t[1] <- t0
  y[1] <- y0
  
  # Euler's method loop
  for (i in 1:n_steps) {
    # Calculate slope at current point
    slope <- f(t[i], y[i])
    
    # Update using Euler's formula
    y[i+1] <- y[i] + h * slope
    t[i+1] <- t[i] + h
    
    # Optional: Check for convergence or stability
    if (abs(y[i+1]) > 1e10) {
      cat("Warning: Solution may be unstable at step", i, "\n")
      break
    }
  }
  
  return(list(t = t[1:(i+1)], y = y[1:(i+1)]))
}

# Example: dy/dt = -2y with y(0) = 1
exponential_decay <- function(t, y) {
  return(-2 * y)
}

# Solve the ODE
solution <- euler_method(exponential_decay, 0, 1, 0.1, 50)

# Conditional logic for method selection
solve_ode <- function(method = 'euler', ...) {
  if (method == 'euler') {
    return(euler_method(...))
  } else if (method == 'rk4') {
    return(runge_kutta_4(...))  # Would implement separately
  } else {
    stop(paste("Unknown method:", method))
  }
}

cat("Solution computed with", length(solution$t), "points\n")
</code>
                            </pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: Essential Libraries -->
        <section class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-3xl font-bold text-blue-800 mb-6">
                <i class="fas fa-book-open mr-3"></i>2. Essential Libraries for ODEs
            </h2>

            <!-- Python Libraries -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4">2.1 Python Libraries</h3>
                
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">
                            <i class="fas fa-calculator mr-2"></i>NumPy & SciPy
                        </h4>
                        <p class="text-sm text-gray-600">Numerical computing and scientific functions</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-800 mb-2">
                            <i class="fas fa-chart-line mr-2"></i>Matplotlib
                        </h4>
                        <p class="text-sm text-gray-600">Plotting and visualization</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-800 mb-2">
                            <i class="fas fa-infinity mr-2"></i>SymPy
                        </h4>
                        <p class="text-sm text-gray-600">Symbolic mathematics</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-800 mb-2">
                            <i class="fas fa-cogs mr-2"></i>SciPy.integrate
                        </h4>
                        <p class="text-sm text-gray-600">ODE solving functions</p>
                    </div>
                </div>

                <div class="code-container">
                    <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                    <pre class="code-content">
<code># Essential Python imports for ODE work
import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint, solve_ivp
import sympy as sp
from sympy import symbols, Function, Eq, dsolve

# NumPy for arrays and mathematical functions
t = np.linspace(0, 10, 1000)
y = np.exp(-t) * np.cos(2*np.pi*t)

# SciPy for solving ODEs numerically
def harmonic_oscillator(t, y):
    """Harmonic oscillator: y'' + y = 0 as system y' = [y[1], -y[0]]"""
    return [y[1], -y[0]]

# Solve using solve_ivp (preferred method)
sol = solve_ivp(harmonic_oscillator, [0, 10], [1, 0], t_eval=t, method='RK45')

# Matplotlib for plotting
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.plot(t, y, 'b-', label='Analytical: $e^{-t}\cos(2\pi t)$')
plt.xlabel('Time')
plt.ylabel('Amplitude')
plt.title('Damped Oscillation')
plt.legend()
plt.grid(True)

plt.subplot(1, 2, 2)
plt.plot(sol.t, sol.y[0], 'r-', label='Position')
plt.plot(sol.t, sol.y[1], 'g--', label='Velocity')
plt.xlabel('Time')
plt.ylabel('Value')
plt.title('Harmonic Oscillator')
plt.legend()
plt.grid(True)

plt.tight_layout()
plt.show()

# SymPy for symbolic solutions
t_sym = symbols('t')
y_func = Function('y')

# Define ODE symbolically: y'' + y = 0
ode = Eq(y_func(t_sym).diff(t_sym, 2) + y_func(t_sym), 0)
general_solution = dsolve(ode, y_func(t_sym))
print("Symbolic solution:", general_solution)

# Evaluate symbolic expressions numerically
C1, C2 = symbols('C1 C2')
particular_solution = general_solution.rhs.subs([(C1, 1), (C2, 0)])
numerical_func = sp.lambdify(t_sym, particular_solution, 'numpy')
y_symbolic = numerical_func(t)

print(f"Max difference between numerical and symbolic: {np.max(np.abs(sol.y[0] - y_symbolic)):.2e}")
</code>
                    </pre>
                </div>
            </div>

            <!-- R Libraries -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4">2.2 R Libraries</h3>
                
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">
                            <i class="fas fa-cogs mr-2"></i>deSolve
                        </h4>
                        <p class="text-sm text-gray-600">Differential equation solving</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-800 mb-2">
                            <i class="fas fa-chart-bar mr-2"></i>ggplot2
                        </h4>
                        <p class="text-sm text-gray-600">Advanced plotting system</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-800 mb-2">
                            <i class="fas fa-calculator mr-2"></i>pracma
                        </h4>
                        <p class="text-sm text-gray-600">Practical numerical math</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-800 mb-2">
                            <i class="fas fa-infinity mr-2"></i>Ryacas
                        </h4>
                        <p class="text-sm text-gray-600">Symbolic computation</p>
                    </div>
                </div>

                <div class="code-container">
                    <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                    <pre class="code-content">
<code># Essential R libraries for ODE work
library(deSolve)
library(ggplot2)
library(pracma)
library(reshape2)  # for data manipulation

# Base R for arrays and mathematical functions
t <- seq(0, 10, length.out = 1000)
y <- exp(-t) * cos(2*pi*t)

# deSolve for solving ODEs numerically
harmonic_oscillator <- function(t, y, parameters) {
  # Harmonic oscillator: y'' + y = 0 as system y' = [y[2], -y[1]]
  with(as.list(c(y, parameters)), {
    dy1 <- y2       # dy/dt = velocity
    dy2 <- -y1      # d²y/dt² = -y (simple harmonic motion)
    list(c(dy1, dy2))
  })
}

# Initial conditions: y(0) = 1, y'(0) = 0
initial_conditions <- c(y1 = 1, y2 = 0)
parameters <- list()

# Solve the ODE
solution <- ode(y = initial_conditions, 
                times = t, 
                func = harmonic_oscillator, 
                parms = parameters,
                method = "rk4")

# Convert to data frame for ggplot2
sol_df <- data.frame(solution)
names(sol_df) <- c("time", "position", "velocity")

# Create comparison data frame
comparison_df <- data.frame(
  time = t,
  analytical = y,
  numerical = sol_df$position
)

# Melt for plotting multiple series
comparison_melted <- melt(comparison_df, id.vars = "time", 
                         variable.name = "type", value.name = "value")

# ggplot2 for advanced plotting
p1 <- ggplot(comparison_melted, aes(x = time, y = value, color = type)) +
  geom_line(size = 1) +
  labs(title = "Analytical vs Numerical Solution",
       x = "Time", y = "Amplitude",
       color = "Solution Type") +
  theme_minimal() +
  theme(legend.position = "bottom")

# Phase portrait
p2 <- ggplot(sol_df, aes(x = position, y = velocity)) +
  geom_path(size = 1, color = "blue") +
  geom_point(aes(x = 1, y = 0), color = "red", size = 3) +  # Initial condition
  labs(title = "Phase Portrait: Harmonic Oscillator",
       x = "Position", y = "Velocity") +
  theme_minimal() +
  coord_equal()

# Display plots
print(p1)
print(p2)

# pracma for numerical methods
# Example: numerical differentiation
dy_dt_numerical <- gradient(sol_df$position, t[2] - t[1])
velocity_error <- max(abs(dy_dt_numerical - sol_df$velocity))

cat("Maximum velocity error (numerical differentiation):", 
    sprintf("%.2e", velocity_error), "\n")

# Symbolic computation with Ryacas (if available)
# Note: Ryacas might require separate installation
# library(Ryacas)
# yac_str("Solve(y'' + y == 0, y)")
</code>
                    </pre>
                </div>
            </div>
        </section>

        <!-- Section 3: Plotting and Visualization -->
        <section class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-3xl font-bold text-blue-800 mb-6">
                <i class="fas fa-chart-area mr-3"></i>3. Plotting and Visualization
            </h2>

            <p class="text-gray-600 mb-6">
                Effective visualization is crucial for understanding ODE solutions, analyzing stability, 
                and communicating results. Both Python and R offer powerful plotting capabilities.
            </p>

            <div class="comparison-grid">
                <div>
                    <h3 class="text-xl font-semibold text-blue-600 mb-4">
                        <i class="fab fa-python mr-2"></i>Python Visualization
                    </h3>
                    <div class="code-container">
                        <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                        <pre class="code-content">
<code># Advanced plotting for ODE analysis
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy.integrate import solve_ivp

# Set up matplotlib for better plots
plt.style.use('seaborn-v0_8')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

# Example: Damped harmonic oscillator
def damped_oscillator(t, y, gamma=0.5, omega0=1.0):
    """
    Damped harmonic oscillator: y'' + 2γy' + ω₀²y = 0
    State vector: y = [position, velocity]
    """
    return [y[1], -2*gamma*y[1] - omega0**2*y[0]]

# Solve for different damping coefficients
t_span = (0, 10)
t_eval = np.linspace(0, 10, 1000)
initial_conditions = [1, 0]  # y(0) = 1, y'(0) = 0

damping_values = [0.1, 0.5, 1.0, 2.0]
colors = ['blue', 'green', 'orange', 'red']

# Create subplot layout
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

# Plot 1: Multiple damping solutions
for gamma, color in zip(damping_values, colors):
    sol = solve_ivp(lambda t, y: damped_oscillator(t, y, gamma), 
                    t_span, initial_conditions, t_eval=t_eval)
    ax1.plot(sol.t, sol.y[0], color=color, linewidth=2, 
             label=f'γ = {gamma}')

ax1.set_xlabel('Time')
ax1.set_ylabel('Position')
ax1.set_title('Damped Harmonic Oscillator: Effect of Damping')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Plot 2: Phase portraits
for gamma, color in zip(damping_values, colors):
    sol = solve_ivp(lambda t, y: damped_oscillator(t, y, gamma), 
                    t_span, initial_conditions, t_eval=t_eval)
    ax2.plot(sol.y[0], sol.y[1], color=color, linewidth=2, 
             label=f'γ = {gamma}')
    ax2.plot(1, 0, 'ko', markersize=8)  # Initial condition

ax2.set_xlabel('Position')
ax2.set_ylabel('Velocity')
ax2.set_title('Phase Portraits')
ax2.legend()
ax2.grid(True, alpha=0.3)
ax2.axis('equal')

# Plot 3: Energy analysis
gamma = 0.1
sol = solve_ivp(lambda t, y: damped_oscillator(t, y, gamma), 
                t_span, initial_conditions, t_eval=t_eval)

kinetic_energy = 0.5 * sol.y[1]**2
potential_energy = 0.5 * sol.y[0]**2
total_energy = kinetic_energy + potential_energy

ax3.plot(sol.t, kinetic_energy, 'b-', label='Kinetic')
ax3.plot(sol.t, potential_energy, 'r-', label='Potential')
ax3.plot(sol.t, total_energy, 'k--', label='Total')
ax3.set_xlabel('Time')
ax3.set_ylabel('Energy')
ax3.set_title(f'Energy vs Time (γ = {gamma})')
ax3.legend()
ax3.grid(True, alpha=0.3)

# Plot 4: 3D trajectory (for system of ODEs)
def lorenz_system(t, y, sigma=10, rho=28, beta=8/3):
    """Lorenz system of ODEs"""
    return [sigma*(y[1] - y[0]),
            y[0]*(rho - y[2]) - y[1],
            y[0]*y[1] - beta*y[2]]

# Solve Lorenz system
lorenz_sol = solve_ivp(lorenz_system, (0, 20), [1, 1, 1], 
                       t_eval=np.linspace(0, 20, 2000))

# Remove the 2D plot and create 3D plot
ax4.remove()
ax4 = fig.add_subplot(224, projection='3d')
ax4.plot(lorenz_sol.y[0], lorenz_sol.y[1], lorenz_sol.y[2], 
         'b-', alpha=0.7, linewidth=0.8)
ax4.set_xlabel('X')
ax4.set_ylabel('Y')
ax4.set_zlabel('Z')
ax4.set_title('Lorenz Attractor')

plt.tight_layout()
plt.show()

# Advanced plotting: Animation (conceptual)
from matplotlib.animation import FuncAnimation

def create_oscillator_animation():
    """Create animated plot of oscillator"""
    fig, ax = plt.subplots()
    line, = ax.plot([], [], 'bo-', markersize=10)
    ax.set_xlim(-2, 2)
    ax.set_ylim(-2, 2)
    ax.set_aspect('equal')
    ax.grid(True)
    
    def animate(frame):
        t_current = frame * 0.1
        sol = solve_ivp(lambda t, y: damped_oscillator(t, y, 0.1), 
                        (0, t_current), [1, 0], t_eval=[t_current])
        if len(sol.y[0]) > 0:
            x, y = sol.y[0][-1], sol.y[1][-1]
            line.set_data([0, x], [0, 0])
        return line,
    
    # Would create animation with: FuncAnimation(fig, animate, frames=100, interval=50)
    return fig

print("Visualization examples complete!")
</code>
                        </pre>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-blue-600 mb-4">
                        <i class="fas fa-chart-line mr-2"></i>R Visualization
                    </h3>
                    <div class="code-container">
                        <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                        <pre class="code-content">
<code># Advanced plotting for ODE analysis in R
library(ggplot2)
library(deSolve)
library(reshape2)
library(gridExtra)
library(plotly)  # for interactive plots

# Example: Damped harmonic oscillator
damped_oscillator <- function(t, y, parameters) {
  with(as.list(c(y, parameters)), {
    dy1 <- y2
    dy2 <- -2*gamma*y2 - omega0^2*y1
    list(c(dy1, dy2))
  })
}

# Solve for different damping coefficients
times <- seq(0, 10, length.out = 1000)
initial_conditions <- c(y1 = 1, y2 = 0)
damping_values <- c(0.1, 0.5, 1.0, 2.0)

# Collect solutions
solutions_list <- list()
for (i in seq_along(damping_values)) {
  gamma <- damping_values[i]
  params <- list(gamma = gamma, omega0 = 1.0)
  
  sol <- ode(y = initial_conditions, 
             times = times,
             func = damped_oscillator, 
             parms = params)
  
  sol_df <- data.frame(sol)
  sol_df$gamma <- gamma
  sol_df$gamma_label <- paste("γ =", gamma)
  solutions_list[[i]] <- sol_df
}

# Combine all solutions
all_solutions <- do.call(rbind, solutions_list)
names(all_solutions)[1:3] <- c("time", "position", "velocity")

# Plot 1: Multiple damping solutions
p1 <- ggplot(all_solutions, aes(x = time, y = position, color = gamma_label)) +
  geom_line(size = 1) +
  labs(title = "Damped Harmonic Oscillator: Effect of Damping",
       x = "Time", y = "Position", color = "Damping") +
  theme_minimal() +
  theme(legend.position = "bottom")

# Plot 2: Phase portraits
p2 <- ggplot(all_solutions, aes(x = position, y = velocity, color = gamma_label)) +
  geom_path(size = 1) +
  geom_point(aes(x = 1, y = 0), color = "black", size = 3) +
  labs(title = "Phase Portraits",
       x = "Position", y = "Velocity", color = "Damping") +
  theme_minimal() +
  coord_equal() +
  theme(legend.position = "bottom")

# Plot 3: Energy analysis
gamma_selected <- 0.1
energy_solution <- subset(all_solutions, gamma == gamma_selected)

energy_solution$kinetic <- 0.5 * energy_solution$velocity^2
energy_solution$potential <- 0.5 * energy_solution$position^2
energy_solution$total <- energy_solution$kinetic + energy_solution$potential

# Reshape for plotting
energy_melted <- melt(energy_solution[, c("time", "kinetic", "potential", "total")], 
                      id.vars = "time", variable.name = "energy_type", 
                      value.name = "energy")

p3 <- ggplot(energy_melted, aes(x = time, y = energy, color = energy_type)) +
  geom_line(size = 1) +
  labs(title = paste("Energy vs Time (γ =", gamma_selected, ")"),
       x = "Time", y = "Energy", color = "Energy Type") +
  theme_minimal() +
  theme(legend.position = "bottom")

# Plot 4: Direction field (slope field)
create_direction_field <- function() {
  # Create grid for direction field
  x_grid <- seq(-2, 2, by = 0.2)
  y_grid <- seq(-2, 2, by = 0.2)
  
  # Calculate slope at each point for y' = -x (simple harmonic oscillator)
  direction_data <- expand.grid(x = x_grid, y = y_grid)
  direction_data$dx <- 1
  direction_data$dy <- -direction_data$x  # For y' = -x
  
  # Normalize arrows
  magnitude <- sqrt(direction_data$dx^2 + direction_data$dy^2)
  direction_data$dx <- direction_data$dx / magnitude * 0.1
  direction_data$dy <- direction_data$dy / magnitude * 0.1
  
  p4 <- ggplot(direction_data) +
    geom_segment(aes(x = x, y = y, xend = x + dx, yend = y + dy),
                 arrow = arrow(length = unit(0.02, "npc")),
                 color = "gray60", size = 0.5) +
    geom_path(data = subset(all_solutions, gamma == 0.1),
              aes(x = position, y = velocity), 
              color = "red", size = 1.5) +
    labs(title = "Direction Field with Solution Trajectory",
         x = "Position", y = "Velocity") +
    theme_minimal() +
    coord_equal()
  
  return(p4)
}

p4 <- create_direction_field()

# Arrange plots
grid.arrange(p1, p2, p3, p4, ncol = 2)

# Interactive plot with plotly
interactive_plot <- plot_ly(data = all_solutions, 
                           x = ~time, y = ~position, 
                           color = ~gamma_label,
                           type = 'scatter', mode = 'lines',
                           name = ~gamma_label) %>%
  layout(title = "Interactive Damped Oscillator",
         xaxis = list(title = "Time"),
         yaxis = list(title = "Position"))

# Display interactive plot (would show in RStudio viewer)
# interactive_plot

# 3D plot for systems (using scatterplot3d or plotly)
library(plotly)

# Example: Lorenz system
lorenz_system <- function(t, y, parameters) {
  with(as.list(c(y, parameters)), {
    dy1 <- sigma * (y2 - y1)
    dy2 <- y1 * (rho - y3) - y2
    dy3 <- y1 * y2 - beta * y3
    list(c(dy1, dy2, dy3))
  })
}

# Solve Lorenz system
lorenz_params <- list(sigma = 10, rho = 28, beta = 8/3)
lorenz_times <- seq(0, 20, by = 0.01)
lorenz_initial <- c(y1 = 1, y2 = 1, y3 = 1)

lorenz_sol <- ode(y = lorenz_initial, 
                  times = lorenz_times,
                  func = lorenz_system, 
                  parms = lorenz_params)

lorenz_df <- data.frame(lorenz_sol)
names(lorenz_df) <- c("time", "x", "y", "z")

# 3D plot
plot_3d <- plot_ly(lorenz_df, x = ~x, y = ~y, z = ~z, 
                   type = 'scatter3d', mode = 'lines',
                   line = list(color = ~time, colorscale = 'Viridis', width = 2)) %>%
  layout(title = "Lorenz Attractor",
         scene = list(xaxis = list(title = "X"),
                     yaxis = list(title = "Y"),
                     zaxis = list(title = "Z")))

# plot_3d  # Would display in viewer

cat("Advanced visualization examples complete!\n")
</code>
                        </pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4: Numerical Methods Implementation -->
        <section class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-3xl font-bold text-blue-800 mb-6">
                <i class="fas fa-calculator mr-3"></i>4. Numerical Methods Implementation
            </h2>

            <p class="text-gray-600 mb-6">
                Implementing numerical methods from scratch helps understand the algorithms behind ODE solvers 
                and provides flexibility for custom problems.
            </p>

            <!-- Euler's Method -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4">4.1 Euler's Method Implementation</h3>
                
                <div class="bg-blue-50 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold text-blue-800 mb-2">Mathematical Foundation</h4>
                    <p class="text-gray-700">
                        Euler's method approximates the solution to $\frac{dy}{dt} = f(t,y)$ with $y(t_0) = y_0$ using:
                    </p>
                    <p class="text-center font-mono bg-white p-2 rounded mt-2">
                        $y_{n+1} = y_n + h \cdot f(t_n, y_n)$
                    </p>
                    <p class="text-gray-700 mt-2">
                        where $h$ is the step size and $t_n = t_0 + nh$.
                    </p>
                </div>

                <div class="comparison-grid">
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fab fa-python mr-2"></i>Python Implementation
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Complete Euler's Method Implementation
import numpy as np
import matplotlib.pyplot as plt

def euler_method(f, t_span, y0, h, return_all=True):
    """
    Solve ODE using Euler's method
    
    Parameters:
    - f: function f(t, y) defining dy/dt = f(t, y)
    - t_span: tuple (t_start, t_end)
    - y0: initial condition (scalar or array)
    - h: step size
    - return_all: if True, return all intermediate points
    
    Returns:
    - t_array: time points
    - y_array: solution values
    """
    t_start, t_end = t_span
    n_steps = int((t_end - t_start) / h)
    
    # Handle both scalar and vector initial conditions
    y0 = np.atleast_1d(y0)
    dim = len(y0)
    
    # Initialize arrays
    if return_all:
        t_array = np.zeros(n_steps + 1)
        y_array = np.zeros((n_steps + 1, dim))
    else:
        t_array = np.array([t_start])
        y_array = y0.reshape(1, -1)
    
    # Set initial conditions
    t_current = t_start
    y_current = y0.copy()
    
    if return_all:
        t_array[0] = t_current
        y_array[0] = y_current
    
    # Euler's method iterations
    for i in range(n_steps):
        # Calculate derivative
        dy_dt = np.atleast_1d(f(t_current, y_current))
        
        # Euler step
        y_next = y_current + h * dy_dt
        t_next = t_current + h
        
        if return_all:
            t_array[i + 1] = t_next
            y_array[i + 1] = y_next
        
        # Update for next iteration
        t_current = t_next
        y_current = y_next
    
    if not return_all:
        return t_current, y_current
    
    # Return scalar if input was scalar
    if dim == 1:
        return t_array, y_array.flatten()
    else:
        return t_array, y_array

# Example 1: Exponential decay dy/dt = -2y, y(0) = 1
def exponential_decay(t, y):
    return -2 * y

# Analytical solution for comparison
def analytical_exponential(t):
    return np.exp(-2 * t)

# Test different step sizes
step_sizes = [0.5, 0.1, 0.05, 0.01]
t_span = (0, 2)
y0 = 1

plt.figure(figsize=(12, 8))

# Plot analytical solution
t_exact = np.linspace(0, 2, 1000)
y_exact = analytical_exponential(t_exact)
plt.subplot(2, 2, 1)
plt.plot(t_exact, y_exact, 'k-', linewidth=2, label='Exact')

for i, h in enumerate(step_sizes):
    t_euler, y_euler = euler_method(exponential_decay, t_span, y0, h)
    plt.plot(t_euler, y_euler, 'o-', label=f'h = {h}', markersize=3)

plt.xlabel('Time')
plt.ylabel('y(t)')
plt.title('Euler Method: Effect of Step Size')
plt.legend()
plt.grid(True, alpha=0.3)

# Example 2: Harmonic oscillator y'' + y = 0
def harmonic_oscillator_system(t, y):
    """Convert second-order ODE to first-order system"""
    return np.array([y[1], -y[0]])

# Initial conditions: y(0) = 1, y'(0) = 0
y0_system = np.array([1, 0])

# Solve with Euler method
h = 0.01
t_euler, y_euler = euler_method(harmonic_oscillator_system, (0, 4*np.pi), y0_system, h)

# Analytical solution
t_exact = np.linspace(0, 4*np.pi, 1000)
y_exact_pos = np.cos(t_exact)
y_exact_vel = -np.sin(t_exact)

# Plot position
plt.subplot(2, 2, 2)
plt.plot(t_exact, y_exact_pos, 'k-', linewidth=2, label='Exact')
plt.plot(t_euler, y_euler[:, 0], 'r--', label='Euler', alpha=0.7)
plt.xlabel('Time')
plt.ylabel('Position')
plt.title('Harmonic Oscillator: Position')
plt.legend()
plt.grid(True, alpha=0.3)

# Phase portrait
plt.subplot(2, 2, 3)
plt.plot(y_exact_pos, y_exact_vel, 'k-', linewidth=2, label='Exact')
plt.plot(y_euler[:, 0], y_euler[:, 1], 'r--', label='Euler', alpha=0.7)
plt.xlabel('Position')
plt.ylabel('Velocity')
plt.title('Phase Portrait')
plt.legend()
plt.grid(True, alpha=0.3)
plt.axis('equal')

# Error analysis
plt.subplot(2, 2, 4)
# Interpolate exact solution at Euler time points
y_exact_interp = np.cos(t_euler)
error = np.abs(y_euler[:, 0] - y_exact_interp)
plt.semilogy(t_euler, error, 'b-', linewidth=2)
plt.xlabel('Time')
plt.ylabel('Absolute Error')
plt.title('Error Growth in Euler Method')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Stability analysis
def test_stability():
    """Test stability of Euler method for different step sizes"""
    def stiff_equation(t, y):
        return -50 * y  # Stiff equation
    
    step_sizes = [0.1, 0.04, 0.02, 0.01]
    colors = ['red', 'orange', 'green', 'blue']
    
    plt.figure(figsize=(10, 6))
    
    for h, color in zip(step_sizes, colors):
        try:
            t_vals, y_vals = euler_method(stiff_equation, (0, 1), 1, h)
            plt.plot(t_vals, y_vals, color=color, label=f'h = {h}')
        except:
            plt.plot([], [], color=color, label=f'h = {h} (unstable)')
    
    # Exact solution
    t_exact = np.linspace(0, 1, 1000)
    y_exact = np.exp(-50 * t_exact)
    plt.plot(t_exact, y_exact, 'k-', linewidth=2, label='Exact')
    
    plt.xlabel('Time')
    plt.ylabel('y(t)')
    plt.title('Stability of Euler Method for Stiff Equation')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

test_stability()
print("Euler method implementation complete!")
</code>
                            </pre>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fas fa-chart-line mr-2"></i>R Implementation
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Complete Euler's Method Implementation in R
library(ggplot2)
library(reshape2)
library(gridExtra)

euler_method <- function(f, t_span, y0, h, return_all = TRUE) {
  # Solve ODE using Euler's method
  # 
  # Parameters:
  # - f: function f(t, y) defining dy/dt = f(t, y)
  # - t_span: vector c(t_start, t_end)
  # - y0: initial condition (scalar or vector)
  # - h: step size
  # - return_all: if TRUE, return all intermediate points
  # 
  # Returns:
  # - list with t_array and y_array
  
  t_start <- t_span[1]
  t_end <- t_span[2]
  n_steps <- floor((t_end - t_start) / h)
  
  # Handle both scalar and vector initial conditions
  if (is.vector(y0) && length(y0) > 1) {
    dim <- length(y0)
  } else {
    dim <- 1
    y0 <- as.numeric(y0)
  }
  
  # Initialize arrays
  if (return_all) {
    t_array <- numeric(n_steps + 1)
    if (dim == 1) {
      y_array <- numeric(n_steps + 1)
    } else {
      y_array <- matrix(0, nrow = n_steps + 1, ncol = dim)
    }
  }
  
  # Set initial conditions
  t_current <- t_start
  y_current <- y0
  
  if (return_all) {
    t_array[1] <- t_current
    if (dim == 1) {
      y_array[1] <- y_current
    } else {
      y_array[1, ] <- y_current
    }
  }
  
  # Euler's method iterations
  for (i in 1:n_steps) {
    # Calculate derivative
    dy_dt <- f(t_current, y_current)
    
    # Euler step
    y_next <- y_current + h * dy_dt
    t_next <- t_current + h
    
    if (return_all) {
      t_array[i + 1] <- t_next
      if (dim == 1) {
        y_array[i + 1] <- y_next
      } else {
        y_array[i + 1, ] <- y_next
      }
    }
    
    # Update for next iteration
    t_current <- t_next
    y_current <- y_next
  }
  
  if (!return_all) {
    return(list(t = t_current, y = y_current))
  }
  
  return(list(t = t_array, y = y_array))
}

# Example 1: Exponential decay dy/dt = -2y, y(0) = 1
exponential_decay <- function(t, y) {
  return(-2 * y)
}

# Analytical solution for comparison
analytical_exponential <- function(t) {
  return(exp(-2 * t))
}

# Test different step sizes
step_sizes <- c(0.5, 0.1, 0.05, 0.01)
t_span <- c(0, 2)
y0 <- 1

# Create data frame for plotting
plot_data <- data.frame()

# Analytical solution
t_exact <- seq(0, 2, length.out = 1000)
y_exact <- analytical_exponential(t_exact)
exact_df <- data.frame(t = t_exact, y = y_exact, method = "Exact", h = "Exact")
plot_data <- rbind(plot_data, exact_df)

# Euler solutions for different step sizes
for (h in step_sizes) {
  solution <- euler_method(exponential_decay, t_span, y0, h)
  euler_df <- data.frame(t = solution$t, y = solution$y, 
                        method = "Euler", h = as.character(h))
  plot_data <- rbind(plot_data, euler_df)
}

# Plot 1: Effect of step size
p1 <- ggplot(plot_data, aes(x = t, y = y, color = h, linetype = method)) +
  geom_line(size = 1) +
  geom_point(data = subset(plot_data, method == "Euler"), size = 1) +
  labs(title = "Euler Method: Effect of Step Size",
       x = "Time", y = "y(t)", color = "Step Size", linetype = "Method") +
  theme_minimal()

# Example 2: Harmonic oscillator y'' + y = 0
harmonic_oscillator_system <- function(t, y) {
  # Convert second-order ODE to first-order system
  return(c(y[2], -y[1]))
}

# Initial conditions: y(0) = 1, y'(0) = 0
y0_system <- c(1, 0)

# Solve with Euler method
h <- 0.01
solution_system <- euler_method(harmonic_oscillator_system, c(0, 4*pi), y0_system, h)

# Create data frame for system solution
t_vals <- solution_system$t
y_vals <- solution_system$y
system_df <- data.frame(
  time = t_vals,
  position = y_vals[, 1],
  velocity = y_vals[, 2]
)

# Analytical solution
t_exact_system <- seq(0, 4*pi, length.out = 1000)
exact_system_df <- data.frame(
  time = t_exact_system,
  position = cos(t_exact_system),
  velocity = -sin(t_exact_system)
)

# Combine for plotting
system_df$method <- "Euler"
exact_system_df$method <- "Exact"
combined_system <- rbind(system_df, exact_system_df)

# Melt for position and velocity plotting
combined_melted <- melt(combined_system, id.vars = c("time", "method"),
                       variable.name = "variable", value.name = "value")

# Plot 2: Harmonic oscillator position
p2 <- ggplot(subset(combined_melted, variable == "position"), 
             aes(x = time, y = value, color = method, linetype = method)) +
  geom_line(size = 1) +
  labs(title = "Harmonic Oscillator: Position",
       x = "Time", y = "Position", color = "Method", linetype = "Method") +
  theme_minimal()

# Plot 3: Phase portrait
p3 <- ggplot(combined_system, aes(x = position, y = velocity, color = method)) +
  geom_path(size = 1) +
  coord_equal() +
  labs(title = "Phase Portrait",
       x = "Position", y = "Velocity", color = "Method") +
  theme_minimal()

# Plot 4: Error analysis
# Interpolate exact solution at Euler time points
exact_interp <- approx(exact_system_df$time, exact_system_df$position, 
                      xout = system_df$time)$y
error_df <- data.frame(
  time = system_df$time,
  error = abs(system_df$position - exact_interp)
)

p4 <- ggplot(error_df, aes(x = time, y = error)) +
  geom_line(color = "blue", size = 1) +
  scale_y_log10() +
  labs(title = "Error Growth in Euler Method",
       x = "Time", y = "Absolute Error (log scale)") +
  theme_minimal()

# Display plots
grid.arrange(p1, p2, p3, p4, ncol = 2)

# Stability analysis function
test_stability <- function() {
  # Test stability of Euler method for different step sizes
  stiff_equation <- function(t, y) {
    return(-50 * y)  # Stiff equation
  }
  
  step_sizes <- c(0.1, 0.04, 0.02, 0.01)
  stability_data <- data.frame()
  
  for (h in step_sizes) {
    tryCatch({
      solution <- euler_method(stiff_equation, c(0, 1), 1, h)
      temp_df <- data.frame(t = solution$t, y = solution$y, 
                           h = as.character(h), stable = TRUE)
      stability_data <- rbind(stability_data, temp_df)
    }, error = function(e) {
      # Handle unstable cases
      temp_df <- data.frame(t = NA, y = NA, 
                           h = as.character(h), stable = FALSE)
      stability_data <- rbind(stability_data, temp_df)
    })
  }
  
  # Add exact solution
  t_exact_stiff <- seq(0, 1, length.out = 1000)
  y_exact_stiff <- exp(-50 * t_exact_stiff)
  exact_stiff_df <- data.frame(t = t_exact_stiff, y = y_exact_stiff, 
                              h = "Exact", stable = TRUE)
  stability_data <- rbind(stability_data, exact_stiff_df)
  
  # Plot stability analysis
  p_stability <- ggplot(subset(stability_data, stable == TRUE), 
                       aes(x = t, y = y, color = h)) +
    geom_line(size = 1) +
    labs(title = "Stability of Euler Method for Stiff Equation",
         x = "Time", y = "y(t)", color = "Step Size") +
    theme_minimal()
  
  print(p_stability)
}

# Run stability test
test_stability()

cat("Euler method implementation complete!\n")
</code>
                            </pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Runge-Kutta Methods -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4">4.2 Runge-Kutta Methods</h3>
                
                <div class="bg-green-50 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold text-green-800 mb-2">Fourth-Order Runge-Kutta (RK4)</h4>
                    <p class="text-gray-700">
                        RK4 provides higher accuracy than Euler's method by using four slope estimates:
                    </p>
                    <div class="font-mono bg-white p-2 rounded mt-2 text-sm">
                        $k_1 = h \cdot f(t_n, y_n)$<br>
                        $k_2 = h \cdot f(t_n + h/2, y_n + k_1/2)$<br>
                        $k_3 = h \cdot f(t_n + h/2, y_n + k_2/2)$<br>
                        $k_4 = h \cdot f(t_n + h, y_n + k_3)$<br>
                        $y_{n+1} = y_n + \frac{1}{6}(k_1 + 2k_2 + 2k_3 + k_4)$
                    </div>
                </div>

                <div class="comparison-grid">
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fab fa-python mr-2"></i>Python RK4 Implementation
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Runge-Kutta 4th Order Implementation
import numpy as np
import matplotlib.pyplot as plt

def rk4_method(f, t_span, y0, h, return_all=True):
    """
    Solve ODE using 4th-order Runge-Kutta method
    
    Parameters same as euler_method
    """
    t_start, t_end = t_span
    n_steps = int((t_end - t_start) / h)
    
    # Handle both scalar and vector initial conditions
    y0 = np.atleast_1d(y0)
    dim = len(y0)
    
    # Initialize arrays
    if return_all:
        t_array = np.zeros(n_steps + 1)
        y_array = np.zeros((n_steps + 1, dim))
        t_array[0] = t_start
        y_array[0] = y0
    
    t_current = t_start
    y_current = y0.copy()
    
    # RK4 iterations
    for i in range(n_steps):
        # Calculate the four slopes
        k1 = h * np.atleast_1d(f(t_current, y_current))
        k2 = h * np.atleast_1d(f(t_current + h/2, y_current + k1/2))
        k3 = h * np.atleast_1d(f(t_current + h/2, y_current + k2/2))
        k4 = h * np.atleast_1d(f(t_current + h, y_current + k3))
        
        # Weighted average
        y_next = y_current + (k1 + 2*k2 + 2*k3 + k4) / 6
        t_next = t_current + h
        
        if return_all:
            t_array[i + 1] = t_next
            y_array[i + 1] = y_next
        
        t_current = t_next
        y_current = y_next
    
    if not return_all:
        return t_current, y_current
    
    if dim == 1:
        return t_array, y_array.flatten()
    else:
        return t_array, y_array

# Compare methods on harmonic oscillator
def compare_methods():
    """Compare Euler, RK2, and RK4 methods"""
    
    def harmonic_oscillator(t, y):
        return np.array([y[1], -y[0]])
    
    # Parameters
    t_span = (0, 4*np.pi)
    y0 = np.array([1, 0])
    h = 0.2  # Relatively large step for comparison
    
    # Solve with different methods
    t_euler, y_euler = euler_method(harmonic_oscillator, t_span, y0, h)
    t_rk4, y_rk4 = rk4_method(harmonic_oscillator, t_span, y0, h)
    
    # Exact solution
    t_exact = np.linspace(0, 4*np.pi, 1000)
    y_exact = np.cos(t_exact)
    
    # Plot comparison
    plt.figure(figsize=(15, 5))
    
    # Position comparison
    plt.subplot(1, 3, 1)
    plt.plot(t_exact, y_exact, 'k-', linewidth=2, label='Exact')
    plt.plot(t_euler, y_euler[:, 0], 'r--o', label='Euler', markersize=4)
    plt.plot(t_rk4, y_rk4[:, 0], 'b--s', label='RK4', markersize=4)
    plt.xlabel('Time')
    plt.ylabel('Position')
    plt.title(f'Method Comparison (h = {h})')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Phase portrait
    plt.subplot(1, 3, 2)
    circle_theta = np.linspace(0, 2*np.pi, 100)
    plt.plot(np.cos(circle_theta), -np.sin(circle_theta), 'k-', linewidth=2, label='Exact')
    plt.plot(y_euler[:, 0], y_euler[:, 1], 'r--o', label='Euler', markersize=4)
    plt.plot(y_rk4[:, 0], y_rk4[:, 1], 'b--s', label='RK4', markersize=4)
    plt.xlabel('Position')
    plt.ylabel('Velocity')
    plt.title('Phase Portrait')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    
    # Error comparison
    plt.subplot(1, 3, 3)
    # Interpolate exact solution
    y_exact_euler = np.cos(t_euler)
    y_exact_rk4 = np.cos(t_rk4)
    
    error_euler = np.abs(y_euler[:, 0] - y_exact_euler)
    error_rk4 = np.abs(y_rk4[:, 0] - y_exact_rk4)
    
    plt.semilogy(t_euler, error_euler, 'r--o', label='Euler', markersize=4)
    plt.semilogy(t_rk4, error_rk4, 'b--s', label='RK4', markersize=4)
    plt.xlabel('Time')
    plt.ylabel('Absolute Error')
    plt.title('Error Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Adaptive step size RK4
def adaptive_rk4(f, t_span, y0, tol=1e-6, h_init=0.01, h_min=1e-8, h_max=1.0):
    """
    RK4 with adaptive step size control
    """
    t_start, t_end = t_span
    y0 = np.atleast_1d(y0)
    
    # Initialize
    t_current = t_start
    y_current = y0.copy()
    h = h_init
    
    t_values = [t_current]
    y_values = [y_current.copy()]
    
    while t_current < t_end:
        # Ensure we don't overshoot
        if t_current + h > t_end:
            h = t_end - t_current
        
        # Take one step of size h
        _, y_full = rk4_method(f, (t_current, t_current + h), y_current, h, return_all=False)
        
        # Take two steps of size h/2
        _, y_half1 = rk4_method(f, (t_current, t_current + h/2), y_current, h/2, return_all=False)
        _, y_half2 = rk4_method(f, (t_current + h/2, t_current + h), y_half1, h/2, return_all=False)
        
        # Estimate error
        error = np.max(np.abs(y_full - y_half2))
        
        if error <= tol:
            # Accept the step
            t_current += h
            y_current = y_half2.copy()  # Use more accurate solution
            t_values.append(t_current)
            y_values.append(y_current.copy())
            
            # Increase step size for next iteration
            h = min(h * min(2.0, (tol / error) ** 0.2), h_max)
        else:
            # Reject the step and reduce step size
            h = max(h * max(0.5, (tol / error) ** 0.2), h_min)
    
    return np.array(t_values), np.array(y_values)

# Test adaptive method
def test_adaptive():
    """Test adaptive step size method"""
    
    def stiff_harmonic(t, y):
        """Mix of fast and slow dynamics"""
        return np.array([y[1], -100*y[0] - 2*y[1]])
    
    # Compare fixed vs adaptive step size
    t_span = (0, 2)
    y0 = np.array([1, 0])
    
    # Fixed step size
    h_fixed = 0.001
    t_fixed, y_fixed = rk4_method(stiff_harmonic, t_span, y0, h_fixed)
    
    # Adaptive step size
    t_adaptive, y_adaptive = adaptive_rk4(stiff_harmonic, t_span, y0, tol=1e-6)
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(t_fixed, y_fixed[:, 0], 'b-', label=f'Fixed (h={h_fixed})', alpha=0.7)
    plt.plot(t_adaptive, y_adaptive[:, 0], 'ro', label='Adaptive', markersize=2)
    plt.xlabel('Time')
    plt.ylabel('Position')
    plt.title('Fixed vs Adaptive Step Size')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    step_sizes = np.diff(t_adaptive)
    plt.plot(t_adaptive[1:], step_sizes, 'g-o', markersize=3)
    plt.xlabel('Time')
    plt.ylabel('Step Size')
    plt.title('Adaptive Step Size Evolution')
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    
    plt.tight_layout()
    plt.show()
    
    print(f"Fixed method: {len(t_fixed)} points")
    print(f"Adaptive method: {len(t_adaptive)} points")

# Run comparisons
compare_methods()
test_adaptive()
print("Runge-Kutta implementation complete!")
</code>
                            </pre>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fas fa-chart-line mr-2"></i>R RK4 Implementation
                        </h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                            <pre class="code-content">
<code># Runge-Kutta 4th Order Implementation in R
library(ggplot2)
library(reshape2)
library(gridExtra)

rk4_method <- function(f, t_span, y0, h, return_all = TRUE) {
  # Solve ODE using 4th-order Runge-Kutta method
  # Parameters same as euler_method
  
  t_start <- t_span[1]
  t_end <- t_span[2]
  n_steps <- floor((t_end - t_start) / h)
  
  # Handle both scalar and vector initial conditions
  if (is.vector(y0) && length(y0) > 1) {
    dim <- length(y0)
  } else {
    dim <- 1
    y0 <- as.numeric(y0)
  }
  
  # Initialize arrays
  if (return_all) {
    t_array <- numeric(n_steps + 1)
    if (dim == 1) {
      y_array <- numeric(n_steps + 1)
    } else {
      y_array <- matrix(0, nrow = n_steps + 1, ncol = dim)
    }
    
    t_array[1] <- t_start
    if (dim == 1) {
      y_array[1] <- y0
    } else {
      y_array[1, ] <- y0
    }
  }
  
  t_current <- t_start
  y_current <- y0
  
  # RK4 iterations
  for (i in 1:n_steps) {
    # Calculate the four slopes
    k1 <- h * f(t_current, y_current)
    k2 <- h * f(t_current + h/2, y_current + k1/2)
    k3 <- h * f(t_current + h/2, y_current + k2/2)
    k4 <- h * f(t_current + h, y_current + k3)
    
    # Weighted average
    y_next <- y_current + (k1 + 2*k2 + 2*k3 + k4) / 6
    t_next <- t_current + h
    
    if (return_all) {
      t_array[i + 1] <- t_next
      if (dim == 1) {
        y_array[i + 1] <- y_next
      } else {
        y_array[i + 1, ] <- y_next
      }
    }
    
    t_current <- t_next
    y_current <- y_next
  }
  
  if (!return_all) {
    return(list(t = t_current, y = y_current))
  }
  
  return(list(t = t_array, y = y_array))
}

# Compare methods on harmonic oscillator
compare_methods <- function() {
  # Compare Euler and RK4 methods
  
  harmonic_oscillator <- function(t, y) {
    return(c(y[2], -y[1]))
  }
  
  # Parameters
  t_span <- c(0, 4*pi)
  y0 <- c(1, 0)
  h <- 0.2  # Relatively large step for comparison
  
  # Solve with different methods
  euler_sol <- euler_method(harmonic_oscillator, t_span, y0, h)
  rk4_sol <- rk4_method(harmonic_oscillator, t_span, y0, h)
  
  # Create data frames
  euler_df <- data.frame(
    time = euler_sol$t,
    position = euler_sol$y[, 1],
    velocity = euler_sol$y[, 2],
    method = "Euler"
  )
  
  rk4_df <- data.frame(
    time = rk4_sol$t,
    position = rk4_sol$y[, 1],
    velocity = rk4_sol$y[, 2],
    method = "RK4"
  )
  
  # Exact solution
  t_exact <- seq(0, 4*pi, length.out = 1000)
  exact_df <- data.frame(
    time = t_exact,
    position = cos(t_exact),
    velocity = -sin(t_exact),
    method = "Exact"
  )
  
  # Combine data
  combined_df <- rbind(euler_df, rk4_df, exact_df)
  
  # Plot 1: Position comparison
  p1 <- ggplot(combined_df, aes(x = time, y = position, color = method, linetype = method)) +
    geom_line(size = 1) +
    geom_point(data = subset(combined_df, method != "Exact"), size = 2) +
    labs(title = paste("Method Comparison (h =", h, ")"),
         x = "Time", y = "Position", color = "Method", linetype = "Method") +
    theme_minimal()
  
  # Plot 2: Phase portrait
  p2 <- ggplot(combined_df, aes(x = position, y = velocity, color = method)) +
    geom_path(size = 1) +
    coord_equal() +
    labs(title = "Phase Portrait",
         x = "Position", y = "Velocity", color = "Method") +
    theme_minimal()
  
  # Plot 3: Error comparison
  # Calculate errors
  euler_exact_pos <- approx(exact_df$time, exact_df$position, xout = euler_df$time)$y
  rk4_exact_pos <- approx(exact_df$time, exact_df$position, xout = rk4_df$time)$y
  
  error_df <- rbind(
    data.frame(time = euler_df$time, error = abs(euler_df$position - euler_exact_pos), method = "Euler"),
    data.frame(time = rk4_df$time, error = abs(rk4_df$position - rk4_exact_pos), method = "RK4")
  )
  
  p3 <- ggplot(error_df, aes(x = time, y = error, color = method)) +
    geom_line(size = 1) +
    geom_point(size = 2) +
    scale_y_log10() +
    labs(title = "Error Comparison",
         x = "Time", y = "Absolute Error (log scale)", color = "Method") +
    theme_minimal()
  
  # Display plots
  grid.arrange(p1, p2, p3, ncol = 1)
  
  return(list(euler = euler_df, rk4 = rk4_df, exact = exact_df, error = error_df))
}

# Adaptive step size RK4 (simplified version)
adaptive_rk4 <- function(f, t_span, y0, tol = 1e-6, h_init = 0.01, h_min = 1e-8, h_max = 1.0) {
  # RK4 with adaptive step size control
  
  t_start <- t_span[1]
  t_end <- t_span[2]
  
  if (is.vector(y0) && length(y0) > 1) {
    dim <- length(y0)
  } else {
    dim <- 1
    y0 <- as.numeric(y0)
  }
  
  # Initialize
  t_current <- t_start
  y_current <- y0
  h <- h_init
  
  t_values <- t_current
  if (dim == 1) {
    y_values <- y_current
  } else {
    y_values <- matrix(y_current, nrow = 1)
  }
  
  while (t_current < t_end) {
    # Ensure we don't overshoot
    if (t_current + h > t_end) {
      h <- t_end - t_current
    }
    
    # Take one step of size h
    sol_full <- rk4_method(f, c(t_current, t_current + h), y_current, h, return_all = FALSE)
    y_full <- sol_full$y
    
    # Take two steps of size h/2
    sol_half1 <- rk4_method(f, c(t_current, t_current + h/2), y_current, h/2, return_all = FALSE)
    y_half1 <- sol_half1$y
    sol_half2 <- rk4_method(f, c(t_current + h/2, t_current + h), y_half1, h/2, return_all = FALSE)
    y_half2 <- sol_half2$y
    
    # Estimate error
    if (dim == 1) {
      error <- abs(y_full - y_half2)
    } else {
      error <- max(abs(y_full - y_half2))
    }
    
    if (error <= tol) {
      # Accept the step
      t_current <- t_current + h
      y_current <- y_half2  # Use more accurate solution
      
      t_values <- c(t_values, t_current)
      if (dim == 1) {
        y_values <- c(y_values, y_current)
      } else {
        y_values <- rbind(y_values, y_current)
      }
      
      # Increase step size for next iteration
      h <- min(h * min(2.0, (tol / error)^0.2), h_max)
    } else {
      # Reject the step and reduce step size
      h <- max(h * max(0.5, (tol / error)^0.2), h_min)
    }
  }
  
  return(list(t = t_values, y = y_values))
}

# Test adaptive method
test_adaptive <- function() {
  # Test adaptive step size method
  
  stiff_harmonic <- function(t, y) {
    # Mix of fast and slow dynamics
    return(c(y[2], -100*y[1] - 2*y[2]))
  }
  
  # Compare fixed vs adaptive step size
  t_span <- c(0, 2)
  y0 <- c(1, 0)
  
  # Fixed step size
  h_fixed <- 0.001
  fixed_sol <- rk4_method(stiff_harmonic, t_span, y0, h_fixed)
  
  # Adaptive step size
  adaptive_sol <- adaptive_rk4(stiff_harmonic, t_span, y0, tol = 1e-6)
  
  # Create data frames
  fixed_df <- data.frame(
    time = fixed_sol$t,
    position = fixed_sol$y[, 1],
    method = "Fixed",
    h = h_fixed
  )
  
  adaptive_df <- data.frame(
    time = adaptive_sol$t,
    position = adaptive_sol$y[, 1],
    method = "Adaptive"
  )
  
  # Plot comparison
  p1 <- ggplot() +
    geom_line(data = fixed_df, aes(x = time, y = position, color = method), size = 1, alpha = 0.7) +
    geom_point(data = adaptive_df, aes(x = time, y = position, color = method), size = 2) +
    labs(title = "Fixed vs Adaptive Step Size",
         x = "Time", y = "Position", color = "Method") +
    theme_minimal()
  
  # Step size evolution
  if (length(adaptive_sol$t) > 1) {
    step_sizes <- diff(adaptive_sol$t)
    step_df <- data.frame(
      time = adaptive_sol$t[-1],
      step_size = step_sizes
    )
    
    p2 <- ggplot(step_df, aes(x = time, y = step_size)) +
      geom_line(color = "green", size = 1) +
      geom_point(color = "green", size = 2) +
      scale_y_log10() +
      labs(title = "Adaptive Step Size Evolution",
           x = "Time", y = "Step Size (log scale)") +
      theme_minimal()
  } else {
    p2 <- ggplot() + labs(title = "Step size data insufficient")
  }
  
  # Display plots
  grid.arrange(p1, p2, ncol = 2)
  
  cat("Fixed method:", length(fixed_sol$t), "points\n")
  cat("Adaptive method:", length(adaptive_sol$t), "points\n")
  
  return(list(fixed = fixed_df, adaptive = adaptive_df))
}

# Run comparisons
comparison_results <- compare_methods()
adaptive_results <- test_adaptive()

cat("Runge-Kutta implementation complete!\n")
</code>
                            </pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 5: File I/O and Data Management -->
        <section class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-3xl font-bold text-blue-800 mb-6">
                <i class="fas fa-file-import mr-3"></i>5. File I/O and Data Management
            </h2>

            <p class="text-gray-600 mb-6">
                Efficient data handling is crucial for ODE projects involving parameter studies, 
                experimental data, and result archiving.
            </p>

            <div class="comparison-grid">
                <div>
                    <h3 class="text-xl font-semibold text-blue-600 mb-4">
                        <i class="fab fa-python mr-2"></i>Python Data Management
                    </h3>
                    <div class="code-container">
                        <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                        <pre class="code-content">
<code># File I/O and Data Management for ODE Projects
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import json
import pickle
from pathlib import Path
import h5py  # For HDF5 files (large datasets)

# Create project directory structure
def setup_project_directory():
    """Create organized directory structure for ODE project"""
    base_dir = Path("ode_project")
    directories = [
        "data/raw",
        "data/processed", 
        "results/figures",
        "results/solutions",
        "parameters",
        "scripts"
    ]
    
    for dir_path in directories:
        (base_dir / dir_path).mkdir(parents=True, exist_ok=True)
    
    print(f"Created project structure in {base_dir.absolute()}")
    return base_dir

# Parameter management
class ParameterManager:
    """Manage ODE parameters with JSON serialization"""
    
    def __init__(self, param_file="parameters/default_params.json"):
        self.param_file = Path(param_file)
        self.params = {}
    
    def set_parameters(self, **kwargs):
        """Set parameters"""
        self.params.update(kwargs)
    
    def save_parameters(self):
        """Save parameters to JSON file"""
        self.param_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.param_file, 'w') as f:
            json.dump(self.params, f, indent=2, default=str)
        print(f"Parameters saved to {self.param_file}")
    
    def load_parameters(self):
        """Load parameters from JSON file"""
        if self.param_file.exists():
            with open(self.param_file, 'r') as f:
                self.params = json.load(f)
            print(f"Parameters loaded from {self.param_file}")
        else:
            print(f"Parameter file {self.param_file} not found")
    
    def get_param(self, key, default=None):
        """Get parameter value"""
        return self.params.get(key, default)

# Solution data management
class SolutionManager:
    """Manage ODE solutions with various file formats"""
    
    def __init__(self, base_dir="results"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
    
    def save_solution_csv(self, t, y, filename, metadata=None):
        """Save solution to CSV with metadata"""
        # Prepare data
        if y.ndim == 1:
            data = pd.DataFrame({'time': t, 'y': y})
        else:
            columns = ['time'] + [f'y{i}' for i in range(y.shape[1])]
            data = pd.DataFrame(np.column_stack([t, y]), columns=columns)
        
        # Add metadata as comments
        filepath = self.base_dir / f"{filename}.csv"
        with open(filepath, 'w') as f:
            if metadata:
                f.write(f"# Metadata: {json.dumps(metadata, default=str)}\n")
            data.to_csv(f, index=False)
        
        print(f"Solution saved to {filepath}")
        return filepath
    
    def load_solution_csv(self, filename):
        """Load solution from CSV"""
        filepath = self.base_dir / f"{filename}.csv"
        
        # Read metadata if present
        metadata = {}
        with open(filepath, 'r') as f:
            first_line = f.readline()
            if first_line.startswith('# Metadata:'):
                metadata = json.loads(first_line[12:])
        
        # Read data
        data = pd.read_csv(filepath, comment='#')
        
        return data, metadata
    
    def save_solution_hdf5(self, t, y, filename, metadata=None):
        """Save solution to HDF5 for large datasets"""
        filepath = self.base_dir / f"{filename}.h5"
        
        with h5py.File(filepath, 'w') as f:
            f.create_dataset('time', data=t)
            f.create_dataset('solution', data=y)
            
            if metadata:
                for key, value in metadata.items():
                    f.attrs[key] = str(value)
        
        print(f"Solution saved to {filepath}")
        return filepath
    
    def load_solution_hdf5(self, filename):
        """Load solution from HDF5"""
        filepath = self.base_dir / f"{filename}.h5"
        
        with h5py.File(filepath, 'r') as f:
            t = f['time'][:]
            y = f['solution'][:]
            metadata = dict(f.attrs)
        
        return t, y, metadata

# Example: Parameter study workflow
def parameter_study_example():
    """Complete workflow for parameter study"""
    
    # Setup project
    base_dir = setup_project_directory()
    
    # Initialize managers
    param_mgr = ParameterManager("ode_project/parameters/harmonic_params.json")
    sol_mgr = SolutionManager("ode_project/results/solutions")
    
    # Define harmonic oscillator
    def damped_harmonic(t, y, gamma, omega):
        return np.array([y[1], -2*gamma*y[1] - omega**2*y[0]])
    
    # Parameter ranges
    gamma_values = np.linspace(0.1, 2.0, 10)
    omega_values = [1.0, 2.0, 3.0]
    
    results_summary = []
    
    for omega in omega_values:
        for gamma in gamma_values:
            # Set parameters
            param_mgr.set_parameters(
                gamma=gamma,
                omega=omega,
                t_span=[0, 10],
                initial_conditions=[1, 0],
                solver='rk4',
                step_size=0.01
            )
            
            # Solve ODE
            t_span = (0, 10)
            y0 = np.array([1, 0])
            h = 0.01
            
            t_sol, y_sol = rk4_method(
                lambda t, y: damped_harmonic(t, y, gamma, omega), 
                t_span, y0, h
            )
            
            # Create unique filename
            filename = f"harmonic_g{gamma:.2f}_w{omega:.1f}"
            
            # Save solution
            metadata = {
                'gamma': gamma,
                'omega': omega,
                'solver': 'rk4',
                'step_size': h,
                'initial_conditions': y0.tolist()
            }
            
            sol_mgr.save_solution_csv(t_sol, y_sol, filename, metadata)
            
            # Calculate summary statistics
            max_amplitude = np.max(np.abs(y_sol[:, 0]))
            decay_rate = -np.log(max_amplitude / 1.0) / 10  # Rough estimate
            
            results_summary.append({
                'gamma': gamma,
                'omega': omega,
                'max_amplitude': max_amplitude,
                'decay_rate': decay_rate,
                'filename': filename
            })
    
    # Save summary
    summary_df = pd.DataFrame(results_summary)
    summary_df.to_csv(base_dir / "results" / "parameter_study_summary.csv", index=False)
    
    # Create visualization
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    for omega in omega_values:
        subset = summary_df[summary_df['omega'] == omega]
        axes[0].plot(subset['gamma'], subset['max_amplitude'], 'o-', label=f'ω = {omega}')
        axes[1].plot(subset['gamma'], subset['decay_rate'], 's-', label=f'ω = {omega}')
    
    axes[0].set_xlabel('Damping (γ)')
    axes[0].set_ylabel('Maximum Amplitude')
    axes[0].set_title('Amplitude vs Damping')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    axes[1].set_xlabel('Damping (γ)')
    axes[1].set_ylabel('Decay Rate')
    axes[1].set_title('Decay Rate vs Damping')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(base_dir / "results" / "figures" / "parameter_study.png", dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Parameter study complete. Results saved in {base_dir}")
    return summary_df

# Data import/export utilities
def import_experimental_data(filename):
    """Import experimental data for comparison with ODE solutions"""
    
    # Example: Import CSV data with time and measurement columns
    try:
        data = pd.read_csv(filename)
        
        # Validate required columns
        required_cols = ['time', 'measurement']
        if not all(col in data.columns for col in required_cols):
            raise ValueError(f"Data must contain columns: {required_cols}")
        
        # Clean data
        data = data.dropna()
        data = data.sort_values('time')
        
        print(f"Imported {len(data)} data points from {filename}")
        return data
    
    except Exception as e:
        print(f"Error importing data: {e}")
        return None

def export_for_publication(t, y, filename, title="ODE Solution", xlabel="Time", ylabel="Solution"):
    """Export high-quality figures for publication"""
    
    plt.figure(figsize=(10, 6))
    plt.rcParams.update({'font.size': 14, 'font.family': 'serif'})
    
    if y.ndim == 1:
        plt.plot(t, y, 'b-', linewidth=2)
    else:
        colors = plt.cm.Set1(np.linspace(0, 1, y.shape[1]))
        for i in range(y.shape[1]):
            plt.plot(t, y[:, i], color=colors[i], linewidth=2, label=f'y{i+1}')
        plt.legend()
    
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.title(title)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # Save in multiple formats
    for fmt in ['png', 'pdf', 'svg']:
        plt.savefig(f"{filename}.{fmt}", dpi=300, bbox_inches='tight')
    
    plt.show()
    print(f"Publication-quality figures saved as {filename}.{{png,pdf,svg}}")

# Run example
if __name__ == "__main__":
    # Example workflow
    summary = parameter_study_example()
    print("\nParameter study summary:")
    print(summary.head())
</code>
                        </pre>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-blue-600 mb-4">
                        <i class="fas fa-chart-line mr-2"></i>R Data Management
                    </h3>
                    <div class="code-container">
                        <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                        <pre class="code-content">
<code># File I/O and Data Management for ODE Projects in R
library(deSolve)
library(ggplot2)
library(jsonlite)
library(readr)
library(readxl)
library(writexl)
library(reshape2)
library(gridExtra)

# Create project directory structure
setup_project_directory <- function(base_dir = "ode_project") {
  # Create organized directory structure for ODE project
  directories <- c(
    "data/raw",
    "data/processed", 
    "results/figures",
    "results/solutions",
    "parameters",
    "scripts"
  )
  
  for (dir_path in directories) {
    full_path <- file.path(base_dir, dir_path)
    dir.create(full_path, recursive = TRUE, showWarnings = FALSE)
  }
  
  cat("Created project structure in", base_dir, "\n")
  return(base_dir)
}

# Parameter management class (using R6 or simple list approach)
create_parameter_manager <- function(param_file = "parameters/default_params.json") {
  # Create parameter management functions
  
  params <- list()
  
  list(
    set_parameters = function(...) {
      new_params <- list(...)
      params <<- modifyList(params, new_params)
    },
    
    save_parameters = function() {
      dir.create(dirname(param_file), recursive = TRUE, showWarnings = FALSE)
      write_json(params, param_file, pretty = TRUE, auto_unbox = TRUE)
      cat("Parameters saved to", param_file, "\n")
    },
    
    load_parameters = function() {
      if (file.exists(param_file)) {
        params <<- read_json(param_file)
        cat("Parameters loaded from", param_file, "\n")
      } else {
        cat("Parameter file", param_file, "not found\n")
      }
    },
    
    get_param = function(key, default = NULL) {
      if (key %in% names(params)) {
        return(params[[key]])
      } else {
        return(default)
      }
    },
    
    get_all_params = function() {
      return(params)
    }
  )
}

# Solution data management
create_solution_manager <- function(base_dir = "results") {
  # Create solution management functions
  
  dir.create(base_dir, recursive = TRUE, showWarnings = FALSE)
  
  list(
    save_solution_csv = function(t, y, filename, metadata = NULL) {
      # Save solution to CSV with metadata
      
      # Prepare data
      if (is.vector(y)) {
        data <- data.frame(time = t, y = y)
      } else {
        col_names <- c("time", paste0("y", 1:ncol(y)))
        data <- data.frame(cbind(t, y))
        names(data) <- col_names
      }
      
      filepath <- file.path(base_dir, paste0(filename, ".csv"))
      
      # Write metadata as comments
      if (!is.null(metadata)) {
        metadata_json <- toJSON(metadata, auto_unbox = TRUE)
        writeLines(paste("#", metadata_json), filepath)
        write_csv(data, filepath, append = TRUE)
      } else {
        write_csv(data, filepath)
      }
      
      cat("Solution saved to", filepath, "\n")
      return(filepath)
    },
    
    load_solution_csv = function(filename) {
      # Load solution from CSV
      filepath <- file.path(base_dir, paste0(filename, ".csv"))
      
      # Read metadata if present
      metadata <- NULL
      first_line <- readLines(filepath, n = 1)
      if (startsWith(first_line, "#")) {
        metadata <- fromJSON(substring(first_line, 3))
        data <- read_csv(filepath, comment = "#", show_col_types = FALSE)
      } else {
        data <- read_csv(filepath, show_col_types = FALSE)
      }
      
      return(list(data = data, metadata = metadata))
    },
    
    save_solution_excel = function(t, y, filename, metadata = NULL) {
      # Save solution to Excel with multiple sheets
      
      # Prepare main data
      if (is.vector(y)) {
        main_data <- data.frame(time = t, y = y)
      } else {
        col_names <- c("time", paste0("y", 1:ncol(y)))
        main_data <- data.frame(cbind(t, y))
        names(main_data) <- col_names
      }
      
      filepath <- file.path(base_dir, paste0(filename, ".xlsx"))
      
      # Create list of sheets
      sheets <- list("Solution" = main_data)
      
      if (!is.null(metadata)) {
        metadata_df <- data.frame(
          Parameter = names(metadata),
          Value = sapply(metadata, as.character)
        )
        sheets[["Metadata"]] <- metadata_df
      }
      
      write_xlsx(sheets, filepath)
      cat("Solution saved to", filepath, "\n")
      return(filepath)
    }
  )
}

# Example: Parameter study workflow
parameter_study_example <- function() {
  # Complete workflow for parameter study
  
  # Setup project
  base_dir <- setup_project_directory()
  
  # Initialize managers
  param_mgr <- create_parameter_manager(file.path(base_dir, "parameters/harmonic_params.json"))
  sol_mgr <- create_solution_manager(file.path(base_dir, "results/solutions"))
  
  # Define damped harmonic oscillator
  damped_harmonic <- function(t, y, parameters) {
    with(as.list(c(y, parameters)), {
      dy1 <- y2
      dy2 <- -2*gamma*y2 - omega^2*y1
      list(c(dy1, dy2))
    })
  }
  
  # Parameter ranges
  gamma_values <- seq(0.1, 2.0, length.out = 10)
  omega_values <- c(1.0, 2.0, 3.0)
  
  results_summary <- data.frame()
  
  for (omega in omega_values) {
    for (gamma in gamma_values) {
      # Set parameters
      param_mgr$set_parameters(
        gamma = gamma,
        omega = omega,
        t_span = c(0, 10),
        initial_conditions = c(1, 0),
        solver = 'lsoda'
      )
      
      # Solve ODE
      times <- seq(0, 10, by = 0.01)
      initial_conditions <- c(y1 = 1, y2 = 0)
      parameters <- list(gamma = gamma, omega = omega)
      
      solution <- ode(y = initial_conditions, 
                     times = times,
                     func = damped_harmonic, 
                     parms = parameters,
                     method = "lsoda")
      
      # Create unique filename
      filename <- sprintf("harmonic_g%.2f_w%.1f", gamma, omega)
      
      # Save solution
      metadata <- list(
        gamma = gamma,
        omega = omega,
        solver = 'lsoda',
        initial_conditions = initial_conditions
      )
      
      sol_mgr$save_solution_csv(solution[, "time"], solution[, 2:3], filename, metadata)
      
      # Calculate summary statistics
      max_amplitude <- max(abs(solution[, "y1"]))
      # Rough decay rate estimate
      decay_rate <- -log(max_amplitude / 1.0) / 10
      
      results_summary <- rbind(results_summary, data.frame(
        gamma = gamma,
        omega = omega,
        max_amplitude = max_amplitude,
        decay_rate = decay_rate,
        filename = filename
      ))
    }
  }
  
  # Save summary
  write_csv(results_summary, file.path(base_dir, "results", "parameter_study_summary.csv"))
  
  # Create visualization
  p1 <- ggplot(results_summary, aes(x = gamma, y = max_amplitude, color = factor(omega))) +
    geom_line(size = 1) +
    geom_point(size = 3) +
    labs(title = "Amplitude vs Damping",
         x = "Damping (γ)", y = "Maximum Amplitude",
         color = "ω") +
    theme_minimal()
  
  p2 <- ggplot(results_summary, aes(x = gamma, y = decay_rate, color = factor(omega))) +
    geom_line(size = 1) +
    geom_point(size = 3) +
    labs(title = "Decay Rate vs Damping",
         x = "Damping (γ)", y = "Decay Rate",
         color = "ω") +
    theme_minimal()
  
  # Save plots
  fig_dir <- file.path(base_dir, "results", "figures")
  dir.create(fig_dir, recursive = TRUE, showWarnings = FALSE)
  
  combined_plot <- grid.arrange(p1, p2, ncol = 2)
  ggsave(file.path(fig_dir, "parameter_study.png"), combined_plot, 
         width = 15, height = 6, dpi = 300)
  
  cat("Parameter study complete. Results saved in", base_dir, "\n")
  return(results_summary)
}

# Data import/export utilities
import_experimental_data <- function(filename) {
  # Import experimental data for comparison with ODE solutions
  
  tryCatch({
    # Determine file type and read accordingly
    if (grepl("\\.csv$", filename, ignore.case = TRUE)) {
      data <- read_csv(filename, show_col_types = FALSE)
    } else if (grepl("\\.xlsx?$", filename, ignore.case = TRUE)) {
      data <- read_excel(filename)
    } else {
      stop("Unsupported file format. Use CSV or Excel files.")
    }
    
    # Validate required columns
    required_cols <- c("time", "measurement")
    if (!all(required_cols %in% names(data))) {
      stop(paste("Data must contain columns:", paste(required_cols, collapse = ", ")))
    }
    
    # Clean data
    data <- data[complete.cases(data), ]
    data <- data[order(data$time), ]
    
    cat("Imported", nrow(data), "data points from", filename, "\n")
    return(data)
    
  }, error = function(e) {
    cat("Error importing data:", e$message, "\n")
    return(NULL)
  })
}

export_for_publication <- function(t, y, filename, title = "ODE Solution", 
                                  xlabel = "Time", ylabel = "Solution") {
  # Export high-quality figures for publication
  
  # Prepare data
  if (is.vector(y)) {
    plot_data <- data.frame(time = t, y = y)
    p <- ggplot(plot_data, aes(x = time, y = y)) +
      geom_line(size = 1.5, color = "blue")
  } else {
    col_names <- paste0("y", 1:ncol(y))
    plot_data <- data.frame(time = t, y)
    names(plot_data) <- c("time", col_names)
    
    # Melt for multiple series
    plot_data_melted <- melt(plot_data, id.vars = "time", 
                            variable.name = "series", value.name = "value")
    
    p <- ggplot(plot_data_melted, aes(x = time, y = value, color = series)) +
      geom_line(size = 1.5) +
      labs(color = "Series")
  }
  
  # Customize plot for publication
  p <- p +
    labs(title = title, x = xlabel, y = ylabel) +
    theme_minimal() +
    theme(
      text = element_text(size = 14, family = "serif"),
      plot.title = element_text(size = 16, face = "bold"),
      axis.title = element_text(size = 14),
      axis.text = element_text(size = 12),
      legend.text = element_text(size = 12),
      panel.grid.minor = element_blank()
    )
  
  # Save in multiple formats
  for (fmt in c("png", "pdf", "svg")) {
    filename_fmt <- paste0(filename, ".", fmt)
    ggsave(filename_fmt, p, width = 10, height = 6, dpi = 300)
  }
  
  print(p)
  cat("Publication-quality figures saved as", paste0(filename, ".{png,pdf,svg}"), "\n")
}

# Batch processing utilities
process_multiple_solutions <- function(solution_dir, pattern = "*.csv") {
  # Process multiple solution files
  
  files <- list.files(solution_dir, pattern = pattern, full.names = TRUE)
  
  if (length(files) == 0) {
    cat("No files found matching pattern", pattern, "in", solution_dir, "\n")
    return(NULL)
  }
  
  results <- list()
  
  for (file in files) {
    filename <- tools::file_path_sans_ext(basename(file))
    
    tryCatch({
      # Load solution
      solution_data <- read_csv(file, show_col_types = FALSE)
      
      # Basic analysis
      if ("y" %in% names(solution_data)) {
        max_val <- max(abs(solution_data$y), na.rm = TRUE)
        min_val <- min(solution_data$y, na.rm = TRUE)
        final_val <- tail(solution_data$y, 1)
      } else {
        max_val <- max(abs(as.matrix(solution_data[, -1])), na.rm = TRUE)
        min_val <- min(as.matrix(solution_data[, -1]), na.rm = TRUE)
        final_val <- NA
      }
      
      results[[filename]] <- list(
        file = file,
        n_points = nrow(solution_data),
        max_value = max_val,
        min_value = min_val,
        final_value = final_val
      )
      
    }, error = function(e) {
      cat("Error processing", file, ":", e$message, "\n")
    })
  }
  
  # Convert to data frame
  results_df <- do.call(rbind, lapply(names(results), function(name) {
    data.frame(
      filename = name,
      n_points = results[[name]]$n_points,
      max_value = results[[name]]$max_value,
      min_value = results[[name]]$min_value,
      final_value = results[[name]]$final_value
    )
  }))
  
  cat("Processed", length(results), "solution files\n")
  return(results_df)
}

# Run example workflow
if (interactive()) {
  # Example workflow
  summary <- parameter_study_example()
  cat("\nParameter study summary:\n")
  print(head(summary))
}

cat("R data management utilities loaded successfully!\n")
</code>
                        </pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Practical Exercises -->
        <section class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-3xl font-bold text-blue-800 mb-6">
                <i class="fas fa-tasks mr-3"></i>6. Practical Exercises
            </h2>

            <div class="space-y-6">
                <!-- Exercise 1 -->
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-6">
                    <h3 class="text-xl font-semibold text-yellow-800 mb-3">
                        <i class="fas fa-dumbbell mr-2"></i>Exercise 1: Method Comparison
                    </h3>
                    <p class="text-gray-700 mb-4">
                        Compare Euler and RK4 methods on the equation $\frac{dy}{dt} = -y + \sin(t)$ with $y(0) = 0$.
                    </p>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold mb-2">Tasks:</h4>
                        <ul class="list-disc ml-6 space-y-2 text-sm">
                            <li>Implement both methods with step size $h = 0.1$</li>
                            <li>Solve from $t = 0$ to $t = 5$</li>
                            <li>Compare with analytical solution: $y(t) = \frac{1}{2}(\sin t - \cos t + e^{-t})$</li>
                            <li>Plot solutions and error vs time</li>
                            <li>Calculate maximum absolute error for each method</li>
                        </ul>
                    </div>
                </div>

                <!-- Exercise 2 -->
                <div class="bg-green-50 border-l-4 border-green-400 p-6">
                    <h3 class="text-xl font-semibold text-green-800 mb-3">
                        <i class="fas fa-project-diagram mr-2"></i>Exercise 2: System of ODEs
                    </h3>
                    <p class="text-gray-700 mb-4">
                        Solve the predator-prey system: $\frac{dx}{dt} = ax - bxy$, $\frac{dy}{dt} = -cy + dxy$
                    </p>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold mb-2">Parameters:</h4>
                        <ul class="list-disc ml-6 space-y-2 text-sm">
                            <li>$a = 1.0$ (prey growth rate)</li>
                            <li>$b = 0.5$ (predation rate)</li>
                            <li>$c = 0.75$ (predator death rate)</li>
                            <li>$d = 0.25$ (predator efficiency)</li>
                            <li>Initial conditions: $x(0) = 2$, $y(0) = 1$</li>
                            <li>Time span: $t \in [0, 20]$</li>
                        </ul>
                        <h4 class="font-semibold mb-2 mt-4">Tasks:</h4>
                        <ul class="list-disc ml-6 space-y-2 text-sm">
                            <li>Plot time series for both populations</li>
                            <li>Create phase portrait (x vs y)</li>
                            <li>Vary initial conditions and observe trajectories</li>
                        </ul>
                    </div>
                </div>

                <!-- Exercise 3 -->
                <div class="bg-purple-50 border-l-4 border-purple-400 p-6">
                    <h3 class="text-xl font-semibold text-purple-800 mb-3">
                        <i class="fas fa-chart-area mr-2"></i>Exercise 3: Parameter Study
                    </h3>
                    <p class="text-gray-700 mb-4">
                        Study the Van der Pol oscillator: $\frac{d^2y}{dt^2} - \mu(1-y^2)\frac{dy}{dt} + y = 0$
                    </p>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold mb-2">Parameters:</h4>
                        <ul class="list-disc ml-6 space-y-2 text-sm">
                            <li>$\mu$ values: 0.1, 1.0, 3.0, 10.0 (damping parameter)</li>
                            <li>Initial conditions: $y(0) = 2$, $\frac{dy}{dt}(0) = 0$</li>
                            <li>Time span: $t \in [0, 30]$</li>
                        </ul>
                        <h4 class="font-semibold mb-2 mt-4">Tasks:</h4>
                        <ul class="list-disc ml-6 space-y-2 text-sm">
                            <li>Convert the second-order ODE to a first-order system</li>
                            <li>Vary $\mu$ and observe the transition from linear to non-linear behavior</li>
                            <li>Plot time series for different $\mu$ values on the same graph</li>
                            <li>Create phase portraits (y vs dy/dt) for each $\mu$ value</li>
                            <li>Identify and analyze limit cycles for $\mu > 0$</li>
                            <li>Compare solutions with different initial conditions</li>
                            <li>Discuss the physical meaning of the parameter $\mu$</li>
                        </ul>
                        <h4 class="font-semibold mb-2 mt-4">Expected Observations:</h4>
                        <ul class="list-disc ml-6 space-y-2 text-sm">
                            <li>For $\mu = 0$: Simple harmonic oscillator</li>
                            <li>For small $\mu > 0$: Nearly sinusoidal oscillations</li>
                            <li>For large $\mu$: Relaxation oscillations with distinct fast/slow phases</li>
                            <li>All non-zero $\mu$ cases should converge to limit cycles</li>
                        </ul>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        function copyCode(button) {
            const codeBlock = button.nextElementSibling;
            const code = codeBlock.textContent;
            
            navigator.clipboard.writeText(code).then(function() {
                button.textContent = 'Copied!';
                setTimeout(() => {
                    button.textContent = 'Copy';
                }, 2000);
            });
        }

        function showCode(language, containerId) {
            const container = document.getElementById(containerId);
            const tabs = container.querySelectorAll('.language-tab');
            const contents = container.querySelectorAll('.code-content');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            contents.forEach(content => content.classList.add('hidden'));
            
            document.querySelector(`[onclick="showCode('${language}', '${containerId}')"]`).classList.add('active');
            document.getElementById(`${language}-${containerId}`).classList.remove('hidden');
        }
    </script>
</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDqyGZq9Dq41IApgbKPr9fOneaMb8JCKwb5wkV4s4ibLht15B9xB12b4ejXVpojuYMRmf28guO%2FiG4y7M7TlpmVdOlqBq%2B3TBGAVvg%2FhYGyNhhCDrfOs%2B0ANr%2BBQJkWv9a9QkFd%2BDs%2FRK6egJiSUk7qFVcKXYiKHzaC9ZeN8A4bPq0bTPx%2F0fcGbEHTU6lOGvh8drkIqgxejq%2FrDSgMnSwNKWiLulSu4nn2Z4YxnZmf6ESdbpjEEwDEALgwvD%2ByRxHXgKkrX2n3qQ%2Fle72KdhTT4lKuJBAIJYb0rw%2B56RxT5H7Pv77rVSmLcoeV2B3tJHa2tDIDIybT%2B1va8pgIqiVwfUffo4Z1hbw8vtvG2Cx9vfZAU82wvGpWT3p6yn%2Bg9vq0b8w%2Bu5%2FZUsVPs0f0jqOeXyy8RLf7alKU8UpFW%2BKRfNnH4uSgdNx%2FRCs3g0OWnBTZ0Oxk8TQmH2AtgV08xJgxEvfCmhGpyzHcIO2jUtX4FuzeDx2UNmdAm%2FqV5oixamRbprIsNqd4qMKENfAvtRqc7vuYNzfxldos4uVq3JlREk";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDqyGZq9Dq41IApgbKPr9fOneaMb8JCKwb5wkV4s4ibLht15B9xB12b4ejXVpojuYMRmf28guO/iG4y7M7TlpmVdOlqBq+3TBGAVvg/hYGyNhhCDrfOs+0ANr+BQJkWv9a9QkFd+Ds/RK6egJiSUk7qFVcKXYiKHzaC9ZeN8A4bPq0bTPx/0fcGbEHTU6lOGvh8drkIqgxejq/rDSgMnSwNKWiLulSu4nn2Z4YxnZmf6ESdbpjEEwDEALgwvD+yRxHXgKkrX2n3qQ/le72KdhTT4lKuJBAIJYb0rw+56RxT5H7Pv77rVSmLcoeV2B3tJHa2tDIDIybT+1va8pgIqiVwfUffo4Z1hbw8vtvG2Cx9vfZAU82wvGpWT3p6yn+g9vq0b8w+u5/ZUsVPs0f0jqOeXyy8RLf7alKU8UpFW+KRfNnH4uSgdNx/RCs3g0OWnBTZ0Oxk8TQmH2AtgV08xJgxEvfCmhGpyzHcIO2jUtX4FuzeDx2UNmdAm/qV5oixamRbprIsNqd4qMKENfAvtRqc7vuYNzfxldos4uVq3JlREk";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    