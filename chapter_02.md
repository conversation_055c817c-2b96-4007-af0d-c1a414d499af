---
output:
  word_document: default
  html_document: default
---
<div class="max-w-6xl mx-auto p-8 bg-white min-h-screen">

<div class="mb-12 text-center border-b-4 border-blue-600 pb-6">

#  Chapter 2: Review of Trigonometry

Comprehensive ODE Tutorial - Part 1

<div class="mt-4 flex justify-center space-x-6 text-sm text-gray-500">

Mathematical Foundations Python & R Integration Visualization Tools

</div>

</div>

## Table of Contents

<div class="grid md:grid-cols-2 gap-4">

- <a href="#introduction" class="text-blue-600 hover:underline">2.1 Introduction to Trigonometry</a>
- <a href="#unit-circle" class="text-blue-600 hover:underline">2.2 The Unit Circle</a>
- <a href="#basic-functions" class="text-blue-600 hover:underline">2.3 Basic Trigonometric Functions</a>
- <a href="#identities" class="text-blue-600 hover:underline">2.4 Fundamental Identities</a>

<!-- -->

- <a href="#graphing" class="text-blue-600 hover:underline">2.5 Graphing Trigonometric Functions</a>
- <a href="#applications" class="text-blue-600 hover:underline">2.6 Applications to ODEs</a>
- <a href="#programming" class="text-blue-600 hover:underline">2.7 Programming Implementation</a>
- <a href="#exercises" class="text-blue-600 hover:underline">2.8 Exercises and Projects</a>

</div>

<div id="introduction" class="section mb-12">

## 2.1 Introduction to Trigonometry

<div class="bg-green-50 p-6 rounded-lg border-l-4 border-green-500 mb-8">

### Why Trigonometry in Differential Equations?

Trigonometric functions are fundamental to differential equations because they naturally arise as solutions to many physical systems involving oscillatory behavior. From simple harmonic motion to complex wave phenomena, understanding trigonometry is essential for modeling periodic and oscillatory systems.

</div>

<div class="definition-box p-6 rounded-lg mb-8">

### Definition: Trigonometric Functions

For a right triangle with angle $\theta$, opposite side $o$, adjacent side $a$, and hypotenuse $h$:

<div class="bg-white bg-opacity-20 p-4 rounded">

<div class="grid md:grid-cols-2 gap-4 text-center">

<div>

$$\sin \theta = \frac{o}{h}$$ $$\cos \theta = \frac{a}{h}$$ $$\tan \theta = \frac{o}{a}$$

</div>

<div>

$$\csc \theta = \frac{h}{o} = \frac{1}{\sin \theta}$$ $$\sec \theta = \frac{h}{a} = \frac{1}{\cos \theta}$$ $$\cot \theta = \frac{a}{o} = \frac{1}{\tan \theta}$$

</div>

</div>

</div>

</div>

<div class="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-500">

### Historical Context

Trigonometry originated from astronomical observations and navigation needs. The word "trigonometry" comes from Greek words meaning "triangle measurement." Ancient civilizations used trigonometric concepts to track celestial bodies and navigate across oceans.

</div>

</div>

<div id="unit-circle" class="section mb-12">

## 2.2 The Unit Circle

<div class="theorem-box p-6 rounded-lg mb-8">

### The Unit Circle Definition

The unit circle is a circle with radius 1 centered at the origin. For any angle $\theta$ measured from the positive x-axis:

<div class="bg-white bg-opacity-20 p-4 rounded text-center">

$$\cos \theta = x\text{-coordinate}$$ $$\sin \theta = y\text{-coordinate}$$ $$x^2 + y^2 = 1 \quad \text{(Pythagorean Identity)}$$

</div>

</div>

<div class="grid lg:grid-cols-2 gap-8 mb-8">

<div>

### Unit Circle Visualization

<div class="chart-container">

</div>

</div>

<div>

### Key Angles and Values

<div class="bg-gray-50 p-4 rounded-lg">

| Angle (°) | Angle (rad) | cos θ | sin θ |
|-----------|-------------|-------|-------|
| 0°        | 0           | 1     | 0     |
| 30°       | π/6         | √3/2  | 1/2   |
| 45°       | π/4         | √2/2  | √2/2  |
| 60°       | π/3         | 1/2   | √3/2  |
| 90°       | π/2         | 0     | 1     |

</div>

</div>

</div>

<div class="example-box p-6 rounded-lg">

### Example: Finding Coordinates on Unit Circle

Find the coordinates of the point on the unit circle corresponding to $\theta = 2\pi/3$.

<div class="bg-white bg-opacity-20 p-4 rounded">

**Solution:**

$\theta = 2\pi/3 = 120°$ is in the second quadrant.

Reference angle: $\pi - 2\pi/3 = \pi/3$

$\cos(2\pi/3) = -\cos(\pi/3) = -1/2$

$\sin(2\pi/3) = \sin(\pi/3) = \sqrt{3}/2$

**Coordinates:** $(-1/2, \sqrt{3}/2)$

</div>

</div>

</div>

<div id="basic-functions" class="section mb-12">

## 2.3 Basic Trigonometric Functions

<div class="grid lg:grid-cols-2 gap-8 mb-8">

<div>

### Sine Function

<div class="bg-blue-50 p-4 rounded-lg mb-4">

- **Domain:** $(-\infty, \infty)$
- **Range:** $[-1, 1]$
- **Period:** $2\pi$
- **Amplitude:** 1
- **Odd Function:** $\sin(-x) = -\sin(x)$

</div>

<div class="chart-container">

</div>

</div>

<div>

### Cosine Function

<div class="bg-red-50 p-4 rounded-lg mb-4">

- **Domain:** $(-\infty, \infty)$
- **Range:** $[-1, 1]$
- **Period:** $2\pi$
- **Amplitude:** 1
- **Even Function:** $\cos(-x) = \cos(x)$

</div>

<div class="chart-container">

</div>

</div>

</div>

<div class="theorem-box p-6 rounded-lg mb-8">

### General Form of Trigonometric Functions

The general forms of sine and cosine functions are:

<div class="bg-white bg-opacity-20 p-4 rounded text-center">

$$y = A \sin(Bx + C) + D$$ $$y = A \cos(Bx + C) + D$$

</div>

<div class="mt-4 grid md:grid-cols-2 gap-4">

- **A:** Amplitude (vertical stretch)
- **B:** Frequency factor (horizontal compression)

<!-- -->

- **C:** Phase shift (horizontal translation)
- **D:** Vertical shift

</div>

**Period:** $\frac{2\pi}{|B|}$

</div>

<div class="bg-gray-50 p-6 rounded-lg">

### Transformations Visualization

<div class="chart-container">

</div>

</div>

</div>

<div id="identities" class="section mb-12">

## 2.4 Fundamental Trigonometric Identities

<div class="grid lg:grid-cols-2 gap-8 mb-8">

<div class="theorem-box p-6 rounded-lg">

### Pythagorean Identities

<div class="bg-white bg-opacity-20 p-4 rounded space-y-3 text-center">

<div>

$$\sin^2 \theta + \cos^2 \theta = 1$$

</div>

<div>

$$1 + \tan^2 \theta = \sec^2 \theta$$

</div>

<div>

$$1 + \cot^2 \theta = \csc^2 \theta$$

</div>

</div>

</div>

<div class="definition-box p-6 rounded-lg">

### Reciprocal Identities

<div class="bg-white bg-opacity-20 p-4 rounded space-y-3 text-center">

<div>

$$\csc \theta = \frac{1}{\sin \theta}$$

</div>

<div>

$$\sec \theta = \frac{1}{\cos \theta}$$

</div>

<div>

$$\cot \theta = \frac{1}{\tan \theta}$$

</div>

</div>

</div>

</div>

<div class="example-box p-6 rounded-lg mb-8">

### Sum and Difference Formulas

<div class="bg-white bg-opacity-20 p-4 rounded">

<div class="grid md:grid-cols-2 gap-6">

<div class="text-center space-y-3">

#### Sine Formulas

<div>

$$\sin(A \pm B) = \sin A \cos B \pm \cos A \sin B$$

</div>

</div>

<div class="text-center space-y-3">

#### Cosine Formulas

<div>

$$\cos(A \pm B) = \cos A \cos B \mp \sin A \sin B$$

</div>

</div>

</div>

</div>

</div>

<div class="theorem-box p-6 rounded-lg">

### Double Angle Formulas

<div class="bg-white bg-opacity-20 p-4 rounded">

<div class="grid md:grid-cols-3 gap-4 text-center space-y-2">

<div>

#### Sine

$$\sin(2\theta) = 2\sin \theta \cos \theta$$

</div>

<div>

#### Cosine

$$\cos(2\theta) = \cos^2 \theta - \sin^2 \theta$$ $$= 2\cos^2 \theta - 1$$ $$= 1 - 2\sin^2 \theta$$

</div>

<div>

#### Tangent

$$\tan(2\theta) = \frac{2\tan \theta}{1 - \tan^2 \theta}$$

</div>

</div>

</div>

</div>

</div>

<div id="graphing" class="section mb-12">

## 2.5 Graphing Trigonometric Functions

<div class="bg-green-50 p-6 rounded-lg border-l-4 border-green-500 mb-8">

### Key Graphing Concepts

<div class="grid md:grid-cols-2 gap-6">

- **Amplitude:** Maximum displacement from equilibrium
- **Period:** Length of one complete cycle
- **Frequency:** Number of cycles per unit interval

<!-- -->

- **Phase Shift:** Horizontal displacement
- **Vertical Shift:** Up or down translation
- **Reflection:** About x-axis or y-axis

</div>

</div>

<div class="mb-8">

### Interactive Sine and Cosine Comparison

<div class="chart-container">

</div>

</div>

<div class="example-box p-6 rounded-lg mb-8">

### Example: Analyzing $y = 3\sin(2x - \pi/3) + 1$

<div class="bg-white bg-opacity-20 p-4 rounded">

<div class="grid md:grid-cols-2 gap-6">

<div>

**Given:** $y = 3\sin(2x - \pi/3) + 1$

**Amplitude:** $|A| = 3$

**Period:** $\frac{2\pi}{B} = \frac{2\pi}{2} = \pi$

**Phase Shift:** $\frac{C}{B} = \frac{\pi/3}{2} = \frac{\pi}{6}$ (right)

**Vertical Shift:** $D = 1$ (up)

</div>

<div>

**Range:** $[1-3, 1+3] = [-2, 4]$

**Key Points:**

- Start: $x = \pi/6$
- Maximum: $x = \pi/6 + \pi/4 = 5\pi/12$
- Zero: $x = \pi/6 + \pi/2 = 2\pi/3$
- Minimum: $x = \pi/6 + 3\pi/4 = 11\pi/12$

</div>

</div>

</div>

</div>

<div class="bg-gray-50 p-6 rounded-lg">

### Function Transformation Visualization

<div class="chart-container">

</div>

</div>

</div>

<div id="applications" class="section mb-12">

## 2.6 Applications to Differential Equations

<div class="theorem-box p-6 rounded-lg mb-8">

### Simple Harmonic Motion

The differential equation for simple harmonic motion is:

<div class="bg-white bg-opacity-20 p-4 rounded text-center">

$$\frac{d^2x}{dt^2} + \omega^2 x = 0$$

</div>

The general solution involves trigonometric functions:

<div class="bg-white bg-opacity-20 p-4 rounded text-center">

$$x(t) = A\cos(\omega t + \phi)$$

</div>

where $A$ is amplitude, $\omega$ is angular frequency, and $\phi$ is the phase constant.

</div>

<div class="grid lg:grid-cols-2 gap-8 mb-8">

<div class="definition-box p-6 rounded-lg">

### Physical Examples

- **Mass-Spring System:** $m\frac{d^2x}{dt^2} = -kx$
- **Pendulum:** $\frac{d^2\theta}{dt^2} = -\frac{g}{L}\sin\theta$
- **RLC Circuit:** $L\frac{d^2q}{dt^2} + R\frac{dq}{dt} + \frac{q}{C} = 0$
- **Wave Equation:** $\frac{\partial^2 u}{\partial t^2} = c^2\frac{\partial^2 u}{\partial x^2}$

</div>

<div class="example-box p-6 rounded-lg">

### Damped Oscillations

For a damped harmonic oscillator:

<div class="bg-white bg-opacity-20 p-4 rounded text-center">

$$\frac{d^2x}{dt^2} + 2\gamma\frac{dx}{dt} + \omega_0^2 x = 0$$

</div>

Solutions depend on the damping coefficient:

- **Underdamped:** $x(t) = Ae^{-\gamma t}\cos(\omega_d t + \phi)$
- **Critically damped:** $x(t) = (A + Bt)e^{-\gamma t}$
- **Overdamped:** $x(t) = Ae^{r_1 t} + Be^{r_2 t}$

</div>

</div>

<div class="bg-gray-50 p-6 rounded-lg">

### Oscillation Types Comparison

<div class="chart-container">

</div>

</div>

</div>

<div id="programming" class="section mb-12">

## 2.7 Programming Implementation

<div class="grid lg:grid-cols-2 gap-8 mb-8">

<div>

### Python Implementation

<div class="code-container">

Copy

``` bg-gray-900
import numpy as np
import matplotlib.pyplot as plt
from math import pi, sin, cos, tan

# Basic trigonometric function plotting
def plot_trig_functions():
    """Plot basic trigonometric functions"""
    x = np.linspace(-2*pi, 2*pi, 1000)
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Sine function
    axes[0,0].plot(x, np.sin(x), 'b-', linewidth=2, label='sin(x)')
    axes[0,0].set_title('Sine Function')
    axes[0,0].grid(True, alpha=0.3)
    axes[0,0].set_ylim(-1.5, 1.5)
    axes[0,0].legend()
    
    # Cosine function
    axes[0,1].plot(x, np.cos(x), 'r-', linewidth=2, label='cos(x)')
    axes[0,1].set_title('Cosine Function')
    axes[0,1].grid(True, alpha=0.3)
    axes[0,1].set_ylim(-1.5, 1.5)
    axes[0,1].legend()
    
    # Tangent function
    y_tan = np.tan(x)
    y_tan[np.abs(y_tan) > 10] = np.nan  # Remove discontinuities
    axes[1,0].plot(x, y_tan, 'g-', linewidth=2, label='tan(x)')
    axes[1,0].set_title('Tangent Function')
    axes[1,0].grid(True, alpha=0.3)
    axes[1,0].set_ylim(-5, 5)
    axes[1,0].legend()
    
    # Combined sine and cosine
    axes[1,1].plot(x, np.sin(x), 'b-', linewidth=2, label='sin(x)')
    axes[1,1].plot(x, np.cos(x), 'r-', linewidth=2, label='cos(x)')
    axes[1,1].set_title('Sine and Cosine Comparison')
    axes[1,1].grid(True, alpha=0.3)
    axes[1,1].set_ylim(-1.5, 1.5)
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.show()

# Unit circle visualization
def plot_unit_circle():
    """Create interactive unit circle plot"""
    fig, ax = plt.subplots(figsize=(8, 8))
    
    # Draw unit circle
    theta = np.linspace(0, 2*pi, 100)
    x_circle = np.cos(theta)
    y_circle = np.sin(theta)
    ax.plot(x_circle, y_circle, 'k-', linewidth=2)
    
    # Key angles
    key_angles = [0, pi/6, pi/4, pi/3, pi/2, 2*pi/3, 3*pi/4, 5*pi/6, pi,
                  7*pi/6, 5*pi/4, 4*pi/3, 3*pi/2, 5*pi/3, 7*pi/4, 11*pi/6]
    
    for angle in key_angles:
        x = cos(angle)
        y = sin(angle)
        ax.plot(x, y, 'ro', markersize=8)
        ax.plot([0, x], [0, y], 'b--', alpha=0.7)
        
        # Add angle labels
        label_x = 1.15 * x
        label_y = 1.15 * y
        angle_deg = int(np.degrees(angle))
        ax.text(label_x, label_y, f'{angle_deg}°', ha='center', va='center')
    
    ax.set_xlim(-1.5, 1.5)
    ax.set_ylim(-1.5, 1.5)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.axhline(y=0, color='k', linewidth=0.5)
    ax.axvline(x=0, color='k', linewidth=0.5)
    ax.set_title('Unit Circle with Key Angles')
    plt.show()

# Advanced transformations
def plot_transformations(A=1, B=1, C=0, D=0):
    """Plot y = A*sin(B*x + C) + D with parameters"""
    x = np.linspace(-2*pi, 2*pi, 1000)
    y_original = np.sin(x)
    y_transformed = A * np.sin(B * x + C) + D
    
    plt.figure(figsize=(12, 6))
    plt.plot(x, y_original, 'b--', alpha=0.7, label='y = sin(x)')
    plt.plot(x, y_transformed, 'r-', linewidth=2, 
             label=f'y = {A}sin({B}x + {C}) + {D}')
    
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.title(f'Transformation: A={A}, B={B}, C={C}, D={D}')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.show()

# Execute examples
if __name__ == "__main__":
    plot_trig_functions()
    plot_unit_circle()
    plot_transformations(A=2, B=0.5, C=pi/4, D=1)
```

</div>

</div>

<div>

### R Implementation

<div class="code-container">

Copy

``` bg-gray-900
# Load required libraries
library(ggplot2)
library(gridExtra)
library(dplyr)

# Basic trigonometric function plotting
plot_trig_functions <- function() {
  # Create data
  x <- seq(-2*pi, 2*pi, length.out = 1000)
  data <- data.frame(
    x = rep(x, 3),
    y = c(sin(x), cos(x), tan(pmax(pmin(x, pi/2 - 0.01), -pi/2 + 0.01))),
    func = rep(c("sin(x)", "cos(x)", "tan(x)"), each = length(x))
  )
  
  # Create plots
  p1 <- ggplot(data[data$func == "sin(x)", ], aes(x, y)) +
    geom_line(color = "blue", size = 1) +
    labs(title = "Sine Function", x = "x", y = "sin(x)") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
  
  p2 <- ggplot(data[data$func == "cos(x)", ], aes(x, y)) +
    geom_line(color = "red", size = 1) +
    labs(title = "Cosine Function", x = "x", y = "cos(x)") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
  
  # Combined plot
  combined_data <- data.frame(
    x = rep(x, 2),
    y = c(sin(x), cos(x)),
    func = rep(c("sin(x)", "cos(x)"), each = length(x))
  )
  
  p3 <- ggplot(combined_data, aes(x, y, color = func)) +
    geom_line(size = 1) +
    scale_color_manual(values = c("cos(x)" = "red", "sin(x)" = "blue")) +
    labs(title = "Sine and Cosine Comparison", 
         x = "x", y = "y", color = "Function") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
  
  # Display plots
  grid.arrange(p1, p2, p3, ncol = 2)
}

# Unit circle visualization
plot_unit_circle <- function() {
  # Create unit circle
  theta <- seq(0, 2*pi, length.out = 100)
  circle_data <- data.frame(x = cos(theta), y = sin(theta))
  
  # Key angles
  key_angles <- c(0, pi/6, pi/4, pi/3, pi/2, 2*pi/3, 3*pi/4, 5*pi/6, 
                  pi, 7*pi/6, 5*pi/4, 4*pi/3, 3*pi/2, 5*pi/3, 7*pi/4, 11*pi/6)
  
  key_points <- data.frame(
    x = cos(key_angles),
    y = sin(key_angles),
    angle = key_angles * 180 / pi
  )
  
  # Create plot
  ggplot() +
    geom_path(data = circle_data, aes(x, y), size = 1) +
    geom_point(data = key_points, aes(x, y), color = "red", size = 3) +
    geom_segment(data = key_points, aes(x = 0, y = 0, xend = x, yend = y), 
                 color = "blue", alpha = 0.7, linetype = "dashed") +
    geom_text(data = key_points, aes(x = 1.15*x, y = 1.15*y, 
                                    label = paste0(round(angle), "°")), 
              size = 3, hjust = 0.5, vjust = 0.5) +
    coord_equal() +
    xlim(-1.5, 1.5) +
    ylim(-1.5, 1.5) +
    labs(title = "Unit Circle with Key Angles", x = "cos(θ)", y = "sin(θ)") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
}

# Function transformations
plot_transformations <- function(A = 1, B = 1, C = 0, D = 0) {
  x <- seq(-2*pi, 2*pi, length.out = 1000)
  
  data <- data.frame(
    x = rep(x, 2),
    y = c(sin(x), A * sin(B * x + C) + D),
    func = rep(c("sin(x)", paste0(A, "sin(", B, "x + ", C, ") + ", D)), 
               each = length(x))
  )
  
  ggplot(data, aes(x, y, color = func, linetype = func)) +
    geom_line(size = 1) +
    scale_color_manual(values = c("sin(x)" = "blue", 
                                 paste0(A, "sin(", B, "x + ", C, ") + ", D) = "red")) +
    scale_linetype_manual(values = c("sin(x)" = "dashed", 
                                    paste0(A, "sin(", B, "x + ", C, ") + ", D) = "solid")) +
    labs(title = paste("Transformation: A =", A, ", B =", B, ", C =", C, ", D =", D),
         x = "x", y = "y", color = "Function", linetype = "Function") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
}

# Execute examples
plot_trig_functions()
plot_unit_circle()
plot_transformations(A = 2, B = 0.5, C = pi/4, D = 1)
```

</div>

</div>

</div>

<div class="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-500 mb-8">

### Advanced Programming Examples

<div class="grid md:grid-cols-2 gap-6">

<div>

#### Python - Numerical Integration

<div class="code-container">

Copy

``` bg-gray-800
from scipy import integrate
import numpy as np

# Integrate sin(x) from 0 to pi
result, error = integrate.quad(np.sin, 0, np.pi)
print(f"∫sin(x)dx from 0 to π = {result:.6f}")

# Verify: should equal 2
print(f"Analytical result: {-np.cos(np.pi) + np.cos(0)}")

# Fourier series approximation
def fourier_series(x, n_terms=5):
    result = np.zeros_like(x)
    for n in range(1, n_terms + 1):
        if n % 2 == 1:  # Odd terms only
            result += (4 / (np.pi * n)) * np.sin(n * x)
    return result
```

</div>

</div>

<div>

#### R - Statistical Analysis

<div class="code-container">

Copy

``` bg-gray-800
# Generate sinusoidal data with noise
set.seed(123)
x <- seq(0, 4*pi, length.out = 100)
y_true <- 2 * sin(x + pi/4) + 1
noise <- rnorm(100, 0, 0.3)
y_observed <- y_true + noise

# Fit sinusoidal model
library(nls2)
model <- nls(y_observed ~ A * sin(B * x + C) + D,
             start = list(A = 1, B = 1, C = 0, D = 0))

# Extract parameters
params <- coef(model)
print(params)

# Calculate R-squared
ss_res <- sum(residuals(model)^2)
ss_tot <- sum((y_observed - mean(y_observed))^2)
r_squared <- 1 - (ss_res / ss_tot)
print(paste("R-squared:", r_squared))
```

</div>

</div>

</div>

</div>

</div>

<div id="exercises" class="section mb-12">

## 2.8 Exercises and Projects

<div class="grid lg:grid-cols-2 gap-8 mb-8">

<div class="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">

### Practice Problems

1.  **Problem 1:** Find the exact values of $\sin(75°)$ and $\cos(75°)$ using sum formulas.
2.  **Problem 2:** Solve $2\sin^2(x) - 3\sin(x) + 1 = 0$ for $x \in [0, 2\pi]$.
3.  **Problem 3:** Graph $y = 3\cos(2x - \pi/3) - 1$ and identify all key features.
4.  **Problem 4:** Prove the identity: $\frac{1 - \cos(2\theta)}{2} = \sin^2(\theta)$
5.  **Problem 5:** Find the period and amplitude of $y = 4\sin(3x + \pi/2) + 2$.

</div>

<div class="bg-green-50 p-6 rounded-lg border-l-4 border-green-500">

### Programming Projects

1.  **Project 1:** Create an interactive unit circle app that shows coordinates as the user clicks on different angles.
2.  **Project 2:** Implement a Fourier series approximation to approximate square wave and sawtooth functions.
3.  **Project 3:** Model and visualize simple harmonic motion with different damping coefficients.
4.  **Project 4:** Create a trigonometric identity verification tool using symbolic computation.
5.  **Project 5:** Analyze real-world periodic data (temperature, sales, etc.) using trigonometric regression.

</div>

</div>

<div class="example-box p-6 rounded-lg mb-8">

### Sample Solution: Problem 1

<div class="bg-white bg-opacity-20 p-4 rounded">

**Find sin(75°) and cos(75°):**

Notice that $75° = 45° + 30°$

**For sin(75°):**

$\sin(75°) = \sin(45° + 30°)$

$= \sin(45°)\cos(30°) + \cos(45°)\sin(30°)$

$= \frac{\sqrt{2}}{2} \cdot \frac{\sqrt{3}}{2} + \frac{\sqrt{2}}{2} \cdot \frac{1}{2}$

$= \frac{\sqrt{6}}{4} + \frac{\sqrt{2}}{4} = \frac{\sqrt{6} + \sqrt{2}}{4}$

**For cos(75°):**

$\cos(75°) = \cos(45° + 30°)$

$= \cos(45°)\cos(30°) - \sin(45°)\sin(30°)$

$= \frac{\sqrt{2}}{2} \cdot \frac{\sqrt{3}}{2} - \frac{\sqrt{2}}{2} \cdot \frac{1}{2}$

$= \frac{\sqrt{6}}{4} - \frac{\sqrt{2}}{4} = \frac{\sqrt{6} - \sqrt{2}}{4}$

</div>

</div>

<div class="theorem-box p-6 rounded-lg">

### Chapter Summary

<div class="bg-white bg-opacity-20 p-4 rounded">

In this chapter, we've covered the essential trigonometric concepts needed for differential equations:

- [x] Unit circle and trigonometric function definitions
- [x] Fundamental trigonometric identities
- [x] Graphing and transformations of trigonometric functions
- [x] Applications to oscillatory systems and differential equations
- [x] Programming implementations in Python and R
- [x] Real-world modeling applications

**Next Chapter Preview:** We'll build on these trigonometric foundations to explore calculus concepts, including limits, derivatives, and integrals, which are crucial for understanding differential equations.

</div>

</div>

</div>

<div class="flex justify-center space-x-8 mb-4">

Comprehensive ODE Tutorial Academic Excellence Practical Implementation

</div>

© 2024 ODE Tutorial Series. Designed for mathematical rigor and practical application.

</div>
