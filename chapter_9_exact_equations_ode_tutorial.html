<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 9: Exact Equations - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        .math-block {
            background-color: #f0f9ff;
            border-left: 4px solid #3b82f6;
            padding: 1rem;
            margin: 1rem 0;
        }
        .example-box {
            background-color: #ecfdf5;
            border: 1px solid #a7f3d0;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .theorem-box {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .definition-box {
            background-color: #e0e7ff;
            border: 1px solid #6366f1;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .application-box {
            background-color: #fce7f3;
            border: 1px solid #ec4899;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .step-box {
            background-color: #f3f4f6;
            border-left: 4px solid #6b7280;
            padding: 1rem;
            margin: 0.5rem 0;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        body {
            line-height: 1.6;
        }
        .chapter-nav {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Chapter Navigation -->
    <nav class="chapter-nav border-b border-gray-200 py-4">
        <div class="max-w-6xl mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-book text-blue-600 text-xl"></i>
                    <h1 class="text-xl font-bold text-gray-800">ODE Tutorial - Part 2</h1>
                </div>
                <div class="text-sm text-gray-600">
                    Chapter 9: Exact Equations
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Chapter Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">
                <i class="fas fa-equals text-blue-600 mr-3"></i>
                Chapter 9: Exact Equations
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Master the theory and solution techniques for exact differential equations, including exactness conditions, potential functions, and integrating factors for non-exact equations.
            </p>
        </div>

        <!-- Learning Objectives -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold text-blue-800 mb-4">
                <i class="fas fa-target mr-2"></i>Learning Objectives
            </h2>
            <div class="grid md:grid-cols-2 gap-4">
                <ul class="space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                        <span>Understand the theory of exact differential equations</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                        <span>Apply exactness conditions and tests</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                        <span>Find potential functions and solve exact equations</span>
                    </li>
                </ul>
                <ul class="space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                        <span>Handle non-exact equations with integrating factors</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                        <span>Implement computational solutions in Python and R</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                        <span>Apply to real-world problems in physics and engineering</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Section 1: Introduction to Exact Equations -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-play-circle text-blue-600 mr-3"></i>
                1. Introduction to Exact Equations
            </h2>

            <div class="definition-box">
                <h3 class="text-xl font-semibold text-indigo-800 mb-3">
                    <i class="fas fa-info-circle mr-2"></i>Definition: Exact Differential Equation
                </h3>
                <p class="mb-4">A first-order differential equation of the form:</p>
                <div class="math-block">
                    $$M(x,y)dx + N(x,y)dy = 0$$
                </div>
                <p class="mb-4">is called <strong>exact</strong> if there exists a function $F(x,y)$ such that:</p>
                <div class="math-block">
                    $$\frac{\partial F}{\partial x} = M(x,y) \quad \text{and} \quad \frac{\partial F}{\partial y} = N(x,y)$$
                </div>
                <p>In this case, $F(x,y) = C$ is the general solution to the differential equation.</p>
            </div>

            <div class="theorem-box">
                <h3 class="text-xl font-semibold text-yellow-800 mb-3">
                    <i class="fas fa-theorem mr-2"></i>Exactness Condition
                </h3>
                <p class="mb-4">The differential equation $M(x,y)dx + N(x,y)dy = 0$ is exact if and only if:</p>
                <div class="math-block">
                    $$\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$$
                </div>
                <p class="mt-4"><strong>Proof:</strong> This follows from the equality of mixed partial derivatives: $\frac{\partial^2 F}{\partial y \partial x} = \frac{\partial^2 F}{\partial x \partial y}$ when $F$ has continuous second partial derivatives.</p>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-semibold text-green-800 mb-3">
                    <i class="fas fa-lightbulb mr-2"></i>Example 1: Testing for Exactness
                </h3>
                <p class="mb-4">Determine if the equation $(2xy + 3x^2)dx + (x^2 + 2y)dy = 0$ is exact.</p>
                
                <div class="step-box">
                    <strong>Step 1:</strong> Identify $M(x,y)$ and $N(x,y)$
                    <div class="math-block">
                        $$M(x,y) = 2xy + 3x^2, \quad N(x,y) = x^2 + 2y$$
                    </div>
                </div>
                
                <div class="step-box">
                    <strong>Step 2:</strong> Compute partial derivatives
                    <div class="math-block">
                        $$\frac{\partial M}{\partial y} = 2x, \quad \frac{\partial N}{\partial x} = 2x$$
                    </div>
                </div>
                
                <div class="step-box">
                    <strong>Step 3:</strong> Check exactness condition
                    <div class="math-block">
                        $$\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x} = 2x$$
                    </div>
                    <p class="mt-2">Since the partial derivatives are equal, the equation is exact.</p>
                </div>
            </div>
        </section>

        <!-- Section 2: Solving Exact Equations -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-blue-600 mr-3"></i>
                2. Solving Exact Equations
            </h2>

            <div class="bg-gray-100 p-6 rounded-lg mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-list-ol mr-2"></i>General Solution Method
                </h3>
                <p class="mb-4">To solve an exact equation $M(x,y)dx + N(x,y)dy = 0$:</p>
                
                <div class="step-box">
                    <strong>Step 1:</strong> Verify exactness condition: $\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$
                </div>
                
                <div class="step-box">
                    <strong>Step 2:</strong> Find potential function $F(x,y)$ by integrating:
                    $$F(x,y) = \int M(x,y) dx + g(y)$$
                    where $g(y)$ is an arbitrary function of $y$ only.
                </div>
                
                <div class="step-box">
                    <strong>Step 3:</strong> Determine $g(y)$ using the condition $\frac{\partial F}{\partial y} = N(x,y)$
                </div>
                
                <div class="step-box">
                    <strong>Step 4:</strong> The general solution is $F(x,y) = C$
                </div>
                
                <div class="step-box">
                    <strong>Step 5:</strong> Apply initial conditions if given to find the particular solution
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-semibold text-green-800 mb-3">
                    <i class="fas fa-calculator mr-2"></i>Example 2: Complete Solution
                </h3>
                <p class="mb-4">Solve $(2xy + 3x^2)dx + (x^2 + 2y)dy = 0$ with initial condition $y(0) = 1$.</p>
                
                <div class="step-box">
                    <strong>Step 1:</strong> We already verified this is exact in Example 1.
                </div>
                
                <div class="step-box">
                    <strong>Step 2:</strong> Find $F(x,y)$ by integrating $M(x,y) = 2xy + 3x^2$ with respect to $x$:
                    <div class="math-block">
                        $$F(x,y) = \int (2xy + 3x^2) dx = x^2y + x^3 + g(y)$$
                    </div>
                </div>
                
                <div class="step-box">
                    <strong>Step 3:</strong> Use $\frac{\partial F}{\partial y} = N(x,y) = x^2 + 2y$:
                    <div class="math-block">
                        $$\frac{\partial F}{\partial y} = x^2 + g'(y) = x^2 + 2y$$
                    </div>
                    <p class="mt-2">Therefore, $g'(y) = 2y$, so $g(y) = y^2 + C_1$</p>
                </div>
                
                <div class="step-box">
                    <strong>Step 4:</strong> The potential function is:
                    <div class="math-block">
                        $$F(x,y) = x^2y + x^3 + y^2$$
                    </div>
                    <p class="mt-2">General solution: $x^2y + x^3 + y^2 = C$</p>
                </div>
                
                <div class="step-box">
                    <strong>Step 5:</strong> Apply initial condition $y(0) = 1$:
                    <div class="math-block">
                        $$0^2(1) + 0^3 + 1^2 = C \Rightarrow C = 1$$
                    </div>
                    <p class="mt-2">Particular solution: $x^2y + x^3 + y^2 = 1$</p>
                </div>
            </div>
        </section>

        <!-- Section 3: Non-Exact Equations and Integrating Factors -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-magic text-blue-600 mr-3"></i>
                3. Non-Exact Equations and Integrating Factors
            </h2>

            <div class="definition-box">
                <h3 class="text-xl font-semibold text-indigo-800 mb-3">
                    <i class="fas fa-info-circle mr-2"></i>Integrating Factors
                </h3>
                <p class="mb-4">If $M(x,y)dx + N(x,y)dy = 0$ is not exact, we can sometimes find an <strong>integrating factor</strong> $\mu(x,y)$ such that:</p>
                <div class="math-block">
                    $$\mu(x,y)M(x,y)dx + \mu(x,y)N(x,y)dy = 0$$
                </div>
                <p>is exact.</p>
            </div>

            <div class="theorem-box">
                <h3 class="text-xl font-semibold text-yellow-800 mb-3">
                    <i class="fas fa-key mr-2"></i>Finding Integrating Factors
                </h3>
                <p class="mb-4"><strong>Case 1:</strong> Integrating factor depends only on $x$</p>
                <p class="mb-2">If $\frac{\frac{\partial M}{\partial y} - \frac{\partial N}{\partial x}}{N}$ is a function of $x$ only, then:</p>
                <div class="math-block">
                    $$\mu(x) = e^{\int \frac{\frac{\partial M}{\partial y} - \frac{\partial N}{\partial x}}{N} dx}$$
                </div>
                
                <p class="mb-4 mt-6"><strong>Case 2:</strong> Integrating factor depends only on $y$</p>
                <p class="mb-2">If $\frac{\frac{\partial N}{\partial x} - \frac{\partial M}{\partial y}}{M}$ is a function of $y$ only, then:</p>
                <div class="math-block">
                    $$\mu(y) = e^{\int \frac{\frac{\partial N}{\partial x} - \frac{\partial M}{\partial y}}{M} dy}$$
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-semibold text-green-800 mb-3">
                    <i class="fas fa-wrench mr-2"></i>Example 3: Using an Integrating Factor
                </h3>
                <p class="mb-4">Solve $y dx + (2x - ye^y) dy = 0$.</p>
                
                <div class="step-box">
                    <strong>Step 1:</strong> Check for exactness
                    <div class="math-block">
                        $$M = y, \quad N = 2x - ye^y$$
                        $$\frac{\partial M}{\partial y} = 1, \quad \frac{\partial N}{\partial x} = 2$$
                    </div>
                    <p class="mt-2">Since $1 \neq 2$, the equation is not exact.</p>
                </div>
                
                <div class="step-box">
                    <strong>Step 2:</strong> Try to find an integrating factor
                    <div class="math-block">
                        $$\frac{\frac{\partial M}{\partial y} - \frac{\partial N}{\partial x}}{N} = \frac{1-2}{2x-ye^y} = \frac{-1}{2x-ye^y}$$
                    </div>
                    <p class="mt-2">This is not a function of $x$ only.</p>
                    
                    <div class="math-block">
                        $$\frac{\frac{\partial N}{\partial x} - \frac{\partial M}{\partial y}}{M} = \frac{2-1}{y} = \frac{1}{y}$$
                    </div>
                    <p class="mt-2">This is a function of $y$ only!</p>
                </div>
                
                <div class="step-box">
                    <strong>Step 3:</strong> Find the integrating factor
                    <div class="math-block">
                        $$\mu(y) = e^{\int \frac{1}{y} dy} = e^{\ln|y|} = |y| = y \quad (y > 0)$$
                    </div>
                </div>
                
                <div class="step-box">
                    <strong>Step 4:</strong> Multiply the equation by $\mu(y) = y$
                    <div class="math-block">
                        $$y^2 dx + y(2x - ye^y) dy = 0$$
                        $$y^2 dx + (2xy - y^2e^y) dy = 0$$
                    </div>
                </div>
                
                <div class="step-box">
                    <strong>Step 5:</strong> Verify the new equation is exact
                    <div class="math-block">
                        $$M_1 = y^2, \quad N_1 = 2xy - y^2e^y$$
                        $$\frac{\partial M_1}{\partial y} = 2y, \quad \frac{\partial N_1}{\partial x} = 2y$$
                    </div>
                    <p class="mt-2">Now it's exact!</p>
                </div>
                
                <div class="step-box">
                    <strong>Step 6:</strong> Solve the exact equation
                    <div class="math-block">
                        $$F(x,y) = \int y^2 dx = xy^2 + g(y)$$
                        $$\frac{\partial F}{\partial y} = 2xy + g'(y) = 2xy - y^2e^y$$
                    </div>
                    <p class="mt-2">Therefore, $g'(y) = -y^2e^y$, so $g(y) = -\int y^2e^y dy$</p>
                    <p class="mt-2">Using integration by parts twice: $g(y) = -y^2e^y + 2ye^y - 2e^y$</p>
                    <p class="mt-2">General solution: $xy^2 - y^2e^y + 2ye^y - 2e^y = C$</p>
                </div>
            </div>
        </section>

        <!-- Section 4: Python Implementation -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fab fa-python text-blue-600 mr-3"></i>
                4. Python Implementation
            </h2>

            <div class="code-block">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Complete Python Toolkit for Exact Equations</h3>
                <pre><code>import sympy as sp
import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from mpl_toolkits.mplot3d import Axes3D

# Define symbols
x, y, C = sp.symbols('x y C')

class ExactEquationSolver:
    def __init__(self, M, N):
        """
        Initialize with M(x,y) and N(x,y) for equation M dx + N dy = 0
        """
        self.M = M
        self.N = N
        self.x = x
        self.y = y
    
    def check_exactness(self):
        """
        Check if the equation is exact
        """
        dM_dy = sp.diff(self.M, self.y)
        dN_dx = sp.diff(self.N, self.x)
        
        print(f"∂M/∂y = {dM_dy}")
        print(f"∂N/∂x = {dN_dx}")
        
        is_exact = sp.simplify(dM_dy - dN_dx) == 0
        print(f"Equation is exact: {is_exact}")
        
        return is_exact, dM_dy, dN_dx
    
    def find_integrating_factor_x(self, dM_dy, dN_dx):
        """
        Find integrating factor that depends only on x
        """
        try:
            ratio = sp.simplify((dM_dy - dN_dx) / self.N)
            # Check if ratio depends only on x
            if not ratio.has(self.y):
                mu_x = sp.exp(sp.integrate(ratio, self.x))
                print(f"Integrating factor μ(x) = {mu_x}")
                return mu_x
        except:
            pass
        return None
    
    def find_integrating_factor_y(self, dM_dy, dN_dx):
        """
        Find integrating factor that depends only on y
        """
        try:
            ratio = sp.simplify((dN_dx - dM_dy) / self.M)
            # Check if ratio depends only on y
            if not ratio.has(self.x):
                mu_y = sp.exp(sp.integrate(ratio, self.y))
                print(f"Integrating factor μ(y) = {mu_y}")
                return mu_y
        except:
            pass
        return None
    
    def solve_exact(self, M_exact, N_exact):
        """
        Solve exact equation by finding potential function
        """
        # Integrate M with respect to x
        F = sp.integrate(M_exact, self.x)
        print(f"∫M dx = {F}")
        
        # Add function of y only
        g = sp.Function('g')
        F_general = F + g(self.y)
        
        # Use condition ∂F/∂y = N
        dF_dy = sp.diff(F, self.y)
        g_prime = sp.simplify(N_exact - dF_dy)
        
        print(f"g'(y) = {g_prime}")
        
        # Integrate to find g(y)
        g_func = sp.integrate(g_prime, self.y)
        
        # Complete potential function
        F_complete = F + g_func
        
        print(f"Potential function F(x,y) = {F_complete}")
        print(f"General solution: {F_complete} = C")
        
        return F_complete
    
    def solve(self):
        """
        Complete solution process
        """
        print("="*50)
        print("EXACT EQUATION SOLVER")
        print("="*50)
        print(f"Equation: ({self.M}) dx + ({self.N}) dy = 0")
        print()
        
        # Check exactness
        is_exact, dM_dy, dN_dx = self.check_exactness()
        
        if is_exact:
            print("\nSolving exact equation...")
            solution = self.solve_exact(self.M, self.N)
            return solution
        else:
            print("\nEquation is not exact. Looking for integrating factors...")
            
            # Try integrating factor depending on x
            mu_x = self.find_integrating_factor_x(dM_dy, dN_dx)
            if mu_x:
                print(f"\nMultiplying by μ(x) = {mu_x}")
                M_new = sp.simplify(mu_x * self.M)
                N_new = sp.simplify(mu_x * self.N)
                print(f"New equation: ({M_new}) dx + ({N_new}) dy = 0")
                return self.solve_exact(M_new, N_new)
            
            # Try integrating factor depending on y
            mu_y = self.find_integrating_factor_y(dM_dy, dN_dx)
            if mu_y:
                print(f"\nMultiplying by μ(y) = {mu_y}")
                M_new = sp.simplify(mu_y * self.M)
                N_new = sp.simplify(mu_y * self.N)
                print(f"New equation: ({M_new}) dx + ({N_new}) dy = 0")
                return self.solve_exact(M_new, N_new)
            
            print("No simple integrating factor found.")
            return None

# Example 1: Exact equation
print("EXAMPLE 1: Exact Equation")
M1 = 2*x*y + 3*x**2
N1 = x**2 + 2*y
solver1 = ExactEquationSolver(M1, N1)
solution1 = solver1.solve()

print("\n" + "="*50)

# Example 2: Non-exact equation requiring integrating factor
print("EXAMPLE 2: Non-exact Equation")
M2 = y
N2 = 2*x - y*sp.exp(y)
solver2 = ExactEquationSolver(M2, N2)
solution2 = solver2.solve()

# Visualization function
def visualize_solution(F, x_range=(-2, 2), y_range=(-2, 2), levels=10):
    """
    Visualize solution curves F(x,y) = C
    """
    x_vals = np.linspace(x_range[0], x_range[1], 100)
    y_vals = np.linspace(y_range[0], y_range[1], 100)
    X, Y = np.meshgrid(x_vals, y_vals)
    
    # Convert symbolic expression to numerical function
    F_func = sp.lambdify((x, y), F, 'numpy')
    
    try:
        Z = F_func(X, Y)
        
        plt.figure(figsize=(10, 8))
        contour = plt.contour(X, Y, Z, levels=levels, colors='blue')
        plt.clabel(contour, inline=True, fontsize=8)
        plt.xlabel('x')
        plt.ylabel('y')
        plt.title(f'Solution Curves: {F} = C')
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        plt.show()
    except Exception as e:
        print(f"Visualization error: {e}")

# Visualize solutions
if solution1:
    print("\nVisualizing Solution 1...")
    visualize_solution(solution1)

# Direction field visualization
def plot_direction_field(M, N, x_range=(-2, 2), y_range=(-2, 2), density=20):
    """
    Plot direction field for M dx + N dy = 0
    """
    x_vals = np.linspace(x_range[0], x_range[1], density)
    y_vals = np.linspace(y_range[0], y_range[1], density)
    X, Y = np.meshgrid(x_vals, y_vals)
    
    # Convert to numerical functions
    M_func = sp.lambdify((x, y), M, 'numpy')
    N_func = sp.lambdify((x, y), N, 'numpy')
    
    try:
        M_vals = M_func(X, Y)
        N_vals = N_func(X, Y)
        
        # Direction field shows dy/dx = -M/N
        DX = np.ones_like(X)
        DY = -M_vals / N_vals
        
        # Normalize arrows
        norm = np.sqrt(DX**2 + DY**2)
        DX = DX / norm
        DY = DY / norm
        
        plt.figure(figsize=(10, 8))
        plt.quiver(X, Y, DX, DY, alpha=0.6)
        plt.xlabel('x')
        plt.ylabel('y')
        plt.title(f'Direction Field for ({M}) dx + ({N}) dy = 0')
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        plt.show()
    except Exception as e:
        print(f"Direction field error: {e}")

# Plot direction field for Example 1
print("\nDirection Field for Example 1:")
plot_direction_field(M1, N1)</code></pre>
            </div>
        </section>

        <!-- Section 5: R Implementation -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fab fa-r-project text-blue-600 mr-3"></i>
                5. R Implementation
            </h2>

            <div class="code-block">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Complete R Toolkit for Exact Equations</h3>
                <pre><code># Load required libraries
library(Ryacas)
library(ggplot2)
library(plotly)
library(deSolve)
library(pracma)

# Define exact equation solver class
ExactEquationSolver <- function(M_expr, N_expr) {
  
  # Check exactness
  check_exactness <- function() {
    cat("Checking exactness condition...\n")
    
    # Compute partial derivatives
    dM_dy <- yac_str(paste0("D(", M_expr, ") y"))
    dN_dx <- yac_str(paste0("D(", N_expr, ") x"))
    
    cat("∂M/∂y =", dM_dy, "\n")
    cat("∂N/∂x =", dN_dx, "\n")
    
    # Check if equal
    diff_expr <- yac_str(paste0("Simplify(", dM_dy, " - (", dN_dx, "))"))
    is_exact <- diff_expr == "0"
    
    cat("Equation is exact:", is_exact, "\n")
    
    return(list(is_exact = is_exact, dM_dy = dM_dy, dN_dx = dN_dx))
  }
  
  # Find integrating factor depending on x
  find_integrating_factor_x <- function(dM_dy, dN_dx) {
    tryCatch({
      ratio_expr <- paste0("Simplify((", dM_dy, " - (", dN_dx, ")) / (", N_expr, "))")
      ratio <- yac_str(ratio_expr)
      
      # Check if ratio depends only on x (simple check)
      if (!grepl("y", ratio)) {
        integral_expr <- paste0("Integrate(", ratio, ", x)")
        integral <- yac_str(integral_expr)
        mu_x <- yac_str(paste0("Exp(", integral, ")"))
        cat("Integrating factor μ(x) =", mu_x, "\n")
        return(mu_x)
      }
      return(NULL)
    }, error = function(e) return(NULL))
  }
  
  # Find integrating factor depending on y
  find_integrating_factor_y <- function(dM_dy, dN_dx) {
    tryCatch({
      ratio_expr <- paste0("Simplify((", dN_dx, " - (", dM_dy, ")) / (", M_expr, "))")
      ratio <- yac_str(ratio_expr)
      
      # Check if ratio depends only on y (simple check)
      if (!grepl("x", ratio)) {
        integral_expr <- paste0("Integrate(", ratio, ", y)")
        integral <- yac_str(integral_expr)
        mu_y <- yac_str(paste0("Exp(", integral, ")"))
        cat("Integrating factor μ(y) =", mu_y, "\n")
        return(mu_y)
      }
      return(NULL)
    }, error = function(e) return(NULL))
  }
  
  # Solve exact equation
  solve_exact <- function(M_exact, N_exact) {
    cat("Solving exact equation...\n")
    
    # Integrate M with respect to x
    F_partial <- yac_str(paste0("Integrate(", M_exact, ", x)"))
    cat("∫M dx =", F_partial, "\n")
    
    # Find g(y) by using ∂F/∂y = N
    dF_dy <- yac_str(paste0("D(", F_partial, ") y"))
    g_prime <- yac_str(paste0("Simplify(", N_exact, " - (", dF_dy, "))")
    
    cat("g'(y) =", g_prime, "\n")
    
    # Integrate to find g(y)
    g_func <- yac_str(paste0("Integrate(", g_prime, ", y)"))
    
    # Complete potential function
    F_complete <- yac_str(paste0("Simplify(", F_partial, " + (", g_func, "))"))
    
    cat("Potential function F(x,y) =", F_complete, "\n")
    cat("General solution:", F_complete, "= C\n")
    
    return(F_complete)
  }
  
  # Main solve function
  solve <- function() {
    cat("===============================================\n")
    cat("EXACT EQUATION SOLVER\n")
    cat("===============================================\n")
    cat("Equation: (", M_expr, ") dx + (", N_expr, ") dy = 0\n\n")
    
    # Check exactness
    result <- check_exactness()
    
    if (result$is_exact) {
      cat("\nSolving exact equation...\n")
      solution <- solve_exact(M_expr, N_expr)
      return(solution)
    } else {
      cat("\nEquation is not exact. Looking for integrating factors...\n")
      
      # Try integrating factor depending on x
      mu_x <- find_integrating_factor_x(result$dM_dy, result$dN_dx)
      if (!is.null(mu_x)) {
        cat("\nMultiplying by μ(x) =", mu_x, "\n")
        M_new <- yac_str(paste0("Simplify((", mu_x, ") * (", M_expr, "))"))
        N_new <- yac_str(paste0("Simplify((", mu_x, ") * (", N_expr, "))"))
        cat("New equation: (", M_new, ") dx + (", N_new, ") dy = 0\n")
        return(solve_exact(M_new, N_new))
      }
      
      # Try integrating factor depending on y
      mu_y <- find_integrating_factor_y(result$dM_dy, result$dN_dx)
      if (!is.null(mu_y)) {
        cat("\nMultiplying by μ(y) =", mu_y, "\n")
        M_new <- yac_str(paste0("Simplify((", mu_y, ") * (", M_expr, "))"))
        N_new <- yac_str(paste0("Simplify((", mu_y, ") * (", N_expr, "))"))
        cat("New equation: (", M_new, ") dx + (", N_new, ") dy = 0\n")
        return(solve_exact(M_new, N_new))
      }
      
      cat("No simple integrating factor found.\n")
      return(NULL)
    }
  }
  
  return(list(solve = solve))
}

# Example 1: Exact equation
cat("EXAMPLE 1: Exact Equation\n")
M1 <- "2*x*y + 3*x^2"
N1 <- "x^2 + 2*y"
solver1 <- ExactEquationSolver(M1, N1)
solution1 <- solver1$solve()

cat("\n===============================================\n")

# Example 2: Non-exact equation
cat("EXAMPLE 2: Non-exact Equation\n")
M2 <- "y"
N2 <- "2*x - y*Exp(y)"
solver2 <- ExactEquationSolver(M2, N2)
solution2 <- solver2$solve()

# Visualization functions using ggplot2
plot_direction_field <- function(M_func, N_func, x_range = c(-2, 2), y_range = c(-2, 2), density = 15) {
  # Create grid
  x_vals <- seq(x_range[1], x_range[2], length.out = density)
  y_vals <- seq(y_range[1], y_range[2], length.out = density)
  grid <- expand.grid(x = x_vals, y = y_vals)
  
  # Calculate direction vectors
  # For M dx + N dy = 0, we have dy/dx = -M/N
  grid$dx <- 1
  grid$dy <- -M_func(grid$x, grid$y) / N_func(grid$x, grid$y)
  
  # Normalize arrows
  norm <- sqrt(grid$dx^2 + grid$dy^2)
  grid$dx <- grid$dx / norm
  grid$dy <- grid$dy / norm
  
  # Remove infinite or NaN values
  grid <- grid[is.finite(grid$dx) & is.finite(grid$dy), ]
  
  # Create plot
  p <- ggplot(grid, aes(x = x, y = y)) +
    geom_segment(aes(xend = x + 0.1*dx, yend = y + 0.1*dy), 
                 arrow = arrow(length = unit(0.02, "npc")), 
                 alpha = 0.6, color = "blue") +
    labs(title = "Direction Field", x = "x", y = "y") +
    theme_minimal() +
    coord_equal()
  
  return(p)
}

# Define numerical functions for plotting
M1_func <- function(x, y) 2*x*y + 3*x^2
N1_func <- function(x, y) x^2 + 2*y

# Plot direction field
p1 <- plot_direction_field(M1_func, N1_func)
print(p1)

# Solution curve plotting
plot_solution_curves <- function(F_expr, x_range = c(-2, 2), y_range = c(-2, 2), levels = 10) {
  # Create grid
  x_vals <- seq(x_range[1], x_range[2], length.out = 100)
  y_vals <- seq(y_range[1], y_range[2], length.out = 100)
  grid <- expand.grid(x = x_vals, y = y_vals)
  
  # This is a simplified approach - in practice, you'd need to 
  # convert the symbolic expression to a numerical function
  
  # For the exact solution x^2*y + x^3 + y^2 = C
  if (grepl("x.*y.*x.*y", F_expr)) {
    grid$F <- grid$x^2 * grid$y + grid$x^3 + grid$y^2
    
    # Create contour plot
    p <- ggplot(grid, aes(x = x, y = y, z = F)) +
      geom_contour(aes(color = ..level..), bins = levels) +
      labs(title = paste("Solution Curves:", F_expr, "= C"), 
           x = "x", y = "y") +
      theme_minimal() +
      coord_equal()
    
    return(p)
  }
  
  return(NULL)
}

# Plot solution curves for Example 1
if (!is.null(solution1)) {
  p_sol <- plot_solution_curves(solution1)
  if (!is.null(p_sol)) print(p_sol)
}</code></pre>
            </div>
        </section>

        <!-- Section 6: Applications -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-flask text-blue-600 mr-3"></i>
                6. Real-World Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-semibold text-pink-800 mb-3">
                    <i class="fas fa-magnet mr-2"></i>Application 1: Conservative Force Fields
                </h3>
                <p class="mb-4">In physics, a conservative force field can be described by a potential function. If $\vec{F} = (P(x,y), Q(x,y))$ is a conservative force, then there exists a potential function $U(x,y)$ such that:</p>
                <div class="math-block">
                    $$P = -\frac{\partial U}{\partial x}, \quad Q = -\frac{\partial U}{\partial y}$$
                </div>
                <p class="mb-4">The condition for a conservative force field is:</p>
                <div class="math-block">
                    $$\frac{\partial P}{\partial y} = \frac{\partial Q}{\partial x}$$
                </div>
                <p class="mb-4">This is exactly the exactness condition for the differential equation $P dx + Q dy = 0$!</p>
                
                <div class="bg-pink-50 p-4 rounded-lg mt-4">
                    <h4 class="font-semibold mb-2">Example: Gravitational Field</h4>
                    <p class="mb-2">The gravitational force field around a point mass at the origin is:</p>
                    <div class="math-block">
                        $$\vec{F} = -\frac{GMm}{r^3}(x, y) = -\frac{GMm}{(x^2+y^2)^{3/2}}(x, y)$$
                    </div>
                    <p>This gives us the exact differential equation for equipotential lines (lines of constant gravitational potential).</p>
                </div>
            </div>

            <div class="application-box">
                <h3 class="text-xl font-semibold text-pink-800 mb-3">
                    <i class="fas fa-thermometer-half mr-2"></i>Application 2: Thermodynamics
                </h3>
                <p class="mb-4">In thermodynamics, exact differentials are fundamental. For a system with two state variables, the first law of thermodynamics can be written as:</p>
                <div class="math-block">
                    $$dU = TdS - PdV$$
                </div>
                <p class="mb-4">where $U$ is internal energy, $T$ is temperature, $S$ is entropy, $P$ is pressure, and $V$ is volume.</p>
                <p class="mb-4">This is an exact differential because $U$ is a state function. The exactness condition gives us Maxwell's relations:</p>
                <div class="math-block">
                    $$\frac{\partial T}{\partial V} = -\frac{\partial P}{\partial S}$$
                </div>
                
                <div class="bg-pink-50 p-4 rounded-lg mt-4">
                    <h4 class="font-semibold mb-2">Example: Ideal Gas</h4>
                    <p class="mb-2">For an ideal gas, we have:</p>
                    <div class="math-block">
                        $$PV = nRT, \quad dU = nC_V dT$$
                    </div>
                    <p>This leads to exact differential equations that describe the thermodynamic processes.</p>
                </div>
            </div>

            <div class="application-box">
                <h3 class="text-xl font-semibold text-pink-800 mb-3">
                    <i class="fas fa-water mr-2"></i>Application 3: Fluid Mechanics
                </h3>
                <p class="mb-4">In fluid mechanics, the condition for irrotational flow (potential flow) is that the vorticity is zero:</p>
                <div class="math-block">
                    $$\nabla \times \vec{v} = 0$$
                </div>
                <p class="mb-4">For 2D flow with velocity components $(u, v)$, this becomes:</p>
                <div class="math-block">
                    $$\frac{\partial v}{\partial x} - \frac{\partial u}{\partial y} = 0$$
                </div>
                <p class="mb-4">This is exactly the exactness condition for the differential equation $u dx + v dy = 0$, which represents streamlines of the flow!</p>
                
                <div class="bg-pink-50 p-4 rounded-lg mt-4">
                    <h4 class="font-semibold mb-2">Example: Flow Around a Cylinder</h4>
                    <p class="mb-2">The velocity potential for flow around a circular cylinder is:</p>
                    <div class="math-block">
                        $$\phi = U\left(r + \frac{a^2}{r}\right)\cos\theta$$
                    </div>
                    <p>where $U$ is the free stream velocity and $a$ is the cylinder radius. The streamlines are given by the exact differential equation derived from this potential.</p>
                </div>
            </div>
        </section>

        <!-- Section 7: Advanced Topics -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-graduation-cap text-blue-600 mr-3"></i>
                7. Advanced Topics
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-semibold text-yellow-800 mb-3">
                    <i class="fas fa-link mr-2"></i>Connection to Line Integrals
                </h3>
                <p class="mb-4">The solution to an exact equation $M dx + N dy = 0$ is related to line integrals. If the equation is exact, then the line integral:</p>
                <div class="math-block">
                    $$\int_C M dx + N dy$$
                </div>
                <p class="mb-4">is independent of the path $C$ between any two points. This is because:</p>
                <div class="math-block">
                    $$\int_C M dx + N dy = \int_C dF = F(x_2, y_2) - F(x_1, y_1)$$
                </div>
                <p>where $F$ is the potential function.</p>
            </div>

            <div class="theorem-box">
                <h3 class="text-xl font-semibold text-yellow-800 mb-3">
                    <i class="fas fa-vector-square mr-2"></i>Green's Theorem Connection
                </h3>
                <p class="mb-4">Green's theorem states that for a simple closed curve $C$ enclosing region $D$:</p>
                <div class="math-block">
                    $$\oint_C M dx + N dy = \iint_D \left(\frac{\partial N}{\partial x} - \frac{\partial M}{\partial y}\right) dA$$
                </div>
                <p class="mb-4">For an exact equation where $\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$, the right side is zero, confirming that:</p>
                <div class="math-block">
                    $$\oint_C M dx + N dy = 0$$
                </div>
                <p>This means the line integral around any closed path is zero for exact equations.</p>
            </div>

            <div class="definition-box">
                <h3 class="text-xl font-semibold text-indigo-800 mb-3">
                    <i class="fas fa-expand-arrows-alt mr-2"></i>Extension to Multiple Variables
                </h3>
                <p class="mb-4">The concept of exact differentials extends to functions of multiple variables. For a function $F(x, y, z)$, the total differential is:</p>
                <div class="math-block">
                    $$dF = \frac{\partial F}{\partial x} dx + \frac{\partial F}{\partial y} dy + \frac{\partial F}{\partial z} dz$$
                </div>
                <p class="mb-4">The exactness conditions become:</p>
                <div class="math-block">
                    $$\frac{\partial^2 F}{\partial y \partial x} = \frac{\partial^2 F}{\partial x \partial y}, \quad \frac{\partial^2 F}{\partial z \partial x} = \frac{\partial^2 F}{\partial x \partial z}, \quad \frac{\partial^2 F}{\partial z \partial y} = \frac{\partial^2 F}{\partial y \partial z}$$
                </div>
                <p>This leads to the compatibility conditions for exact differential equations in multiple variables.</p>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-semibold text-green-800 mb-3">
                    <i class="fas fa-cube mr-2"></i>Advanced Example: 3D Exact Equation
                </h3>
                <p class="mb-4">Consider the differential equation:</p>
                <div class="math-block">
                    $$(2xyz + z^2)dx + (x^2z)dy + (x^2y + 2xz)dz = 0$$
                </div>
                
                <div class="step-box">
                    <strong>Step 1:</strong> Identify the coefficients
                    <div class="math-block">
                        $$P = 2xyz + z^2, \quad Q = x^2z, \quad R = x^2y + 2xz$$
                    </div>
                </div>
                
                <div class="step-box">
                    <strong>Step 2:</strong> Check exactness conditions
                    <div class="math-block">
                        $$\frac{\partial P}{\partial y} = 2xz, \quad \frac{\partial Q}{\partial x} = 2xz \quad \checkmark$$
                        $$\frac{\partial P}{\partial z} = 2xy + 2z, \quad \frac{\partial R}{\partial x} = 2xy + 2z \quad \checkmark$$
                        $$\frac{\partial Q}{\partial z} = x^2, \quad \frac{\partial R}{\partial y} = x^2 \quad \checkmark$$
                    </div>
                </div>
                
                <div class="step-box">
                    <strong>Step 3:</strong> Find the potential function
                    <div class="math-block">
                        $$F(x,y,z) = \int P dx = \int (2xyz + z^2) dx = x^2yz + xz^2 + g(y,z)$$
                    </div>
                    <p class="mt-2">Using the other conditions to find $g(y,z)$, we get:</p>
                    <div class="math-block">
                        $$F(x,y,z) = x^2yz + xz^2$$
                    </div>
                    <p class="mt-2">General solution: $x^2yz + xz^2 = C$</p>
                </div>
            </div>
        </section>

        <!-- Section 8: Problem-Solving Strategies -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-chess text-blue-600 mr-3"></i>
                8. Problem-Solving Strategies
            </h2>

            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-indigo-800 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>Systematic Approach to Exact Equations
                </h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">Recognition Phase</h4>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5">1</span>
                                <span>Write equation in standard form $M dx + N dy = 0$</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5">2</span>
                                <span>Compute $\frac{\partial M}{\partial y}$ and $\frac{\partial N}{\partial x}$</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5">3</span>
                                <span>Check if $\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">Solution Phase</h4>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-start">
                                <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5">4</span>
                                <span>If exact: Find potential function $F(x,y)$</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5">5</span>
                                <span>If not exact: Find integrating factor</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5">6</span>
                                <span>Write general solution: $F(x,y) = C$</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 class="text-xl font-semibold text-yellow-800 mb-4">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Common Pitfalls and How to Avoid Them
                </h3>
                
                <div class="space-y-4">
                    <div class="bg-white p-4 rounded-lg border border-yellow-200">
                        <h4 class="font-semibold text-red-600 mb-2">❌ Pitfall 1: Incorrect Partial Derivatives</h4>
                        <p class="text-sm mb-2">Students often make errors when computing partial derivatives of complex expressions.</p>
                        <p class="text-sm text-green-600"><strong>Solution:</strong> Double-check your work and use symbolic computation tools for verification.</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg border border-yellow-200">
                        <h4 class="font-semibold text-red-600 mb-2">❌ Pitfall 2: Forgetting the Arbitrary Function</h4>
                        <p class="text-sm mb-2">When integrating to find the potential function, students forget that the "constant" can be a function of the other variable.</p>
                        <p class="text-sm text-green-600"><strong>Solution:</strong> Always add $+ g(y)$ when integrating with respect to $x$, then use the second condition to find $g(y)$.</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg border border-yellow-200">
                        <h4 class="font-semibold text-red-600 mb-2">❌ Pitfall 3: Incorrect Integrating Factor Application</h4>
                        <p class="text-sm mb-2">Students sometimes apply integrating factors incorrectly or fail to verify that the result is exact.</p>
                        <p class="text-sm text-green-600"><strong>Solution:</strong> Always verify exactness after applying an integrating factor, and remember to multiply both terms.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 9: Exercises and Practice Problems -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-dumbbell text-blue-600 mr-3"></i>
                9. Practice Problems
            </h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-800 mb-4">
                        <i class="fas fa-star mr-2"></i>Basic Problems
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Problem 1</h4>
                            <p class="text-sm mb-2">Solve: $(3x^2 + 2xy)dx + (x^2 + 2y)dy = 0$</p>
                            <details class="text-sm text-gray-600">
                                <summary class="cursor-pointer hover:text-blue-600">Hint</summary>
                                <p class="mt-2">Check exactness first, then find the potential function by integrating.</p>
                            </details>
                        </div>
                        
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Problem 2</h4>
                            <p class="text-sm mb-2">Solve: $(2x + y)dx + (x - 2y)dy = 0$</p>
                            <details class="text-sm text-gray-600">
                                <summary class="cursor-pointer hover:text-blue-600">Hint</summary>
                                <p class="mt-2">This equation is not exact. Look for an integrating factor.</p>
                            </details>
                        </div>
                        
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Problem 3</h4>
                            <p class="text-sm mb-2">Solve: $e^x \sin y \, dx + e^x \cos y \, dy = 0$</p>
                            <details class="text-sm text-gray-600">
                                <summary class="cursor-pointer hover:text-blue-600">Hint</summary>
                                <p class="mt-2">This should be exact. The potential function involves exponential and trigonometric functions.</p>
                            </details>
                        </div>
                    </div>
                </div>
                
                <div class="bg-green-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-green-800 mb-4">
                        <i class="fas fa-star-half-alt mr-2"></i>Intermediate Problems
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Problem 4</h4>
                            <p class="text-sm mb-2">Solve: $\frac{y}{x^2 + y^2}dx - \frac{x}{x^2 + y^2}dy = 0$</p>
                            <details class="text-sm text-gray-600">
                                <summary class="cursor-pointer hover:text-blue-600">Hint</summary>
                                <p class="mt-2">This is related to the arctangent function. Consider polar coordinates.</p>
                            </details>
                        </div>
                        
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Problem 5</h4>
                            <p class="text-sm mb-2">Find an integrating factor for: $y \, dx + (x^2y - x)dy = 0$</p>
                            <details class="text-sm text-gray-600">
                                <summary class="cursor-pointer hover:text-blue-600">Hint</summary>
                                <p class="mt-2">Try an integrating factor that depends only on $x$.</p>
                            </details>
                        </div>
                        
                        <div class="bg-white p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Problem 6</h4>
                            <p class="text-sm mb-2">Solve the IVP: $(2xy + 1)dx + x^2 dy = 0$, $y(1) = 2$</p>
                            <details class="text-sm text-gray-600">
                                <summary class="cursor-pointer hover:text-blue-600">Hint</summary>
                                <p class="mt-2">Check exactness, solve the general equation, then apply the initial condition.</p>
                            </details>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 bg-red-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-red-800 mb-4">
                    <i class="fas fa-fire mr-2"></i>Advanced Problems
                </h3>
                
                <div class="space-y-4">
                    <div class="bg-white p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">Problem 7: Application to Physics</h4>
                        <p class="text-sm mb-2">A conservative force field is given by $\vec{F} = (2xy + y^2, x^2 + 2xy)$. Find the potential function and the work done moving a particle from $(0,0)$ to $(1,1)$.</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">Problem 8: Thermodynamics</h4>
                        <p class="text-sm mb-2">For an ideal gas, show that the differential $dU = nC_V dT$ is exact, where $U$ is internal energy, $n$ is number of moles, $C_V$ is heat capacity at constant volume, and $T$ is temperature.</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">Problem 9: Multiple Variables</h4>
                        <p class="text-sm mb-2">Determine if the differential equation $(yz + 2x)dx + (xz + 1)dy + (xy + z)dz = 0$ is exact in three variables. If so, find the solution.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 10: Summary and Key Takeaways -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-clipboard-check text-blue-600 mr-3"></i>
                10. Chapter Summary
            </h2>

            <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-8 rounded-lg">
                <h3 class="text-2xl font-semibold text-gray-800 mb-6 text-center">Key Concepts Mastered</h3>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="text-center mb-3">
                            <i class="fas fa-search text-3xl text-blue-500"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Recognition</h4>
                        <p class="text-sm text-gray-600">Identify exact equations using the condition $\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="text-center mb-3">
                            <i class="fas fa-cog text-3xl text-green-500"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Solution Method</h4>
                        <p class="text-sm text-gray-600">Find potential functions and solve exact equations systematically</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="text-center mb-3">
                            <i class="fas fa-magic text-3xl text-purple-500"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Integrating Factors</h4>
                        <p class="text-sm text-gray-600">Transform non-exact equations into exact ones using integrating factors</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="text-center mb-3">
                            <i class="fas fa-code text-3xl text-red-500"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Computation</h4>
                        <p class="text-sm text-gray-600">Implement solutions in Python and R with symbolic and numerical methods</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="text-center mb-3">
                            <i class="fas fa-flask text-3xl text-yellow-500"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Applications</h4>
                        <p class="text-sm text-gray-600">Apply to physics, thermodynamics, and engineering problems</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="text-center mb-3">
                            <i class="fas fa-chart-line text-3xl text-indigo-500"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Visualization</h4>
                        <p class="text-sm text-gray-600">Create direction fields and solution curve plots for geometric insight</p>
                    </div>
                </div>
            </div>

            <div class="mt-8 bg-gray-100 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-route mr-2"></i>Solution Method Summary
                </h3>
                
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="bg-blue-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-equals"></i>
                        </div>
                        <h4 class="font-semibold mb-2">Exact Equations</h4>
                        <p class="text-sm text-gray-600">$\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$</p>
                        <p class="text-sm text-gray-600">Find potential function directly</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="bg-green-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-magic"></i>
                        </div>
                        <h4 class="font-semibold mb-2">Integrating Factor</h4>
                        <p class="text-sm text-gray-600">$\mu(x)$ or $\mu(y)$</p>
                        <p class="text-sm text-gray-600">Transform to exact equation</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="bg-purple-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4 class="font-semibold mb-2">Solution Curves</h4>
                        <p class="text-sm text-gray-600">$F(x,y) = C$</p>
                        <p class="text-sm text-gray-600">Family of solution curves</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation to Next Chapter -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
            <h3 class="text-xl font-semibold text-blue-800 mb-4">
                <i class="fas fa-arrow-right mr-2"></i>Ready for the Next Chapter?
            </h3>
            <p class="text-gray-600 mb-4">
                You've mastered exact equations! Next, we'll explore comprehensive modeling techniques that combine all the solution methods you've learned.
            </p>
            <div class="text-sm text-gray-500">
                <strong>Next:</strong> Chapter 10 - Modeling with First-Order ODEs
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-12 text-center text-gray-500 text-sm">
            <p>&copy; 2024 ODE Tutorial Series. Chapter 9: Exact Equations</p>
            <p>Comprehensive coverage of exact differential equations with computational implementations</p>
        </footer>
    </div>

    <!-- Chart Initialization (if needed) -->
    <script>
        // Initialize any charts or interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add any interactive functionality here
            console.log('Chapter 9: Exact Equations loaded successfully');
        });
    </script>
</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDuhZ%2Bb8lk6M1sRJ%2BH4WaovgcTYGWzIHR0nrMVa1bQmkfNYOA30DCqLrzFFscUi4LEeMeP1o6W1w6mzh%2Fu1Ql5IuJi%2Fnakzl5AvUSJe5xR1H0FYT3C6Eeq9qVjvA0DKEFqeAoxXPcDTtjv1MJ2UQ6ublnUbMUgoAvJvb6SjKQANoRdZso%2FN8z%2Fhg54NoExiz87coc4LjNAJ1%2BHfWrdB8OXnTFTcLXi9LV0l1FhyHqlEQphzW4%2B0dfI91n0mlBg0foKlBJpV4ZjmcWjWrwxclGWZG%2BQESAe6vMX9mIDmymo8lEZOTz%2BBTO2kLdSWxoVp7WJX2F8kZiF9TKYGOn9rEDEBsROhN16d%2B4a4dg6t%2B750zdKeIEJFTNoj5XEhdYnJUrWGIYRvi%2FAMYHRnybEHmo6HaKs2Y8ecc9PmXP0kg28OAlGYR6srv2YhvPRgb%2Bjf6ZayWAikzAsFjHOgaKryDHVA%2Fb54DC%2FO%2B4SG5jIk0rSChyv6AiESCPffIoeCZ3AkNJ5UCEsjpA368Hn4U6MOPqX3E%3D";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDuhZ+b8lk6M1sRJ+H4WaovgcTYGWzIHR0nrMVa1bQmkfNYOA30DCqLrzFFscUi4LEeMeP1o6W1w6mzh/u1Ql5IuJi/nakzl5AvUSJe5xR1H0FYT3C6Eeq9qVjvA0DKEFqeAoxXPcDTtjv1MJ2UQ6ublnUbMUgoAvJvb6SjKQANoRdZso/N8z/hg54NoExiz87coc4LjNAJ1+HfWrdB8OXnTFTcLXi9LV0l1FhyHqlEQphzW4+0dfI91n0mlBg0foKlBJpV4ZjmcWjWrwxclGWZG+QESAe6vMX9mIDmymo8lEZOTz+BTO2kLdSWxoVp7WJX2F8kZiF9TKYGOn9rEDEBsROhN16d+4a4dg6t+750zdKeIEJFTNoj5XEhdYnJUrWGIYRvi/AMYHRnybEHmo6HaKs2Y8ecc9PmXP0kg28OAlGYR6srv2YhvPRgb+jf6ZayWAikzAsFjHOgaKryDHVA/b54DC/O+4SG5jIk0rSChyv6AiESCPffIoeCZ3AkNJ5UCEsjpA368Hn4U6MOPqX3E=";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    