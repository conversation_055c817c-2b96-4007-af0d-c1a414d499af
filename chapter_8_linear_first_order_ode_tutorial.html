<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 8: Linear First-Order Equations - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <style>
        body { font-family: 'Georgia', serif; line-height: 1.6; }
        .math-display { margin: 1.5rem 0; }
        .code-block { margin: 1rem 0; border-radius: 8px; overflow: hidden; }
        .theorem-box { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .example-box { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .definition-box { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .application-box { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    </style>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code', 'a']
            }
        };
    </script>
</head>
<body class="bg-gray-50 text-gray-900">
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-8 px-6 rounded-xl shadow-lg">
                <h1 class="text-4xl font-bold mb-2">Chapter 8: Linear First-Order Equations</h1>
                <p class="text-xl opacity-90">Part 2: First-Order Differential Equations</p>
                <div class="mt-4 flex justify-center space-x-6 text-sm">
                    <span><i class="fas fa-book-open mr-2"></i>ODE Tutorial Series</span>
                    <span><i class="fas fa-calculator mr-2"></i>Python & R Integration</span>
                    <span><i class="fas fa-chart-line mr-2"></i>Real Applications</span>
                </div>
            </div>
        </header>

        <!-- Table of Contents -->
        <nav class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-4"><i class="fas fa-list mr-2"></i>Chapter Contents</h2>
            <div class="grid md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold text-lg text-blue-600 mb-2">Theory & Methods</h3>
                    <ul class="space-y-1 text-sm">
                        <li><a href="#standard-form" class="hover:text-blue-600">8.1 Standard Form and Classification</a></li>
                        <li><a href="#integrating-factor" class="hover:text-blue-600">8.2 Integrating Factor Method</a></li>
                        <li><a href="#solution-structure" class="hover:text-blue-600">8.3 Solution Structure</a></li>
                        <li><a href="#special-cases" class="hover:text-blue-600">8.4 Special Cases</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-lg text-green-600 mb-2">Applications & Computing</h3>
                    <ul class="space-y-1 text-sm">
                        <li><a href="#applications" class="hover:text-green-600">8.5 Real-World Applications</a></li>
                        <li><a href="#python-implementation" class="hover:text-green-600">8.6 Python Implementation</a></li>
                        <li><a href="#r-implementation" class="hover:text-green-600">8.7 R Implementation</a></li>
                        <li><a href="#exercises" class="hover:text-green-600">8.8 Exercises & Projects</a></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Section 8.1: Standard Form and Classification -->
        <section id="standard-form" class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="definition-box text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-shapes mr-3"></i>8.1 Standard Form and Classification
                    </h2>
                </div>
                <div class="p-6">
                    <div class="definition-box text-white p-4 rounded-lg mb-6">
                        <h3 class="text-xl font-bold mb-3"><i class="fas fa-info-circle mr-2"></i>Definition: Linear First-Order ODE</h3>
                        <p class="mb-4">A <strong>linear first-order differential equation</strong> has the standard form:</p>
                        <div class="bg-white bg-opacity-20 p-4 rounded text-center">
                            $$\frac{dy}{dx} + P(x)y = Q(x)$$
                        </div>
                        <p class="mt-4">where $P(x)$ and $Q(x)$ are functions of $x$ only.</p>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6 mb-8">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-bold text-blue-800 mb-2"><i class="fas fa-check-circle mr-2"></i>Linear Examples</h4>
                            <ul class="space-y-2 text-sm">
                                <li>$\frac{dy}{dx} + 2y = x^2$ ✓</li>
                                <li>$\frac{dy}{dx} - 3xy = e^x$ ✓</li>
                                <li>$x\frac{dy}{dx} + y = \sin x$ ✓</li>
                                <li>$\frac{dy}{dx} + \frac{y}{x} = x^3$ ✓</li>
                            </ul>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <h4 class="font-bold text-red-800 mb-2"><i class="fas fa-times-circle mr-2"></i>Nonlinear Examples</h4>
                            <ul class="space-y-2 text-sm">
                                <li>$\frac{dy}{dx} + y^2 = x$ ✗</li>
                                <li>$\frac{dy}{dx} + y\frac{dy}{dx} = x$ ✗</li>
                                <li>$\frac{dy}{dx} = \sqrt{y}$ ✗</li>
                                <li>$\frac{dy}{dx} + y\sin y = 0$ ✗</li>
                            </ul>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h4 class="font-bold text-lg mb-4"><i class="fas fa-sitemap mr-2"></i>Classification</h4>
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <h5 class="font-semibold text-green-600 mb-2">Homogeneous Linear ODE</h5>
                                <p class="text-sm mb-2">When $Q(x) = 0$:</p>
                                <div class="bg-white p-3 rounded border text-center">
                                    $$\frac{dy}{dx} + P(x)y = 0$$
                                </div>
                                <p class="text-sm mt-2">These are separable and have exponential solutions.</p>
                            </div>
                            <div>
                                <h5 class="font-semibold text-blue-600 mb-2">Nonhomogeneous Linear ODE</h5>
                                <p class="text-sm mb-2">When $Q(x) \neq 0$:</p>
                                <div class="bg-white p-3 rounded border text-center">
                                    $$\frac{dy}{dx} + P(x)y = Q(x)$$
                                </div>
                                <p class="text-sm mt-2">Require integrating factor method for solution.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 8.2: Integrating Factor Method -->
        <section id="integrating-factor" class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="theorem-box text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-cogs mr-3"></i>8.2 Integrating Factor Method
                    </h2>
                </div>
                <div class="p-6">
                    <div class="theorem-box text-white p-4 rounded-lg mb-6">
                        <h3 class="text-xl font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>The Integrating Factor</h3>
                        <p class="mb-4">For the linear ODE $\frac{dy}{dx} + P(x)y = Q(x)$, the integrating factor is:</p>
                        <div class="bg-white bg-opacity-20 p-4 rounded text-center">
                            $$\mu(x) = e^{\int P(x) dx}$$
                        </div>
                    </div>

                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <h4 class="font-bold text-lg mb-4"><i class="fas fa-step-forward mr-2"></i>Solution Algorithm</h4>
                        <div class="grid gap-4">
                            <div class="bg-white p-4 rounded border-l-4 border-blue-500">
                                <h5 class="font-semibold text-blue-600">Step 1: Standard Form</h5>
                                <p class="text-sm">Rewrite the equation in standard form: $\frac{dy}{dx} + P(x)y = Q(x)$</p>
                            </div>
                            <div class="bg-white p-4 rounded border-l-4 border-green-500">
                                <h5 class="font-semibold text-green-600">Step 2: Find Integrating Factor</h5>
                                <p class="text-sm">Calculate $\mu(x) = e^{\int P(x) dx}$ (don't add constant of integration)</p>
                            </div>
                            <div class="bg-white p-4 rounded border-l-4 border-purple-500">
                                <h5 class="font-semibold text-purple-600">Step 3: Multiply Through</h5>
                                <p class="text-sm">Multiply both sides by $\mu(x)$: $\mu(x)\frac{dy}{dx} + \mu(x)P(x)y = \mu(x)Q(x)$</p>
                            </div>
                            <div class="bg-white p-4 rounded border-l-4 border-red-500">
                                <h5 class="font-semibold text-red-600">Step 4: Recognize Product Rule</h5>
                                <p class="text-sm">Left side becomes: $\frac{d}{dx}[\mu(x)y] = \mu(x)Q(x)$</p>
                            </div>
                            <div class="bg-white p-4 rounded border-l-4 border-yellow-500">
                                <h5 class="font-semibold text-yellow-600">Step 5: Integrate</h5>
                                <p class="text-sm">$\mu(x)y = \int \mu(x)Q(x) dx + C$</p>
                            </div>
                            <div class="bg-white p-4 rounded border-l-4 border-indigo-500">
                                <h5 class="font-semibold text-indigo-600">Step 6: Solve for y</h5>
                                <p class="text-sm">$y = \frac{1}{\mu(x)}\left[\int \mu(x)Q(x) dx + C\right]$</p>
                            </div>
                        </div>
                    </div>

                    <div class="example-box text-white p-4 rounded-lg mb-6">
                        <h4 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 1: Complete Solution</h4>
                        <p class="mb-2">Solve: $\frac{dy}{dx} + 2y = 6e^{3x}$ with $y(0) = 1$</p>
                    </div>

                    <div class="bg-white border rounded-lg p-6 mb-6">
                        <div class="space-y-4">
                            <div>
                                <h5 class="font-semibold text-blue-600 mb-2">Step 1: Identify P(x) and Q(x)</h5>
                                <p>Equation is already in standard form: $P(x) = 2$, $Q(x) = 6e^{3x}$</p>
                            </div>
                            <div>
                                <h5 class="font-semibold text-green-600 mb-2">Step 2: Find integrating factor</h5>
                                <div class="bg-gray-50 p-3 rounded">
                                    $$\mu(x) = e^{\int 2 dx} = e^{2x}$$
                                </div>
                            </div>
                            <div>
                                <h5 class="font-semibold text-purple-600 mb-2">Step 3: Multiply by μ(x)</h5>
                                <div class="bg-gray-50 p-3 rounded">
                                    $$e^{2x}\frac{dy}{dx} + 2e^{2x}y = 6e^{5x}$$
                                </div>
                            </div>
                            <div>
                                <h5 class="font-semibold text-red-600 mb-2">Step 4: Recognize product rule</h5>
                                <div class="bg-gray-50 p-3 rounded">
                                    $$\frac{d}{dx}[e^{2x}y] = 6e^{5x}$$
                                </div>
                            </div>
                            <div>
                                <h5 class="font-semibold text-yellow-600 mb-2">Step 5: Integrate both sides</h5>
                                <div class="bg-gray-50 p-3 rounded">
                                    $$e^{2x}y = \int 6e^{5x} dx = \frac{6e^{5x}}{5} + C$$
                                </div>
                            </div>
                            <div>
                                <h5 class="font-semibold text-indigo-600 mb-2">Step 6: Solve for y</h5>
                                <div class="bg-gray-50 p-3 rounded">
                                    $$y = \frac{6e^{3x}}{5} + Ce^{-2x}$$
                                </div>
                            </div>
                            <div>
                                <h5 class="font-semibold text-gray-600 mb-2">Step 7: Apply initial condition</h5>
                                <p>$y(0) = 1$: $1 = \frac{6}{5} + C$, so $C = -\frac{1}{5}$</p>
                                <div class="bg-blue-50 p-3 rounded border-2 border-blue-300">
                                    <p class="font-semibold text-blue-800">Final Solution:</p>
                                    $$y = \frac{6e^{3x}}{5} - \frac{e^{-2x}}{5}$$
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                        <h4 class="font-bold text-yellow-800 mb-2"><i class="fas fa-exclamation-triangle mr-2"></i>Key Insight</h4>
                        <p class="text-sm">The integrating factor transforms the left side into the derivative of a product, making integration possible. This is why we don't include a constant when finding μ(x).</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 8.3: Solution Structure -->
        <section id="solution-structure" class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-green-500 to-teal-500 text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-building mr-3"></i>8.3 Solution Structure
                    </h2>
                </div>
                <div class="p-6">
                    <div class="theorem-box text-white p-4 rounded-lg mb-6">
                        <h3 class="text-xl font-bold mb-3"><i class="fas fa-puzzle-piece mr-2"></i>General Solution Structure</h3>
                        <p class="mb-4">The general solution of $\frac{dy}{dx} + P(x)y = Q(x)$ has the form:</p>
                        <div class="bg-white bg-opacity-20 p-4 rounded text-center">
                            $$y = y_h + y_p$$
                        </div>
                        <div class="mt-4 text-sm">
                            <p><strong>$y_h$:</strong> Homogeneous solution (complementary solution)</p>
                            <p><strong>$y_p$:</strong> Particular solution</p>
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-bold text-blue-800 mb-3">Homogeneous Solution</h4>
                            <p class="text-sm mb-2">Solve: $\frac{dy}{dx} + P(x)y = 0$</p>
                            <div class="bg-white p-3 rounded">
                                $$y_h = Ce^{-\int P(x) dx}$$
                            </div>
                            <p class="text-sm mt-2">Contains the arbitrary constant C</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-bold text-green-800 mb-3">Particular Solution</h4>
                            <p class="text-sm mb-2">Any solution to the full equation</p>
                            <div class="bg-white p-3 rounded">
                                $$y_p = \frac{1}{\mu(x)}\int \mu(x)Q(x) dx$$
                            </div>
                            <p class="text-sm mt-2">No arbitrary constant included</p>
                        </div>
                    </div>

                    <div class="example-box text-white p-4 rounded-lg mb-6">
                        <h4 class="text-xl font-bold mb-3"><i class="fas fa-wrench mr-2"></i>Example 2: Analyzing Solution Structure</h4>
                        <p>For $\frac{dy}{dx} + y = x$, analyze the solution components.</p>
                    </div>

                    <div class="bg-white border rounded-lg p-6 mb-6">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <h5 class="font-semibold text-blue-600 mb-2">Homogeneous Part</h5>
                                <p class="text-sm mb-2">$\frac{dy}{dx} + y = 0$</p>
                                <div class="bg-gray-50 p-3 rounded text-sm">
                                    <p>Separable: $\frac{dy}{y} = -dx$</p>
                                    <p>Solution: $y_h = Ce^{-x}$</p>
                                </div>
                            </div>
                            <div>
                                <h5 class="font-semibold text-green-600 mb-2">Particular Solution</h5>
                                <p class="text-sm mb-2">Using integrating factor $\mu(x) = e^x$</p>
                                <div class="bg-gray-50 p-3 rounded text-sm">
                                    <p>$e^x y = \int xe^x dx = xe^x - e^x$</p>
                                    <p>$y_p = x - 1$</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 bg-blue-50 p-4 rounded">
                            <h5 class="font-semibold text-blue-800 mb-2">Complete Solution</h5>
                            <div class="text-center">
                                $$y = Ce^{-x} + x - 1$$
                            </div>
                        </div>
                        <div class="mt-4">
                            <canvas id="solutionChart" style="height: 400px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 8.4: Special Cases -->
        <section id="special-cases" class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-star mr-3"></i>8.4 Special Cases and Techniques
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-bold text-purple-800 mb-3">Bernoulli Equations</h4>
                            <p class="text-sm mb-2">Form: $\frac{dy}{dx} + P(x)y = Q(x)y^n$</p>
                            <div class="bg-white p-3 rounded text-sm">
                                <p><strong>Substitution:</strong> $v = y^{1-n}$</p>
                                <p>Transforms to linear in $v$</p>
                            </div>
                        </div>
                        <div class="bg-pink-50 p-4 rounded-lg">
                            <h4 class="font-bold text-pink-800 mb-3">Riccati Equations</h4>
                            <p class="text-sm mb-2">Form: $\frac{dy}{dx} = P(x) + Q(x)y + R(x)y^2$</p>
                            <div class="bg-white p-3 rounded text-sm">
                                <p>Generally nonlinear</p>
                                <p>Linear if $R(x) = 0$</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <h4 class="font-bold text-lg mb-4"><i class="fas fa-exclamation-circle mr-2"></i>Singular Points</h4>
                        <p class="mb-4">Points where $P(x)$ or $Q(x)$ are undefined can cause solution difficulties.</p>
                        
                        <div class="example-box text-white p-4 rounded-lg mb-4">
                            <h5 class="font-bold mb-2">Example: $x\frac{dy}{dx} + y = x^2$</h5>
                            <p class="text-sm">Standard form: $\frac{dy}{dx} + \frac{1}{x}y = x$</p>
                            <p class="text-sm">Singular point at $x = 0$ where $P(x) = \frac{1}{x}$ is undefined</p>
                        </div>

                        <div class="bg-white p-4 rounded border">
                            <h5 class="font-semibold mb-2">Solution Strategy:</h5>
                            <div class="text-sm space-y-2">
                                <p>1. Find integrating factor: $\mu(x) = e^{\int \frac{1}{x} dx} = |x|$</p>
                                <p>2. For $x > 0$: $\mu(x) = x$</p>
                                <p>3. Multiply: $x\frac{dy}{dx} + y = x^2$</p>
                                <p>4. Recognize: $\frac{d}{dx}[xy] = x^2$</p>
                                <p>5. Integrate: $xy = \frac{x^3}{3} + C$</p>
                                <p>6. Solution: $y = \frac{x^2}{3} + \frac{C}{x}$ for $x \neq 0$</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                        <h4 class="font-bold text-yellow-800 mb-2"><i class="fas fa-lightbulb mr-2"></i>Connection to Variation of Parameters</h4>
                        <p class="text-sm">The integrating factor method is equivalent to variation of parameters for first-order linear equations. Both methods give the same solution structure $y = y_h + y_p$.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 8.5: Applications -->
        <section id="applications" class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="application-box text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-rocket mr-3"></i>8.5 Real-World Applications
                    </h2>
                </div>
                <div class="p-6">
                    <!-- RC Circuits -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4"><i class="fas fa-bolt mr-2"></i>RC Electrical Circuits</h3>
                        <div class="bg-blue-50 p-6 rounded-lg">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-blue-800 mb-3">Circuit Analysis</h4>
                                    <p class="text-sm mb-2">By Kirchhoff's voltage law:</p>
                                    <div class="bg-white p-3 rounded">
                                        $$L\frac{di}{dt} + Ri = V(t)$$
                                    </div>
                                    <p class="text-sm mt-2">For RC circuit (L=0):</p>
                                    <div class="bg-white p-3 rounded">
                                        $$R\frac{dq}{dt} + \frac{q}{C} = V(t)$$
                                    </div>
                                    <p class="text-sm mt-2">Since $i = \frac{dq}{dt}$</p>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-blue-800 mb-3">Solution Process</h4>
                                    <div class="text-sm space-y-2">
                                        <p><strong>Standard form:</strong> $\frac{dq}{dt} + \frac{q}{RC} = \frac{V(t)}{R}$</p>
                                        <p><strong>P(t) =</strong> $\frac{1}{RC}$, <strong>Q(t) =</strong> $\frac{V(t)}{R}$</p>
                                        <p><strong>Integrating factor:</strong> $\mu(t) = e^{t/(RC)}$</p>
                                        <p><strong>Time constant:</strong> $\tau = RC$</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="example-box text-white p-4 rounded-lg mt-4 mb-4">
                            <h4 class="text-xl font-bold mb-2">Example: RC Circuit with Step Input</h4>
                            <p class="text-sm">R = 1000Ω, C = 10μF, V(t) = 12V (step), q(0) = 0</p>
                        </div>

                        <div class="bg-white border rounded-lg p-4">
                            <div class="text-sm space-y-2">
                                <p>Equation: $\frac{dq}{dt} + 100q = 0.012$</p>
                                <p>Integrating factor: $\mu(t) = e^{100t}$</p>
                                <p>Solution: $q(t) = 1.2 \times 10^{-4}(1 - e^{-100t})$ Coulombs</p>
                                <p>Current: $i(t) = 0.012e^{-100t}$ Amperes</p>
                            </div>
                            <div class="mt-4">
                                <canvas id="rcCircuitChart" style="height: 300px;"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Mixing Problems -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4"><i class="fas fa-flask mr-2"></i>Mixing Problems</h3>
                        <div class="bg-green-50 p-6 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-3">Tank Mixing Model</h4>
                            <p class="text-sm mb-4">A tank contains V liters of solution. Pure water flows in at rate $r_{in}$ L/min, mixture flows out at rate $r_{out}$ L/min.</p>
                            
                            <div class="bg-white p-4 rounded mb-4">
                                <p class="text-sm mb-2"><strong>Rate of change = Rate in - Rate out</strong></p>
                                <div class="text-center">
                                    $$\frac{dS}{dt} = r_{in} \cdot c_{in} - r_{out} \cdot \frac{S(t)}{V(t)}$$
                                </div>
                                <p class="text-sm mt-2">Where S(t) = amount of salt, c_{in} = input concentration</p>
                            </div>

                            <div class="example-box text-white p-4 rounded-lg mb-4">
                                <h4 class="text-lg font-bold mb-2">Example: Salt Water Tank</h4>
                                <p class="text-sm">1000L tank, salt water (0.1 kg/L) in at 50 L/min, mixture out at 50 L/min</p>
                            </div>

                            <div class="bg-white border rounded-lg p-4">
                                <div class="text-sm space-y-2">
                                    <p>Volume constant: V(t) = 1000L</p>
                                    <p>Equation: $\frac{dS}{dt} = 50(0.1) - 50\frac{S}{1000} = 5 - 0.05S$</p>
                                    <p>Standard form: $\frac{dS}{dt} + 0.05S = 5$</p>
                                    <p>Solution: $S(t) = 100(1 - e^{-0.05t})$ kg</p>
                                    <p>Equilibrium: $S_{eq} = 100$ kg (concentration = 0.1 kg/L)</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Population with Harvesting -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4"><i class="fas fa-fish mr-2"></i>Population Models with Harvesting</h3>
                        <div class="bg-purple-50 p-6 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-3">Mathematical Model</h4>
                            <p class="text-sm mb-2">Population growth with constant harvesting rate:</p>
                            <div class="bg-white p-3 rounded mb-4">
                                $$\frac{dP}{dt} = rP - H$$
                            </div>
                            <p class="text-sm mb-4">This is linear: $\frac{dP}{dt} - rP = -H$</p>
                            
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="bg-white p-4 rounded">
                                    <h5 class="font-semibold text-purple-600 mb-2">Solution</h5>
                                    <div class="text-sm space-y-1">
                                        <p>Integrating factor: $\mu(t) = e^{-rt}$</p>
                                        <p>General solution:</p>
                                        <div class="text-center">
                                            $$P(t) = \frac{H}{r} + Ce^{rt}$$
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white p-4 rounded">
                                    <h5 class="font-semibold text-purple-600 mb-2">Analysis</h5>
                                    <div class="text-sm space-y-1">
                                        <p>Equilibrium: $P^* = \frac{H}{r}$</p>
                                        <p>If $P_0 > P^*$: exponential growth</p>
                                        <p>If $P_0 < P^*$: population crashes</p>
                                        <p>Critical harvesting: $H = rP_0$</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 8.6: Python Implementation -->
        <section id="python-implementation" class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-yellow-500 to-orange-500 text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fab fa-python mr-3"></i>8.6 Python Implementation
                    </h2>
                </div>
                <div class="p-6">
                    <!-- Symbolic Solutions -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Symbolic Solutions with SymPy</h3>
                        <div class="code-block">
                            <pre><code class="language-python">import sympy as sp
import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint

# Define symbols
x, y, C = sp.symbols('x y C')

def solve_linear_ode_symbolic(P_func, Q_func, var='x'):
    """
    Solve first-order linear ODE dy/dx + P(x)y = Q(x) symbolically
    """
    x = sp.Symbol(var)
    y = sp.Function('y')
    
    # Define the ODE
    ode = sp.Eq(y(x).diff(x) + P_func * y(x), Q_func)
    
    # Solve using SymPy
    general_solution = sp.dsolve(ode, y(x))
    
    print(f"ODE: dy/dx + ({P_func})y = {Q_func}")
    print(f"General Solution: {general_solution}")
    
    return general_solution

# Example 1: dy/dx + 2y = 6*exp(3x)
print("=== Example 1: dy/dx + 2y = 6*exp(3x) ===")
P1 = 2
Q1 = 6*sp.exp(3*x)
sol1 = solve_linear_ode_symbolic(P1, Q1)

# Extract solution function
y_sol1 = sp.solve(sol1, sp.Function('y')(x))[0]
print(f"y(x) = {y_sol1}")

# Apply initial condition y(0) = 1
C1_val = sp.solve(y_sol1.subs(x, 0) - 1, sp.Symbol('C1'))[0]
y_particular1 = y_sol1.subs(sp.Symbol('C1'), C1_val)
print(f"Particular solution with y(0)=1: y(x) = {y_particular1}")
</code></pre>
                        </div>
                    </div>

                    <!-- Integrating Factor Calculator -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Integrating Factor Calculator</h3>
                        <div class="code-block">
                            <pre><code class="language-python">def integrating_factor_method(P_func, Q_func, initial_condition=None):
    """
    Solve linear ODE using integrating factor method
    """
    x = sp.Symbol('x')
    
    # Step 1: Find integrating factor
    mu = sp.exp(sp.integrate(P_func, x))
    print(f"P(x) = {P_func}")
    print(f"Integrating factor μ(x) = exp(∫P(x)dx) = {mu}")
    
    # Step 2: Multiply by integrating factor and integrate
    integrand = mu * Q_func
    print(f"μ(x)Q(x) = {integrand}")
    
    # Step 3: Integrate
    integral = sp.integrate(integrand, x)
    print(f"∫μ(x)Q(x)dx = {integral}")
    
    # Step 4: General solution
    C = sp.Symbol('C')
    y_general = (integral + C) / mu
    y_general = sp.simplify(y_general)
    print(f"General solution: y = {y_general}")
    
    # Step 5: Apply initial condition if given
    if initial_condition:
        x0, y0 = initial_condition
        C_val = sp.solve(y_general.subs(x, x0) - y0, C)[0]
        y_particular = y_general.subs(C, C_val)
        y_particular = sp.simplify(y_particular)
        print(f"Particular solution: y = {y_particular}")
        return y_particular
    
    return y_general

# Example: dy/dx + y/x = x^2 with y(1) = 2
print("\n=== Example: dy/dx + y/x = x^2, y(1) = 2 ===")
P_ex = 1/x
Q_ex = x**2
solution = integrating_factor_method(P_ex, Q_ex, (1, 2))
</code></pre>
                        </div>
                    </div>

                    <!-- Numerical Solutions and Visualization -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Numerical Solutions and Visualization</h3>
                        <div class="code-block">
                            <pre><code class="language-python">def plot_linear_ode_solutions(P_func, Q_func, x_range, initial_conditions, title=""):
    """
    Plot family of solutions for linear ODE
    """
    x_vals = np.linspace(x_range[0], x_range[1], 1000)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Convert symbolic functions to numeric
    P_numeric = sp.lambdify(x, P_func, 'numpy')
    Q_numeric = sp.lambdify(x, Q_func, 'numpy')
    
    def ode_system(y, x):
        return -P_numeric(x) * y + Q_numeric(x)
    
    # Plot solution family
    colors = plt.cm.viridis(np.linspace(0, 1, len(initial_conditions)))
    for i, (x0, y0) in enumerate(initial_conditions):
        # Solve numerically
        if x0 in x_vals:
            idx = np.argmin(np.abs(x_vals - x0))
            x_forward = x_vals[idx:]
            x_backward = x_vals[:idx+1][::-1]
            
            if len(x_forward) > 1:
                sol_forward = odeint(ode_system, y0, x_forward)
                ax1.plot(x_forward, sol_forward[:, 0], color=colors[i], 
                        label=f'y({x0}) = {y0}', linewidth=2)
            
            if len(x_backward) > 1:
                sol_backward = odeint(ode_system, y0, x_backward)
                ax1.plot(x_backward[::-1], sol_backward[::-1, 0], 
                        color=colors[i], linewidth=2)
    
    ax1.set_xlabel('x')
    ax1.set_ylabel('y')
    ax1.set_title(f'{title} - Solution Family')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Direction field
    x_dir = np.linspace(x_range[0], x_range[1], 20)
    y_dir = np.linspace(-5, 5, 20)
    X_dir, Y_dir = np.meshgrid(x_dir, y_dir)
    
    DY = -P_numeric(X_dir) * Y_dir + Q_numeric(X_dir)
    DX = np.ones_like(DY)
    
    # Normalize arrows
    N = np.sqrt(DX**2 + DY**2)
    DX, DY = DX/N, DY/N
    
    ax2.quiver(X_dir, Y_dir, DX, DY, alpha=0.6, color='gray')
    
    # Overlay solutions
    for i, (x0, y0) in enumerate(initial_conditions):
        if x0 in x_vals:
            idx = np.argmin(np.abs(x_vals - x0))
            x_forward = x_vals[idx:]
            
            if len(x_forward) > 1:
                sol_forward = odeint(ode_system, y0, x_forward)
                ax2.plot(x_forward, sol_forward[:, 0], color=colors[i], 
                        linewidth=2)
    
    ax2.set_xlabel('x')
    ax2.set_ylabel('y')
    ax2.set_title(f'{title} - Direction Field')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Example: dy/dx + y = x
print("Plotting solutions for dy/dx + y = x")
P_plot = 1
Q_plot = x
initial_conditions = [(0, -1), (0, 0), (0, 1), (0, 2)]
plot_linear_ode_solutions(P_plot, Q_plot, (-2, 3), initial_conditions, 
                         "dy/dx + y = x")
</code></pre>
                        </div>
                    </div>

                    <!-- Parameter Estimation -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Parameter Estimation from Data</h3>
                        <div class="code-block">
                            <pre><code class="language-python">from scipy.optimize import curve_fit
from sklearn.metrics import r2_score

def fit_linear_ode_model(t_data, y_data, model_type='exponential'):
    """
    Fit linear ODE models to experimental data
    """
    
    if model_type == 'exponential':
        # Model: dy/dt = -ky, solution: y = y0 * exp(-kt)
        def model_func(t, y0, k):
            return y0 * np.exp(-k * t)
        
        # Initial guess
        p0 = [y_data[0], 0.1]
        
    elif model_type == 'logistic_linear':
        # Model: dy/dt = r*y - h, solution: y = h/r + (y0 - h/r)*exp(rt)
        def model_func(t, y0, r, h):
            return h/r + (y0 - h/r) * np.exp(r * t)
        
        p0 = [y_data[0], 0.1, 0.1]
    
    # Fit the model
    try:
        popt, pcov = curve_fit(model_func, t_data, y_data, p0=p0, maxfev=5000)
        
        # Calculate R-squared
        y_pred = model_func(t_data, *popt)
        r2 = r2_score(y_data, y_pred)
        
        # Parameter uncertainties
        param_errors = np.sqrt(np.diag(pcov))
        
        print(f"Model: {model_type}")
        print(f"Fitted parameters: {popt}")
        print(f"Parameter errors: {param_errors}")
        print(f"R-squared: {r2:.4f}")
        
        # Plot results
        plt.figure(figsize=(10, 6))
        plt.scatter(t_data, y_data, color='red', label='Data', s=50, zorder=5)
        
        t_smooth = np.linspace(t_data.min(), t_data.max(), 200)
        y_smooth = model_func(t_smooth, *popt)
        plt.plot(t_smooth, y_smooth, 'b-', label=f'Fitted {model_type}', linewidth=2)
        
        plt.xlabel('Time')
        plt.ylabel('y(t)')
        plt.title(f'{model_type.title()} Model Fit (R² = {r2:.4f})')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()
        
        return popt, pcov, r2
        
    except Exception as e:
        print(f"Fitting failed: {e}")
        return None, None, None

# Generate synthetic data for demonstration
np.random.seed(42)
t_exp = np.linspace(0, 5, 20)
y_true = 2 * np.exp(-0.5 * t_exp)
y_data = y_true + 0.1 * np.random.normal(size=len(t_exp))

# Fit exponential decay model
params, cov, r2 = fit_linear_ode_model(t_exp, y_data, 'exponential')
</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 8.7: R Implementation -->
        <section id="r-implementation" class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fab fa-r-project mr-3"></i>8.7 R Implementation
                    </h2>
                </div>
                <div class="p-6">
                    <!-- Symbolic Solutions in R -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Symbolic Solutions with Ryacas</h3>
                        <div class="code-block">
                            <pre><code class="language-r"># Load required libraries
library(Ryacas)
library(deSolve)
library(ggplot2)
library(plotly)
library(pracma)

# Function to solve linear ODE symbolically
solve_linear_ode_symbolic <- function(P_expr, Q_expr) {
  # Create Ryacas expressions
  cat("Solving ODE: dy/dx + (", P_expr, ")y = ", Q_expr, "\n")
  
  # Define the equation symbolically
  # Note: Ryacas syntax for ODE solving
  ode_expr <- paste("OdeSolve(y'(x) + (", P_expr, ")*y(x) == ", Q_expr, ", x, y(x))")
  
  # Solve using Ryacas
  tryCatch({
    solution <- yac_str(ode_expr)
    cat("Symbolic solution:\n", solution, "\n")
    return(solution)
  }, error = function(e) {
    cat("Symbolic solution failed, using manual integrating factor method\n")
    return(NULL)
  })
}

# Manual integrating factor method
integrating_factor_method <- function(P_func, Q_func, x_vals, initial_condition = NULL) {
  # Calculate integrating factor numerically
  # mu(x) = exp(integral of P(x))
  
  P_values <- sapply(x_vals, P_func)
  
  # Numerical integration for mu(x)
  mu_values <- numeric(length(x_vals))
  mu_values[1] <- 1
  
  for(i in 2:length(x_vals)) {
    dx <- x_vals[i] - x_vals[i-1]
    integral_P <- trapz(x_vals[1:i], P_values[1:i])
    mu_values[i] <- exp(integral_P)
  }
  
  # Calculate solution
  Q_values <- sapply(x_vals, Q_func)
  mu_Q_product <- mu_values * Q_values
  
  # Integrate mu(x)*Q(x)
  integral_muQ <- numeric(length(x_vals))
  for(i in 2:length(x_vals)) {
    integral_muQ[i] <- trapz(x_vals[1:i], mu_Q_product[1:i])
  }
  
  # General solution: y = (integral + C) / mu(x)
  # If initial condition given, find C
  C <- 0
  if(!is.null(initial_condition)) {
    x0 <- initial_condition[1]
    y0 <- initial_condition[2]
    idx <- which.min(abs(x_vals - x0))
    C <- y0 * mu_values[idx] - integral_muQ[idx]
  }
  
  y_solution <- (integral_muQ + C) / mu_values
  
  return(list(
    x = x_vals,
    y = y_solution,
    mu = mu_values,
    C = C
  ))
}

# Example 1: dy/dx + 2y = 6*exp(3x)
cat("=== Example 1: dy/dx + 2y = 6*exp(3x) ===\n")

P1 <- function(x) 2
Q1 <- function(x) 6 * exp(3*x)

x_range <- seq(0, 2, length.out = 100)
solution1 <- integrating_factor_method(P1, Q1, x_range, c(0, 1))

cat("Integrating factor μ(x) = exp(2x)\n")
cat("Solution with y(0) = 1 calculated numerically\n")
</code></pre>
                        </div>
                    </div>

                    <!-- deSolve Package Solutions -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Numerical Solutions with deSolve</h3>
                        <div class="code-block">
                            <pre><code class="language-r"># Function to solve linear ODE using deSolve
solve_linear_ode_numerical <- function(P_func, Q_func, initial_condition, time_span) {
  # Define the ODE system
  linear_ode <- function(t, y, parameters) {
    dydt <- -P_func(t) * y + Q_func(t)
    return(list(dydt))
  }
  
  # Solve the ODE
  solution <- ode(y = initial_condition[2], 
                  times = time_span, 
                  func = linear_ode, 
                  parms = NULL,
                  method = "lsoda")
  
  return(as.data.frame(solution))
}

# Multiple initial conditions
solve_ode_family <- function(P_func, Q_func, initial_conditions, time_span) {
  solutions <- list()
  
  for(i in 1:nrow(initial_conditions)) {
    ic <- initial_conditions[i, ]
    sol <- solve_linear_ode_numerical(P_func, Q_func, ic, time_span)
    sol$ic_label <- paste0("y(", ic[1], ") = ", ic[2])
    solutions[[i]] <- sol
  }
  
  # Combine all solutions
  combined_solutions <- do.call(rbind, solutions)
  return(combined_solutions)
}

# Example: dy/dx + y = x
P2 <- function(x) 1
Q2 <- function(x) x

# Define initial conditions
initial_conditions <- data.frame(
  x0 = c(0, 0, 0, 0),
  y0 = c(-1, 0, 1, 2)
)

time_span <- seq(-1, 3, length.out = 200)

# Solve for family of solutions
family_solutions <- solve_ode_family(P2, Q2, initial_conditions, time_span)

# Plot using ggplot2
p1 <- ggplot(family_solutions, aes(x = time, y = `1`, color = ic_label)) +
  geom_line(size = 1.2) +
  labs(
    title = "Linear ODE: dy/dx + y = x",
    subtitle = "Family of solutions with different initial conditions",
    x = "x", 
    y = "y(x)",
    color = "Initial Condition"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 16, face = "bold"),
    legend.position = "bottom"
  ) +
  scale_color_viridis_d()

print(p1)
</code></pre>
                        </div>
                    </div>

                    <!-- Interactive Visualization -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Interactive Visualization</h3>
                        <div class="code-block">
                            <pre><code class="language-r"># Create direction field visualization
create_direction_field <- function(P_func, Q_func, x_range, y_range, 
                                 initial_conditions = NULL, title = "") {
  
  # Create grid for direction field
  x_grid <- seq(x_range[1], x_range[2], length.out = 20)
  y_grid <- seq(y_range[1], y_range[2], length.out = 20)
  
  # Create direction field data
  dir_field_data <- expand.grid(x = x_grid, y = y_grid)
  dir_field_data$dx <- 1
  dir_field_data$dy <- -P_func(dir_field_data$x) * dir_field_data$y + 
                       Q_func(dir_field_data$x)
  
  # Normalize direction vectors
  magnitude <- sqrt(dir_field_data$dx^2 + dir_field_data$dy^2)
  dir_field_data$dx_norm <- dir_field_data$dx / magnitude * 0.1
  dir_field_data$dy_norm <- dir_field_data$dy / magnitude * 0.1
  
  # Base plot
  p <- ggplot() +
    geom_segment(data = dir_field_data,
                aes(x = x, y = y, 
                    xend = x + dx_norm, yend = y + dy_norm),
                arrow = arrow(length = unit(0.02, "npc")),
                alpha = 0.6, color = "gray50") +
    labs(title = paste("Direction Field:", title),
         x = "x", y = "y") +
    theme_minimal()
  
  # Add solution curves if initial conditions provided
  if(!is.null(initial_conditions)) {
    time_span <- seq(x_range[1], x_range[2], length.out = 200)
    solutions <- solve_ode_family(P_func, Q_func, initial_conditions, time_span)
    
    p <- p + geom_line(data = solutions, 
                      aes(x = time, y = `1`, color = ic_label),
                      size = 1.2) +
          scale_color_viridis_d(name = "Initial Condition")
  }
  
  return(p)
}

# Example: Create interactive direction field
P_example <- function(x) 1
Q_example <- function(x) x

initial_cond <- data.frame(x0 = c(0, 0), y0 = c(-1, 2))

dir_plot <- create_direction_field(P_example, Q_example, 
                                 c(-2, 3), c(-3, 4), 
                                 initial_cond, "dy/dx + y = x")

# Make it interactive with plotly
interactive_plot <- ggplotly(dir_plot)
interactive_plot
</code></pre>
                        </div>
                    </div>

                    <!-- Statistical Analysis -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Statistical Model Fitting</h3>
                        <div class="code-block">
                            <pre><code class="language-r"># Function for fitting ODE models to data
fit_linear_ode_model <- function(t_data, y_data, model_type = "exponential") {
  
  if(model_type == "exponential") {
    # Model: dy/dt = -k*y, solution: y = y0 * exp(-k*t)
    model_func <- function(t, y0, k) {
      y0 * exp(-k * t)
    }
    
    # Fit using nonlinear least squares
    fit <- tryCatch({
      nls(y_data ~ y0 * exp(-k * t_data), 
          start = list(y0 = y_data[1], k = 0.1))
    }, error = function(e) {
      cat("NLS fitting failed, trying alternative approach\n")
      return(NULL)
    })
    
  } else if(model_type == "linear_with_constant") {
    # Model: dy/dt + a*y = b, solution: y = b/a + (y0 - b/a)*exp(-a*t)
    model_func <- function(t, y0, a, b) {
      b/a + (y0 - b/a) * exp(-a * t)
    }
    
    fit <- tryCatch({
      nls(y_data ~ b/a + (y0 - b/a) * exp(-a * t_data),
          start = list(y0 = y_data[1], a = 0.5, b = 0.1))
    }, error = function(e) {
      return(NULL)
    })
  }
  
  if(!is.null(fit)) {
    # Extract results
    params <- coef(fit)
    param_se <- summary(fit)$coefficients[, "Std. Error"]
    
    # Calculate R-squared
    y_pred <- predict(fit)
    ss_res <- sum((y_data - y_pred)^2)
    ss_tot <- sum((y_data - mean(y_data))^2)
    r_squared <- 1 - (ss_res / ss_tot)
    
    # Create results data frame
    results <- data.frame(
      Parameter = names(params),
      Estimate = as.numeric(params),
      Std_Error = param_se,
      stringsAsFactors = FALSE
    )
    
    cat("Model:", model_type, "\n")
    print(results)
    cat("R-squared:", round(r_squared, 4), "\n\n")
    
    # Plot results
    t_smooth <- seq(min(t_data), max(t_data), length.out = 200)
    y_smooth <- predict(fit, newdata = list(t_data = t_smooth))
    
    plot_data <- data.frame(
      t_obs = t_data, y_obs = y_data,
      t_fit = t_smooth, y_fit = y_smooth
    )
    
    p <- ggplot(plot_data) +
      geom_point(aes(x = t_obs, y = y_obs), color = "red", size = 3) +
      geom_line(aes(x = t_fit, y = y_fit), color = "blue", size = 1.2) +
      labs(
        title = paste("ODE Model Fit:", model_type),
        subtitle = paste("R² =", round(r_squared, 4)),
        x = "Time", y = "y(t)"
      ) +
      theme_minimal()
    
    print(p)
    
    return(list(fit = fit, results = results, r_squared = r_squared))
  } else {
    cat("Model fitting failed\n")
    return(NULL)
  }
}

# Generate synthetic data
set.seed(123)
t_data <- seq(0, 3, length.out = 15)
y_true <- 2 * exp(-0.8 * t_data)
y_data <- y_true + rnorm(length(t_data), 0, 0.1)

# Fit exponential decay model
fit_results <- fit_linear_ode_model(t_data, y_data, "exponential")
</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 8.8: Exercises and Projects -->
        <section id="exercises" class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-red-500 to-pink-500 text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-dumbbell mr-3"></i>8.8 Exercises and Projects
                    </h2>
                </div>
                <div class="p-6">
                    <!-- Basic Exercises -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Basic Exercises</h3>
                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-bold text-blue-800 mb-3">Exercise 1-5: Solve by Hand</h4>
                                <ol class="text-sm space-y-2">
                                    <li>1. $\frac{dy}{dx} + 3y = e^{2x}$</li>
                                    <li>2. $\frac{dy}{dx} - 2y = x^2$</li>
                                    <li>3. $x\frac{dy}{dx} + y = x^3$</li>
                                    <li>4. $\frac{dy}{dx} + y\tan x = \sec x$</li>
                                    <li>5. $\frac{dy}{dx} + \frac{2y}{x} = \frac{\sin x}{x^2}$</li>
                                </ol>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-bold text-green-800 mb-3">Exercise 6-10: Initial Value Problems</h4>
                                <ol class="text-sm space-y-2" start="6">
                                    <li>6. $\frac{dy}{dx} + y = 2x$, $y(0) = 1$</li>
                                    <li>7. $\frac{dy}{dx} - 3y = e^x$, $y(0) = 2$</li>
                                    <li>8. $\frac{dy}{dx} + \frac{y}{x} = 1$, $y(1) = 2$</li>
                                    <li>9. $\frac{dy}{dx} + 2xy = x$, $y(0) = 0$</li>
                                    <li>10. $(1+x^2)\frac{dy}{dx} + 2xy = \frac{1}{1+x^2}$, $y(0) = 1$</li>
                                </ol>
                            </div>
                        </div>
                    </div>

                    <!-- Application Problems -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Application Problems</h3>
                        <div class="space-y-6">
                            <div class="bg-purple-50 p-6 rounded-lg">
                                <h4 class="font-bold text-purple-800 mb-3"><i class="fas fa-flask mr-2"></i>Problem 11: Chemical Mixing</h4>
                                <p class="text-sm mb-3">A 1000-liter tank initially contains 50 kg of salt dissolved in water. Pure water flows in at 25 L/min, and the mixture flows out at the same rate. Set up and solve the differential equation for the amount of salt S(t) at time t.</p>
                                <div class="bg-white p-3 rounded text-sm">
                                    <p><strong>Tasks:</strong></p>
                                    <ul class="list-disc ml-4 space-y-1">
                                        <li>Derive the differential equation</li>
                                        <li>Solve using integrating factor method</li>
                                        <li>Find when salt concentration drops to 1% of initial</li>
                                        <li>Plot S(t) and concentration C(t) = S(t)/1000</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="bg-orange-50 p-6 rounded-lg">
                                <h4 class="font-bold text-orange-800 mb-3"><i class="fas fa-bolt mr-2"></i>Problem 12: RC Circuit Analysis</h4>
                                <p class="text-sm mb-3">An RC circuit has R = 500Ω and C = 200μF. At t = 0, the capacitor is uncharged and a voltage source V(t) = 12sin(ωt) is applied.</p>
                                <div class="bg-white p-3 rounded text-sm">
                                    <p><strong>Tasks:</strong></p>
                                    <ul class="list-disc ml-4 space-y-1">
                                        <li>Set up the differential equation for charge q(t)</li>
                                        <li>Solve for q(t) and current i(t) = dq/dt</li>
                                        <li>Analyze behavior for ω = 10, 100, 1000 rad/s</li>
                                        <li>Plot voltage, charge, and current vs time</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="bg-teal-50 p-6 rounded-lg">
                                <h4 class="font-bold text-teal-800 mb-3"><i class="fas fa-chart-line mr-2"></i>Problem 13: Population with Immigration</h4>
                                <p class="text-sm mb-3">A population grows according to dP/dt = 0.02P + 1000, where the first term represents natural growth and the second term represents constant immigration.</p>
                                <div class="bg-white p-3 rounded text-sm">
                                    <p><strong>Tasks:</strong></p>
                                    <ul class="list-disc ml-4 space-y-1">
                                        <li>Solve the differential equation</li>
                                        <li>Find the equilibrium population</li>
                                        <li>Analyze long-term behavior</li>
                                        <li>Compare with and without immigration</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Computational Projects -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Computational Projects</h3>
                        <div class="space-y-6">
                            <div class="bg-gradient-to-r from-blue-100 to-blue-200 p-6 rounded-lg">
                                <h4 class="font-bold text-blue-800 mb-3"><i class="fas fa-laptop-code mr-2"></i>Project 1: ODE Solver Implementation</h4>
                                <p class="text-sm mb-3">Create a comprehensive linear ODE solver in Python or R.</p>
                                <div class="bg-white p-4 rounded">
                                    <p class="font-semibold mb-2">Requirements:</p>
                                    <ul class="text-sm list-disc ml-4 space-y-1">
                                        <li>Automatic integrating factor calculation</li>
                                        <li>Symbolic and numerical solution options</li>
                                        <li>Solution verification functionality</li>
                                        <li>Visualization of solution families</li>
                                        <li>Parameter sensitivity analysis</li>
                                        <li>Error analysis and convergence testing</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="bg-gradient-to-r from-green-100 to-green-200 p-6 rounded-lg">
                                <h4 class="font-bold text-green-800 mb-3"><i class="fas fa-database mr-2"></i>Project 2: Real Data Analysis</h4>
                                <p class="text-sm mb-3">Fit linear ODE models to real experimental data.</p>
                                <div class="bg-white p-4 rounded">
                                    <p class="font-semibold mb-2">Data Sources:</p>
                                    <ul class="text-sm list-disc ml-4 space-y-1">
                                        <li>Temperature cooling data (coffee, hot water)</li>
                                        <li>Capacitor discharge measurements</li>
                                        <li>Population data with harvesting</li>
                                        <li>Drug concentration in blood</li>
                                        <li>Radioactive decay measurements</li>
                                    </ul>
                                    <p class="font-semibold mt-3 mb-2">Analysis Tasks:</p>
                                    <ul class="text-sm list-disc ml-4 space-y-1">
                                        <li>Model selection and validation</li>
                                        <li>Parameter estimation with confidence intervals</li>
                                        <li>Residual analysis and diagnostics</li>
                                        <li>Prediction and forecasting</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Challenge Problems -->
                    <div class="bg-red-50 p-6 rounded-lg">
                        <h4 class="font-bold text-red-800 mb-4"><i class="fas fa-trophy mr-2"></i>Challenge Problems</h4>
                        <div class="space-y-4">
                            <div class="bg-white p-4 rounded">
                                <h5 class="font-semibold text-red-600 mb-2">Challenge 1: Variable Coefficients</h5>
                                <p class="text-sm">Solve $x^2\frac{dy}{dx} + 3xy = x^4$ and analyze the solution near the singular point x = 0.</p>
                            </div>
                            <div class="bg-white p-4 rounded">
                                <h5 class="font-semibold text-red-600 mb-2">Challenge 2: Discontinuous Forcing</h5>
                                <p class="text-sm">Solve $\frac{dy}{dx} + y = f(x)$ where f(x) is a step function. Analyze solution continuity.</p>
                            </div>
                            <div class="bg-white p-4 rounded">
                                <h5 class="font-semibold text-red-600 mb-2">Challenge 3: Optimization</h5>
                                <p class="text-sm">Find the harvesting rate that maximizes long-term yield in the population model $\frac{dP}{dt} = rP - H$.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Chapter Summary -->
        <section class="mb-12">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-gray-700 to-gray-900 text-white p-6">
                    <h2 class="text-3xl font-bold flex items-center">
                        <i class="fas fa-graduation-cap mr-3"></i>Chapter Summary
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-xl font-bold text-gray-800 mb-4">Key Concepts Mastered</h3>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Linear first-order ODE standard form</li>
                                <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Integrating factor method derivation and application</li>
                                <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Solution structure: homogeneous + particular</li>
                                <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Initial value problem solving</li>
                                <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Real-world modeling applications</li>
                                <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Computational implementation in Python and R</li>
                                <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Parameter estimation from experimental data</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-800 mb-4">Applications Explored</h3>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-start"><i class="fas fa-flask text-blue-500 mr-2 mt-1"></i>Tank mixing problems</li>
                                <li class="flex items-start"><i class="fas fa-bolt text-yellow-500 mr-2 mt-1"></i>RC electrical circuits</li>
                                <li class="flex items-start"><i class="fas fa-fish text-green-500 mr-2 mt-1"></i>Population models with harvesting</li>
                                <li class="flex items-start"><i class="fas fa-thermometer-half text-red-500 mr-2 mt-1"></i>Newton's law of cooling</li>
                                <li class="flex items-start"><i class="fas fa-pills text-purple-500 mr-2 mt-1"></i>Drug concentration modeling</li>
                                <li class="flex items-start"><i class="fas fa-chart-line text-indigo-500 mr-2 mt-1"></i>Economic growth models</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-8 bg-blue-50 p-6 rounded-lg">
                        <h3 class="text-xl font-bold text-blue-800 mb-3">Bridge to Next Chapter</h3>
                        <p class="text-sm text-gray-700">
                            Chapter 8 focused on linear first-order ODEs, where the unknown function y appears linearly. 
                            In Chapter 9, we'll explore <strong>Exact Equations</strong>, which have a special structure 
                            that allows for direct integration using a different approach. While linear equations require 
                            the integrating factor method, exact equations can often be solved more directly through 
                            recognition of their geometric properties and conservative nature.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="text-center py-8 bg-gray-100 rounded-lg">
            <p class="text-gray-600">
                <i class="fas fa-book mr-2"></i>ODE Tutorial Series - Chapter 8: Linear First-Order Equations
            </p>
            <p class="text-sm text-gray-500 mt-2">
                Next: Chapter 9 - Exact Equations | Previous: Chapter 7 - Separable Equations
            </p>
        </footer>
    </div>

    <!-- Charts and Interactive Elements -->
    <script>
        // Solution Structure Chart
        const ctx1 = document.getElementById('solutionChart');
        if (ctx1) {
            new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: Array.from({length: 100}, (_, i) => (i * 0.05 - 2).toFixed(2)),
                    datasets: [
                        {
                            label: 'Homogeneous Solution: Ce^(-x)',
                            data: Array.from({length: 100}, (_, i) => {
                                const x = i * 0.05 - 2;
                                return Math.exp(-x);
                            }),
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'Particular Solution: x - 1',
                            data: Array.from({length: 100}, (_, i) => {
                                const x = i * 0.05 - 2;
                                return x - 1;
                            }),
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'General Solution: Ce^(-x) + x - 1 (C=1)',
                            data: Array.from({length: 100}, (_, i) => {
                                const x = i * 0.05 - 2;
                                return Math.exp(-x) + x - 1;
                            }),
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.1)',
                            borderWidth: 3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'x'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'y'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Solution Structure: dy/dx + y = x'
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        }

        // RC Circuit Chart
        const ctx2 = document.getElementById('rcCircuitChart');
        if (ctx2) {
            new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: Array.from({length: 100}, (_, i) => (i * 0.1).toFixed(2)),
                    datasets: [
                        {
                            label: 'Charge q(t) (×10^4 C)',
                            data: Array.from({length: 100}, (_, i) => {
                                const t = i * 0.1;
                                return 1.2 * (1 - Math.exp(-100 * t));
                            }),
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.1)',
                            borderWidth: 2,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Current i(t) (A)',
                            data: Array.from({length: 100}, (_, i) => {
                                const t = i * 0.1;
                                return 0.012 * Math.exp(-100 * t);
                            }),
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.1)',
                            borderWidth: 2,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Time (s)'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Charge (×10^-4 C)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Current (A)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'RC Circuit Response (R=1000Ω, C=10μF, V=12V)'
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDuGBMtpJnIOCgj9Q1%2B3spvanytpzdPf2HT1G2B8hXczhLnLkpFDFEAGDEonHnQld%2BPwwXuUqykNE%2BxGk3%2Fi0P60Pl7HjpKD2jaTICUC5oG3qmYkcia%2FvkRY5Jfx7GZxLIb6%2Bk7%2BBbK8YjjIlY1yHdZWlmw7BL83XRxISVjH%2FSK9%2FnL5Y29yGTmJBg4j%2BKNqiODqewk6CAK69A5eZmfj3v2v0Q%2F9eHNOYyOe7INY%2Bc4phKA1a9UxaQYKu6FueNxvnXxLfEdb0e98szgFhVe9IyUJ07kBRiER0FZBuLq5TI%2Bhb2IQCyTuLjFC7bmXbV8o1dkwqi2K3%2B5CRZIsHM3wN2ta0zlzd0zI%2BdGB%2BTRwfEUJCYEzsgVpJ1TtSFQP1OvmanaI9yYAW7fpkxq5qztumXERaT52EKJILb3WYDoEDWjvKIbk5kFPcjLl4QIrIIpfUqwfBwfZuuz4mPLl5sWyGbGL9l0vBq%2BV4VBlmRrTolEcgIKFGHB6rnPKyeYG8c3qn5AmVPMKu2uNzD44BKQrJe6I3%2BqzGWT7ndI%2FeBi%2BXcxZM";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDuGBMtpJnIOCgj9Q1+3spvanytpzdPf2HT1G2B8hXczhLnLkpFDFEAGDEonHnQld+PwwXuUqykNE+xGk3/i0P60Pl7HjpKD2jaTICUC5oG3qmYkcia/vkRY5Jfx7GZxLIb6+k7+BbK8YjjIlY1yHdZWlmw7BL83XRxISVjH/SK9/nL5Y29yGTmJBg4j+KNqiODqewk6CAK69A5eZmfj3v2v0Q/9eHNOYyOe7INY+c4phKA1a9UxaQYKu6FueNxvnXxLfEdb0e98szgFhVe9IyUJ07kBRiER0FZBuLq5TI+hb2IQCyTuLjFC7bmXbV8o1dkwqi2K3+5CRZIsHM3wN2ta0zlzd0zI+dGB+TRwfEUJCYEzsgVpJ1TtSFQP1OvmanaI9yYAW7fpkxq5qztumXERaT52EKJILb3WYDoEDWjvKIbk5kFPcjLl4QIrIIpfUqwfBwfZuuz4mPLl5sWyGbGL9l0vBq+V4VBlmRrTolEcgIKFGHB6rnPKyeYG8c3qn5AmVPMKu2uNzD44BKQrJe6I3+qzGWT7ndI/eBi+XcxZM";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    