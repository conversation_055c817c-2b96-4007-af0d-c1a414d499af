<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 4: Introduction to Mathematical Modeling - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        .code-block {
            background-color: #1a1a1a;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        .python-code { border-left: 4px solid #3776ab; }
        .r-code { border-left: 4px solid #276dc3; }
        .output-block {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 1rem;
            margin: 1rem 0;
        }
        .highlight { background-color: #fff3cd; padding: 0.2rem; border-radius: 4px; }
        .theorem-box {
            background-color: #e3f2fd;
            border-left: 6px solid #2196f3;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
        }
        .example-box {
            background-color: #f3e5f5;
            border-left: 6px solid #9c27b0;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
        }
        .warning-box {
            background-color: #fff3e0;
            border-left: 6px solid #ff9800;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
        }
        .chart-container {
            height: 400px;
            margin: 1rem 0;
        }
        .exercise-box {
            background-color: #e8f5e8;
            border-left: 6px solid #4caf50;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
        }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        .step-number {
            background-color: #2196f3;
            color: white;
            border-radius: 50%;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-calculator text-blue-600 mr-3"></i>
                    Chapter 4: Introduction to Mathematical Modeling
                </h1>
                <p class="text-xl text-gray-600 mb-4">Building Bridges Between Reality and Mathematics</p>
                <div class="flex justify-center space-x-8 text-sm text-gray-500">
                    <span><i class="fas fa-book mr-2"></i>ODE Tutorial Series</span>
                    <span><i class="fas fa-layer-group mr-2"></i>Part 1: Mathematical Foundations</span>
                    <span><i class="fas fa-clock mr-2"></i>Est. Reading Time: 45 minutes</span>
                </div>
            </div>
        </div>

        <!-- Learning Objectives -->
        <div class="bg-blue-50 border-l-4 border-blue-500 p-6 mb-8 rounded-r-lg">
            <h2 class="text-2xl font-bold text-blue-800 mb-4">
                <i class="fas fa-target mr-2"></i>Learning Objectives
            </h2>
            <div class="grid md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold text-blue-700 mb-2">By the end of this chapter, you will:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li><i class="fas fa-check-circle text-green-500 mr-2"></i>Understand the mathematical modeling process</li>
                        <li><i class="fas fa-check-circle text-green-500 mr-2"></i>Translate word problems into mathematical expressions</li>
                        <li><i class="fas fa-check-circle text-green-500 mr-2"></i>Apply dimensional analysis and model validation</li>
                        <li><i class="fas fa-check-circle text-green-500 mr-2"></i>Distinguish between different modeling paradigms</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-700 mb-2">Practical Skills:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li><i class="fas fa-code text-purple-500 mr-2"></i>Implement models in Python and R</li>
                        <li><i class="fas fa-chart-line text-orange-500 mr-2"></i>Perform parameter estimation and sensitivity analysis</li>
                        <li><i class="fas fa-flask text-green-500 mr-2"></i>Apply models to real-world scenarios</li>
                        <li><i class="fas fa-arrows-alt text-red-500 mr-2"></i>Prepare for differential equation formulations</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Section 1: What is Mathematical Modeling? -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-question-circle text-blue-600 mr-3"></i>
                1. What is Mathematical Modeling?
            </h2>

            <div class="prose max-w-none">
                <p class="text-lg text-gray-700 mb-6">
                    Mathematical modeling is the process of creating mathematical representations of real-world phenomena. 
                    It serves as a bridge between abstract mathematical concepts and practical applications, allowing us to 
                    analyze, predict, and understand complex systems using the language of mathematics.
                </p>

                <div class="theorem-box">
                    <h3 class="text-xl font-semibold text-blue-800 mb-3">
                        <i class="fas fa-lightbulb mr-2"></i>Definition: Mathematical Model
                    </h3>
                    <p class="text-gray-700">
                        A <strong>mathematical model</strong> is a mathematical representation of a real-world system, 
                        phenomenon, or process that captures its essential features while simplifying or abstracting 
                        away less important details.
                    </p>
                </div>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4 mt-8">The Modeling Cycle</h3>
                
                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="step-number">1</div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Problem Identification</h4>
                                    <p class="text-gray-600 text-sm">Define the real-world problem clearly</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="step-number">2</div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Assumptions & Simplifications</h4>
                                    <p class="text-gray-600 text-sm">Identify key variables and relationships</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="step-number">3</div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Mathematical Formulation</h4>
                                    <p class="text-gray-600 text-sm">Translate to mathematical expressions</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="step-number">4</div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Solution & Analysis</h4>
                                    <p class="text-gray-600 text-sm">Solve the mathematical problem</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="step-number">5</div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Validation & Interpretation</h4>
                                    <p class="text-gray-600 text-sm">Check results against reality</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="modelingCycleChart"></canvas>
                    </div>
                </div>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4">Types of Mathematical Models</h3>
                
                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">
                            <i class="fas fa-layer-group mr-2"></i>By Complexity
                        </h4>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• Linear vs. Nonlinear</li>
                            <li>• Deterministic vs. Stochastic</li>
                            <li>• Static vs. Dynamic</li>
                        </ul>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-lg">
                        <h4 class="font-semibold text-green-800 mb-2">
                            <i class="fas fa-clock mr-2"></i>By Time Dependence
                        </h4>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• Discrete Time</li>
                            <li>• Continuous Time</li>
                            <li>• Time-invariant</li>
                        </ul>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-lg">
                        <h4 class="font-semibold text-purple-800 mb-2">
                            <i class="fas fa-microscope mr-2"></i>By Scale
                        </h4>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• Microscopic</li>
                            <li>• Macroscopic</li>
                            <li>• Multi-scale</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section 2: Interpreting Word Problems -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-language text-green-600 mr-3"></i>
                2. Interpreting Word Problems
            </h2>

            <div class="prose max-w-none">
                <p class="text-lg text-gray-700 mb-6">
                    The ability to translate written descriptions into mathematical expressions is fundamental to 
                    mathematical modeling. This skill bridges the gap between everyday language and mathematical precision.
                </p>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4">Key Translation Strategies</h3>

                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">
                                <i class="fas fa-search mr-2"></i>Identify Key Information
                            </h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• What is being asked?</li>
                                <li>• What quantities are given?</li>
                                <li>• What relationships exist?</li>
                                <li>• What are the constraints?</li>
                            </ul>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">
                                <i class="fas fa-tags mr-2"></i>Define Variables
                            </h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Use descriptive variable names</li>
                                <li>• Specify units clearly</li>
                                <li>• Distinguish between parameters and variables</li>
                                <li>• Consider domain restrictions</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">
                                <i class="fas fa-link mr-2"></i>Establish Relationships
                            </h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Proportional relationships</li>
                                <li>• Rate of change descriptions</li>
                                <li>• Conservation principles</li>
                                <li>• Geometric constraints</li>
                            </ul>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">
                                <i class="fas fa-check-double mr-2"></i>Verify Reasonableness
                            </h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Check dimensional consistency</li>
                                <li>• Test with simple cases</li>
                                <li>• Verify boundary conditions</li>
                                <li>• Consider physical plausibility</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="example-box">
                    <h3 class="text-xl font-semibold text-purple-800 mb-3">
                        <i class="fas fa-flask mr-2"></i>Example 1: Population Growth Problem
                    </h3>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800 italic">
                            "A bacterial culture initially contains 1000 bacteria. The population doubles every 3 hours. 
                            How many bacteria will there be after 12 hours?"
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Step 1: Identify Information</h4>
                            <ul class="text-gray-700 space-y-1 ml-4">
                                <li>• Initial population: $P_0 = 1000$ bacteria</li>
                                <li>• Doubling time: $t_d = 3$ hours</li>
                                <li>• Time of interest: $t = 12$ hours</li>
                                <li>• Question: Find $P(12)$</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Step 2: Mathematical Formulation</h4>
                            <p class="text-gray-700 mb-2">
                                For exponential growth with doubling time $t_d$:
                            </p>
                            <p class="text-center bg-white p-2 rounded border">
                                $P(t) = P_0 \cdot 2^{t/t_d}$
                            </p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Step 3: Solution</h4>
                            <p class="text-gray-700">
                                $P(12) = 1000 \cdot 2^{12/3} = 1000 \cdot 2^4 = 1000 \cdot 16 = 16,000$ bacteria
                            </p>
                        </div>
                    </div>
                </div>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4 mt-8">Common Word Problem Patterns</h3>

                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300 rounded-lg">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 p-3 text-left font-semibold">Pattern</th>
                                <th class="border border-gray-300 p-3 text-left font-semibold">Key Phrases</th>
                                <th class="border border-gray-300 p-3 text-left font-semibold">Mathematical Translation</th>
                            </tr>
                        </thead>
                        <tbody class="text-sm">
                            <tr>
                                <td class="border border-gray-300 p-3 font-medium">Rate of Change</td>
                                <td class="border border-gray-300 p-3">"increases at a rate of", "decreases by", "per unit time"</td>
                                <td class="border border-gray-300 p-3">$\frac{dy}{dt} = k$ or $\frac{dy}{dt} = f(t)$</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td class="border border-gray-300 p-3 font-medium">Proportional</td>
                                <td class="border border-gray-300 p-3">"is proportional to", "varies as", "directly related"</td>
                                <td class="border border-gray-300 p-3">$y = kx$ or $\frac{dy}{dt} = ky$</td>
                            </tr>
                            <tr>
                                <td class="border border-gray-300 p-3 font-medium">Optimization</td>
                                <td class="border border-gray-300 p-3">"maximum", "minimum", "optimize", "best"</td>
                                <td class="border border-gray-300 p-3">$\frac{df}{dx} = 0$, $f''(x) \lessgtr 0$</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td class="border border-gray-300 p-3 font-medium">Conservation</td>
                                <td class="border border-gray-300 p-3">"total amount", "conserved", "balance"</td>
                                <td class="border border-gray-300 p-3">$\text{Input} - \text{Output} = \text{Accumulation}$</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Section 3: Dimensional Analysis -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-ruler-combined text-red-600 mr-3"></i>
                3. Dimensional Analysis and Units
            </h2>

            <div class="prose max-w-none">
                <p class="text-lg text-gray-700 mb-6">
                    Dimensional analysis is a powerful tool for checking the consistency of mathematical models 
                    and deriving relationships between physical quantities. It ensures that our equations make 
                    physical sense and can help identify errors early in the modeling process.
                </p>

                <div class="theorem-box">
                    <h3 class="text-xl font-semibold text-blue-800 mb-3">
                        <i class="fas fa-balance-scale mr-2"></i>Principle of Dimensional Homogeneity
                    </h3>
                    <p class="text-gray-700 mb-2">
                        A physically meaningful equation must be dimensionally consistent: both sides of the equation 
                        must have the same dimensions.
                    </p>
                    <p class="text-gray-700 text-sm">
                        <strong>Consequence:</strong> We can only add, subtract, or equate quantities that have the same dimensions.
                    </p>
                </div>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4">Fundamental Dimensions</h3>

                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">SI Base Dimensions</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                <span class="font-medium">Length</span>
                                <span class="text-blue-600">[L]</span>
                            </div>
                            <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                <span class="font-medium">Mass</span>
                                <span class="text-blue-600">[M]</span>
                            </div>
                            <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                <span class="font-medium">Time</span>
                                <span class="text-blue-600">[T]</span>
                            </div>
                            <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                <span class="font-medium">Temperature</span>
                                <span class="text-blue-600">[Θ]</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">Derived Dimensions</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                <span class="font-medium">Velocity</span>
                                <span class="text-green-600">[LT⁻¹]</span>
                            </div>
                            <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                <span class="font-medium">Acceleration</span>
                                <span class="text-green-600">[LT⁻²]</span>
                            </div>
                            <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                <span class="font-medium">Force</span>
                                <span class="text-green-600">[MLT⁻²]</span>
                            </div>
                            <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                <span class="font-medium">Energy</span>
                                <span class="text-green-600">[ML²T⁻²]</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="example-box">
                    <h3 class="text-xl font-semibold text-purple-800 mb-3">
                        <i class="fas fa-calculator mr-2"></i>Example 2: Dimensional Analysis of Pendulum Period
                    </h3>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800 italic">
                            "Derive the functional form for the period of a simple pendulum using dimensional analysis. 
                            Consider that the period T might depend on the length L, gravitational acceleration g, 
                            and possibly the mass m and amplitude θ."
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Step 1: Identify Dimensions</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-white p-3 rounded border">
                                    <p>$[T] = \text{[T]}$ (time)</p>
                                    <p>$[L] = \text{[L]}$ (length)</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <p>$[g] = \text{[LT}^{-2}]$ (acceleration)</p>
                                    <p>$[m] = \text{[M]}$ (mass)</p>
                                    <p>$[\theta] = \text{dimensionless}$</p>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Step 2: Assume Functional Form</h4>
                            <p class="text-gray-700 mb-2">
                                Let $T = k \cdot L^a \cdot g^b \cdot m^c \cdot \theta^d$, where $k$ is dimensionless.
                            </p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Step 3: Dimensional Equation</h4>
                            <p class="text-center bg-white p-2 rounded border">
                                $[\text{T}] = [\text{L}]^a \cdot [\text{LT}^{-2}]^b \cdot [\text{M}]^c$
                            </p>
                            <p class="text-center bg-white p-2 rounded border mt-2">
                                $[\text{T}] = [\text{L}]^{a+b} \cdot [\text{T}]^{-2b} \cdot [\text{M}]^c$
                            </p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Step 4: Solve for Exponents</h4>
                            <div class="bg-white p-3 rounded border">
                                <p>For $[\text{T}]$: $1 = -2b \Rightarrow b = -\frac{1}{2}$</p>
                                <p>For $[\text{L}]$: $0 = a + b \Rightarrow a = \frac{1}{2}$</p>
                                <p>For $[\text{M}]$: $0 = c \Rightarrow c = 0$</p>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Step 5: Result</h4>
                            <p class="text-center bg-blue-50 p-3 rounded border text-lg">
                                $T = k \sqrt{\frac{L}{g}} \cdot f(\theta)$
                            </p>
                            <p class="text-gray-700 text-sm mt-2">
                                The mass doesn't affect the period, and the amplitude dependence is captured by $f(\theta)$.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="code-block python-code">
                    <h4 class="text-white font-semibold mb-3">
                        <i class="fab fa-python mr-2"></i>Python Implementation: Dimensional Analysis
                    </h4>
                    <pre class="text-green-300"><code># Dimensional Analysis Toolkit
import numpy as np
import matplotlib.pyplot as plt
from sympy import symbols, solve, simplify
import sympy as sp

class DimensionalAnalysis:
    """
    A class for performing dimensional analysis
    """
    
    def __init__(self):
        # Define fundamental dimensions
        self.dimensions = {
            'L': 'Length',
            'M': 'Mass', 
            'T': 'Time',
            'K': 'Temperature',
            'A': 'Current',
            'N': 'Amount',
            'J': 'Luminous Intensity'
        }
        
    def check_dimensional_consistency(self, equation_terms):
        """
        Check if terms in an equation have consistent dimensions
        
        Args:
            equation_terms: List of dictionaries with dimensions
                          e.g. [{'L': 1, 'T': -1}, {'L': 1, 'T': -1}]
        
        Returns:
            bool: True if dimensionally consistent
        """
        if len(equation_terms) < 2:
            return True
            
        reference = equation_terms[0]
        for term in equation_terms[1:]:
            if term != reference:
                return False
        return True
    
    def derive_scaling_law(self, target_quantity, influencing_quantities):
        """
        Derive scaling relationships using dimensional analysis
        
        Args:
            target_quantity: Dictionary of target dimensions
            influencing_quantities: List of dictionaries of influencing dimensions
        
        Returns:
            Scaling exponents
        """
        # This is a simplified version - in practice, we'd solve the system
        # of linear equations for the exponents
        n_vars = len(influencing_quantities)
        n_dims = len(set().union(*(d.keys() for d in influencing_quantities + [target_quantity])))
        
        # Create matrix equation A*x = b where x are the exponents
        # This is a conceptual framework - full implementation would be more complex
        print(f"System has {n_vars} variables and {n_dims} dimensional constraints")
        print("Degrees of freedom:", n_vars - n_dims)
        
        return "Dimensional analysis framework established"

# Example: Pendulum period analysis
da = DimensionalAnalysis()

# Define dimensions for pendulum problem
target = {'T': 1}  # Period [T]
quantities = [
    {'L': 1},        # Length [L]
    {'L': 1, 'T': -2},  # Gravity [LT^-2]
    {'M': 1},        # Mass [M]
]

print("Pendulum Period Dimensional Analysis:")
print("=====================================")
result = da.derive_scaling_law(target, quantities)
print(result)

# Verify dimensional consistency
velocity_terms = [
    {'L': 1, 'T': -1},  # v = dx/dt
    {'L': 1, 'T': -1}   # Another velocity term
]

print(f"\nVelocity terms consistent: {da.check_dimensional_consistency(velocity_terms)}")

# Inconsistent example
inconsistent_terms = [
    {'L': 1, 'T': -1},  # Velocity
    {'L': 1, 'T': -2}   # Acceleration
]

print(f"Inconsistent terms: {da.check_dimensional_consistency(inconsistent_terms)}")

# Practical example: Free fall
print("\nFree Fall Example:")
print("==================")
print("Distance: s = v₀t + ½gt²")
print("Term 1 [v₀t]: [LT⁻¹][T] = [L]")
print("Term 2 [½gt²]: [LT⁻²][T²] = [L]")
print("Dimensionally consistent: ✓")

# Visualization of dimensional relationships
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# Pendulum period vs length
L = np.linspace(0.1, 2, 100)
g = 9.81
T_theoretical = 2 * np.pi * np.sqrt(L / g)

ax1.plot(L, T_theoretical, 'b-', linewidth=2, label='T ∝ √L')
ax1.set_xlabel('Length L (m)')
ax1.set_ylabel('Period T (s)')
ax1.set_title('Pendulum Period vs Length')
ax1.grid(True, alpha=0.3)
ax1.legend()

# Scaling relationship
log_L = np.log10(L)
log_T = np.log10(T_theoretical)
slope = np.polyfit(log_L, log_T, 1)[0]

ax2.plot(log_L, log_T, 'r-', linewidth=2, label=f'Slope = {slope:.2f} ≈ 0.5')
ax2.set_xlabel('log₁₀(L)')
ax2.set_ylabel('log₁₀(T)')
ax2.set_title('Log-Log Plot: Scaling Verification')
ax2.grid(True, alpha=0.3)
ax2.legend()

plt.tight_layout()
plt.show()

print(f"\nScaling exponent from fit: {slope:.3f}")
print(f"Theoretical exponent: 0.5")
print(f"Relative error: {abs(slope - 0.5)/0.5 * 100:.1f}%")</code></pre>
                </div>

                <div class="code-block r-code">
                    <h4 class="text-white font-semibold mb-3">
                        <i class="fab fa-r-project mr-2"></i>R Implementation: Dimensional Analysis
                    </h4>
                    <pre class="text-blue-300"><code># Dimensional Analysis in R
library(ggplot2)
library(dplyr)
library(gridExtra)

# Function to check dimensional consistency
check_dimensions <- function(dim_list) {
  # Check if all elements in the list have the same dimensions
  reference <- dim_list[[1]]
  all(sapply(dim_list, function(x) identical(x, reference)))
}

# Function for dimensional analysis of scaling laws
dimensional_scaling <- function(target_dims, variable_dims) {
  # Create a framework for dimensional analysis
  # This is a simplified version for educational purposes
  
  n_vars <- length(variable_dims)
  n_dims <- length(unique(unlist(c(target_dims, variable_dims))))
  
  cat("Dimensional Analysis Summary:\n")
  cat("============================\n")
  cat("Number of variables:", n_vars, "\n")
  cat("Number of dimensions:", n_dims, "\n")
  cat("Degrees of freedom:", n_vars - n_dims, "\n")
  
  return(list(
    n_variables = n_vars,
    n_dimensions = n_dims,
    degrees_freedom = n_vars - n_dims
  ))
}

# Example 1: Pendulum period
cat("PENDULUM PERIOD ANALYSIS\n")
cat("========================\n")

# Define dimensional vectors (exponents of L, M, T)
period_dims <- c(0, 0, 1)      # [T]
length_dims <- c(1, 0, 0)      # [L]
gravity_dims <- c(1, 0, -2)    # [LT^-2]
mass_dims <- c(0, 1, 0)        # [M]

# Dimensional analysis
target <- list(L = 0, M = 0, T = 1)
variables <- list(
  length = list(L = 1, M = 0, T = 0),
  gravity = list(L = 1, M = 0, T = -2),
  mass = list(L = 0, M = 1, T = 0)
)

analysis <- dimensional_scaling(target, variables)

# Example 2: Verify dimensional consistency
cat("\nDIMENSIONAL CONSISTENCY CHECK\n")
cat("=============================\n")

# Velocity terms: v = dx/dt
velocity_terms <- list(
  list(L = 1, M = 0, T = -1),
  list(L = 1, M = 0, T = -1)
)

cat("Velocity terms consistent:", check_dimensions(velocity_terms), "\n")

# Inconsistent terms
inconsistent_terms <- list(
  list(L = 1, M = 0, T = -1),  # Velocity
  list(L = 1, M = 0, T = -2)   # Acceleration
)

cat("Inconsistent terms:", check_dimensions(inconsistent_terms), "\n")

# Practical demonstration: Pendulum scaling
L <- seq(0.1, 2, length.out = 100)
g <- 9.81
T_theoretical <- 2 * pi * sqrt(L / g)

# Create data frame for plotting
pendulum_data <- data.frame(
  Length = L,
  Period = T_theoretical,
  log_Length = log10(L),
  log_Period = log10(T_theoretical)
)

# Plot 1: Period vs Length
p1 <- ggplot(pendulum_data, aes(x = Length, y = Period)) +
  geom_line(color = "blue", size = 1.2) +
  labs(
    title = "Pendulum Period vs Length",
    subtitle = "Theoretical relationship: T ∝ √L",
    x = "Length L (m)",
    y = "Period T (s)"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "gray60")
  )

# Plot 2: Log-log plot for scaling verification
fit <- lm(log_Period ~ log_Length, data = pendulum_data)
slope <- coef(fit)[2]

p2 <- ggplot(pendulum_data, aes(x = log_Length, y = log_Period)) +
  geom_line(color = "red", size = 1.2) +
  geom_smooth(method = "lm", se = TRUE, alpha = 0.2) +
  labs(
    title = "Log-Log Plot: Scaling Verification",
    subtitle = paste("Slope =", round(slope, 3), "≈ 0.5"),
    x = "log₁₀(L)",
    y = "log₁₀(T)"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "gray60")
  )

# Display plots
grid.arrange(p1, p2, ncol = 2)

# Numerical verification
cat("\nSCALING VERIFICATION\n")
cat("====================\n")
cat("Fitted scaling exponent:", round(slope, 3), "\n")
cat("Theoretical exponent: 0.5\n")
cat("Relative error:", round(abs(slope - 0.5)/0.5 * 100, 1), "%\n")

# Advanced: Buckingham Pi theorem demonstration
cat("\nBUCKINGHAM PI THEOREM EXAMPLE\n")
cat("=============================\n")

# For a more complex problem: drag force on a sphere
# Variables: F (force), ρ (density), v (velocity), d (diameter), μ (viscosity)
# Dimensions: [MLT^-2], [ML^-3], [LT^-1], [L], [ML^-1T^-1]

drag_variables <- list(
  force = list(L = 1, M = 1, T = -2),
  density = list(L = -3, M = 1, T = 0),
  velocity = list(L = 1, M = 0, T = -1),
  diameter = list(L = 1, M = 0, T = 0),
  viscosity = list(L = -1, M = 1, T = -1)
)

drag_analysis <- dimensional_scaling(list(L = 1, M = 1, T = -2), drag_variables)
cat("Expected dimensionless groups (π terms):", drag_analysis$degrees_freedom, "\n")
cat("This leads to: F = f(ρv²d², μ/(ρvd)) = ρv²d² × f(Re)\n")
cat("Where Re = ρvd/μ is the Reynolds number\n")</code></pre>
                </div>
            </div>
        </div>

        <!-- Section 4: Modeling Paradigms -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-sitemap text-purple-600 mr-3"></i>
                4. Modeling Paradigms
            </h2>

            <div class="prose max-w-none">
                <p class="text-lg text-gray-700 mb-6">
                    Different types of mathematical models serve different purposes and are appropriate for different 
                    kinds of problems. Understanding these paradigms helps us choose the right approach for our modeling goals.
                </p>

                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg">
                        <h3 class="text-xl font-semibold text-blue-800 mb-4">
                            <i class="fas fa-chart-line mr-2"></i>Deterministic Models
                        </h3>
                        <p class="text-gray-700 mb-3">
                            Output is completely determined by the input parameters and initial conditions. 
                            No randomness is involved.
                        </p>
                        <div class="bg-white p-3 rounded border">
                            <h4 class="font-semibold text-blue-700 mb-2">Examples:</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Newton's laws of motion</li>
                                <li>• Population growth models</li>
                                <li>• Chemical reaction kinetics</li>
                                <li>• Electrical circuit analysis</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-lg">
                        <h3 class="text-xl font-semibold text-green-800 mb-4">
                            <i class="fas fa-dice mr-2"></i>Stochastic Models
                        </h3>
                        <p class="text-gray-700 mb-3">
                            Include random variables and probability distributions. Account for uncertainty 
                            and variability in the system.
                        </p>
                        <div class="bg-white p-3 rounded border">
                            <h4 class="font-semibold text-green-700 mb-2">Examples:</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Stock price models</li>
                                <li>• Weather prediction</li>
                                <li>• Epidemiological models</li>
                                <li>• Quantum mechanics</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4">Linear vs. Nonlinear Models</h3>

                <div class="overflow-x-auto mb-8">
                    <table class="w-full border-collapse border border-gray-300 rounded-lg">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 p-4 text-left font-semibold">Aspect</th>
                                <th class="border border-gray-300 p-4 text-left font-semibold">Linear Models</th>
                                <th class="border border-gray-300 p-4 text-left font-semibold">Nonlinear Models</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="border border-gray-300 p-4 font-medium">Mathematical Form</td>
                                <td class="border border-gray-300 p-4">$ay + by' + cy'' = f(t)$</td>
                                <td class="border border-gray-300 p-4">$(y')^2 + \sin(y) = 0$</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td class="border border-gray-300 p-4 font-medium">Superposition</td>
                                <td class="border border-gray-300 p-4">✓ Applies</td>
                                <td class="border border-gray-300 p-4">✗ Does not apply</td>
                            </tr>
                            <tr>
                                <td class="border border-gray-300 p-4 font-medium">Solution Methods</td>
                                <td class="border border-gray-300 p-4">Analytical, systematic</td>
                                <td class="border border-gray-300 p-4">Often numerical</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td class="border border-gray-300 p-4 font-medium">Behavior</td>
                                <td class="border border-gray-300 p-4">Predictable, proportional</td>
                                <td class="border border-gray-300 p-4">Complex, emergent</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4">Discrete vs. Continuous Models</h3>

                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div class="space-y-4">
                        <div class="bg-orange-50 p-6 rounded-lg border-l-4 border-orange-400">
                            <h4 class="font-semibold text-orange-800 mb-3">
                                <i class="fas fa-step-forward mr-2"></i>Discrete Models
                            </h4>
                            <p class="text-gray-700 mb-3">
                                Variables change at specific time points. Often use difference equations.
                            </p>
                            <div class="bg-white p-3 rounded border">
                                <p class="text-sm text-gray-600 mb-2"><strong>Example:</strong> Population growth</p>
                                <p class="text-center">$P_{n+1} = rP_n(1 - P_n/K)$</p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-100 p-4 rounded-lg">
                            <h5 class="font-semibold text-gray-800 mb-2">When to Use:</h5>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Discrete events (births, deaths)</li>
                                <li>• Seasonal effects</li>
                                <li>• Computer simulations</li>
                                <li>• Economic models (annual data)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-400">
                            <h4 class="font-semibold text-blue-800 mb-3">
                                <i class="fas fa-wave-square mr-2"></i>Continuous Models
                            </h4>
                            <p class="text-gray-700 mb-3">
                                Variables change continuously with time. Use differential equations.
                            </p>
                            <div class="bg-white p-3 rounded border">
                                <p class="text-sm text-gray-600 mb-2"><strong>Example:</strong> Population growth</p>
                                <p class="text-center">$\frac{dP}{dt} = rP(1 - P/K)$</p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-100 p-4 rounded-lg">
                            <h5 class="font-semibold text-gray-800 mb-2">When to Use:</h5>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Physical processes</li>
                                <li>• Large populations</li>
                                <li>• Smooth changes</li>
                                <li>• Mathematical tractability</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="example-box">
                    <h3 class="text-xl font-semibold text-purple-800 mb-3">
                        <i class="fas fa-vial mr-2"></i>Example 3: Comparing Discrete and Continuous Models
                    </h3>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800 italic">
                            "Model bacterial growth in a petri dish. Compare discrete and continuous approaches."
                        </p>
                    </div>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-orange-700 mb-2">Discrete Model</h4>
                            <div class="bg-white p-3 rounded border">
                                <p class="text-sm mb-2">Generation-based growth:</p>
                                <p class="text-center">$P_{n+1} = P_n \cdot r$</p>
                                <p class="text-sm mt-2">where $n$ = generation number</p>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-700 mb-2">Continuous Model</h4>
                            <div class="bg-white p-3 rounded border">
                                <p class="text-sm mb-2">Time-based growth:</p>
                                <p class="text-center">$\frac{dP}{dt} = rP$</p>
                                <p class="text-sm mt-2">Solution: $P(t) = P_0 e^{rt}$</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="modelComparisonChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Section 5: Case Studies -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-microscope text-orange-600 mr-3"></i>
                5. Case Studies Leading to Differential Equations
            </h2>

            <div class="prose max-w-none">
                <p class="text-lg text-gray-700 mb-6">
                    Real-world problems often naturally lead to differential equations. Here we explore several 
                    case studies that demonstrate the modeling process and bridge toward ODE formulations.
                </p>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4">Case Study 1: Population Dynamics</h3>
                
                <div class="example-box">
                    <h4 class="text-lg font-semibold text-purple-800 mb-3">
                        <i class="fas fa-seedling mr-2"></i>Logistic Growth Model
                    </h4>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800 italic">
                            "A population of rabbits is introduced to an island. Initially, they grow exponentially, 
                            but as resources become limited, the growth rate decreases. The island can support at most 
                            1000 rabbits (carrying capacity)."
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h5 class="font-semibold text-gray-800 mb-2">Modeling Process:</h5>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="bg-white p-3 rounded border">
                                    <h6 class="font-semibold text-blue-700 mb-2">Variables & Parameters</h6>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        <li>• $P(t)$ = population at time $t$</li>
                                        <li>• $r$ = intrinsic growth rate</li>
                                        <li>• $K$ = carrying capacity</li>
                                        <li>• $P_0$ = initial population</li>
                                    </ul>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <h6 class="font-semibold text-green-700 mb-2">Assumptions</h6>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        <li>• Growth rate decreases linearly with population</li>
                                        <li>• No migration or disease</li>
                                        <li>• Resources are the limiting factor</li>
                                        <li>• Continuous time model</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-semibold text-gray-800 mb-2">Mathematical Formulation:</h5>
                            <div class="bg-white p-4 rounded border">
                                <p class="text-center text-lg mb-2">
                                    $\frac{dP}{dt} = rP\left(1 - \frac{P}{K}\right)$
                                </p>
                                <p class="text-sm text-gray-600 text-center">
                                    This is the logistic differential equation
                                </p>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-semibold text-gray-800 mb-2">Solution & Interpretation:</h5>
                            <div class="bg-white p-4 rounded border">
                                <p class="text-center mb-2">
                                    $P(t) = \frac{K P_0}{P_0 + (K - P_0)e^{-rt}}$
                                </p>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• S-shaped (sigmoid) growth curve</li>
                                    <li>• Initially exponential growth</li>
                                    <li>• Levels off at carrying capacity</li>
                                    <li>• Inflection point at $P = K/2$</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4 mt-8">Case Study 2: Cooling/Heating Problems</h3>
                
                <div class="example-box">
                    <h4 class="text-lg font-semibold text-purple-800 mb-3">
                        <i class="fas fa-thermometer-half mr-2"></i>Newton's Law of Cooling
                    </h4>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800 italic">
                            "A hot coffee cup at 90°C is placed in a room at 20°C. The temperature of the coffee 
                            decreases at a rate proportional to the temperature difference between the coffee and the room."
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h5 class="font-semibold text-gray-800 mb-2">Physical Principle:</h5>
                            <div class="bg-white p-3 rounded border">
                                <p class="text-gray-700 mb-2">
                                    Heat transfer rate is proportional to temperature difference:
                                </p>
                                <p class="text-center">
                                    $\frac{dT}{dt} = -k(T - T_{\text{ambient}})$
                                </p>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-semibold text-gray-800 mb-2">Solution:</h5>
                            <div class="bg-white p-3 rounded border">
                                <p class="text-center mb-2">
                                    $T(t) = T_{\text{ambient}} + (T_0 - T_{\text{ambient}})e^{-kt}$
                                </p>
                                <p class="text-sm text-gray-600">
                                    Exponential approach to ambient temperature
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <h3 class="text-2xl font-semibold text-gray-800 mb-4 mt-8">Case Study 3: Mixing Problems</h3>
                
                <div class="example-box">
                    <h4 class="text-lg font-semibold text-purple-800 mb-3">
                        <i class="fas fa-flask mr-2"></i>Salt Concentration in a Tank
                    </h4>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800 italic">
                            "A 1000-liter tank initially contains pure water. Salt water with concentration 0.5 kg/L 
                            flows in at 10 L/min, and the well-mixed solution flows out at the same rate. 
                            Find the salt concentration over time."
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h5 class="font-semibold text-gray-800 mb-2">Balance Equation:</h5>
                            <div class="bg-white p-4 rounded border">
                                <p class="text-center mb-2">
                                    $\frac{d(\text{Amount of salt})}{dt} = \text{Rate in} - \text{Rate out}$
                                </p>
                                <p class="text-center">
                                    $\frac{dS}{dt} = r \cdot c_{\text{in}} - r \cdot \frac{S}{V}$
                                </p>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-semibold text-gray-800 mb-2">Differential Equation:</h5>
                            <div class="bg-white p-4 rounded border">
                                <p class="text-center">
                                    $\frac{dS}{dt} = 5 - \frac{S}{100}$
                                </p>
                                <p class="text-sm text-gray-600 text-center">
                                    First-order linear ODE
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="code-block python-code">
                    <h4 class="text-white font-semibold mb-3">
                        <i class="fab fa-python mr-2"></i>Python Implementation: Case Studies
                    </h4>
                    <pre class="text-green-300"><code># Case Studies Implementation
import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint
import seaborn as sns

# Set style for better plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Case Study 1: Logistic Growth
def logistic_growth(P, t, r, K):
    """
    Logistic growth differential equation
    dP/dt = rP(1 - P/K)
    """
    return r * P * (1 - P/K)

def logistic_analytical(t, P0, r, K):
    """
    Analytical solution to logistic equation
    """
    return K * P0 / (P0 + (K - P0) * np.exp(-r * t))

# Parameters for rabbit population
r = 0.1  # growth rate (per day)
K = 1000  # carrying capacity
P0 = 50   # initial population
t = np.linspace(0, 100, 1000)

# Numerical solution
P_numerical = odeint(logistic_growth, P0, t, args=(r, K))

# Analytical solution
P_analytical = logistic_analytical(t, P0, r, K)

# Plot logistic growth
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

# Logistic growth curve
ax1.plot(t, P_numerical, 'b-', linewidth=2, label='Numerical')
ax1.plot(t, P_analytical, 'r--', linewidth=2, label='Analytical')
ax1.axhline(y=K, color='g', linestyle=':', alpha=0.7, label='Carrying Capacity')
ax1.axhline(y=K/2, color='orange', linestyle=':', alpha=0.7, label='Inflection Point')
ax1.set_xlabel('Time (days)')
ax1.set_ylabel('Population')
ax1.set_title('Logistic Growth: Rabbit Population')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Growth rate over time
growth_rate = r * P_analytical * (1 - P_analytical/K)
ax2.plot(t, growth_rate, 'purple', linewidth=2)
ax2.set_xlabel('Time (days)')
ax2.set_ylabel('Growth Rate (dP/dt)')
ax2.set_title('Growth Rate vs Time')
ax2.grid(True, alpha=0.3)

# Case Study 2: Newton's Law of Cooling
def cooling(T, t, k, T_ambient):
    """
    Newton's law of cooling
    dT/dt = -k(T - T_ambient)
    """
    return -k * (T - T_ambient)

def cooling_analytical(t, T0, k, T_ambient):
    """
    Analytical solution to cooling equation
    """
    return T_ambient + (T0 - T_ambient) * np.exp(-k * t)

# Parameters for coffee cooling
k = 0.05  # cooling constant (per minute)
T_ambient = 20  # room temperature (°C)
T0 = 90  # initial temperature (°C)
t_cool = np.linspace(0, 120, 1000)

# Solutions
T_numerical = odeint(cooling, T0, t_cool, args=(k, T_ambient))
T_analytical = cooling_analytical(t_cool, T0, k, T_ambient)

# Plot cooling curve
ax3.plot(t_cool, T_numerical, 'b-', linewidth=2, label='Numerical')
ax3.plot(t_cool, T_analytical, 'r--', linewidth=2, label='Analytical')
ax3.axhline(y=T_ambient, color='g', linestyle=':', alpha=0.7, label='Room Temperature')
ax3.set_xlabel('Time (minutes)')
ax3.set_ylabel('Temperature (°C)')
ax3.set_title("Newton's Law of Cooling: Coffee Temperature")
ax3.legend()
ax3.grid(True, alpha=0.3)

# Case Study 3: Mixing Problem
def mixing(S, t, r, c_in, V):
    """
    Mixing problem differential equation
    dS/dt = r*c_in - r*S/V
    """
    return r * c_in - r * S / V

def mixing_analytical(t, S0, r, c_in, V):
    """
    Analytical solution to mixing equation
    """
    return c_in * V * (1 - np.exp(-r * t / V)) + S0 * np.exp(-r * t / V)

# Parameters for salt mixing
r = 10      # flow rate (L/min)
c_in = 0.5  # input concentration (kg/L)
V = 1000    # tank volume (L)
S0 = 0      # initial salt amount (kg)
t_mix = np.linspace(0, 600, 1000)

# Solutions
S_numerical = odeint(mixing, S0, t_mix, args=(r, c_in, V))
S_analytical = mixing_analytical(t_mix, S0, r, c_in, V)

# Convert to concentration
c_numerical = S_numerical / V
c_analytical = S_analytical / V

# Plot mixing problem
ax4.plot(t_mix, c_numerical, 'b-', linewidth=2, label='Numerical')
ax4.plot(t_mix, c_analytical, 'r--', linewidth=2, label='Analytical')
ax4.axhline(y=c_in, color='g', linestyle=':', alpha=0.7, label='Input Concentration')
ax4.set_xlabel('Time (minutes)')
ax4.set_ylabel('Concentration (kg/L)')
ax4.set_title('Mixing Problem: Salt Concentration')
ax4.legend()
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Comparative analysis
print("CASE STUDIES ANALYSIS")
print("====================")
print("\n1. LOGISTIC GROWTH:")
print(f"   - Carrying capacity: {K} rabbits")
print(f"   - Time to reach 50% capacity: {np.log(K/P0 - 1)/r:.1f} days")
print(f"   - Maximum growth rate: {r*K/4:.1f} rabbits/day at P = {K/2}")

print("\n2. COOLING PROBLEM:")
print(f"   - Temperature drop in 30 min: {T0 - cooling_analytical(30, T0, k, T_ambient):.1f}°C")
print(f"   - Time to reach 30°C: {-np.log((30-T_ambient)/(T0-T_ambient))/k:.1f} minutes")

print("\n3. MIXING PROBLEM:")
print(f"   - Final concentration: {c_in:.1f} kg/L")
print(f"   - Time to reach 90% of final: {-V/r * np.log(0.1):.1f} minutes")

# Parameter sensitivity analysis
print("\nPARAMETER SENSITIVITY ANALYSIS")
print("==============================")

# Logistic growth sensitivity to r
r_values = np.array([0.05, 0.1, 0.15, 0.2])
plt.figure(figsize=(12, 4))

for i, r_val in enumerate(r_values):
    P_sens = logistic_analytical(t, P0, r_val, K)
    plt.subplot(1, 3, 1)
    plt.plot(t, P_sens, label=f'r = {r_val}')

plt.xlabel('Time (days)')
plt.ylabel('Population')
plt.title('Sensitivity to Growth Rate r')
plt.legend()
plt.grid(True, alpha=0.3)

# Cooling sensitivity to k
k_values = np.array([0.02, 0.05, 0.08, 0.1])
for i, k_val in enumerate(k_values):
    T_sens = cooling_analytical(t_cool, T0, k_val, T_ambient)
    plt.subplot(1, 3, 2)
    plt.plot(t_cool, T_sens, label=f'k = {k_val}')

plt.xlabel('Time (minutes)')
plt.ylabel('Temperature (°C)')
plt.title('Sensitivity to Cooling Rate k')
plt.legend()
plt.grid(True, alpha=0.3)

# Mixing sensitivity to flow rate
r_values = np.array([5, 10, 15, 20])
for i, r_val in enumerate(r_values):
    c_sens = mixing_analytical(t_mix, S0, r_val, c_in, V) / V
    plt.subplot(1, 3, 3)
    plt.plot(t_mix, c_sens, label=f'r = {r_val} L/min')

plt.xlabel('Time (minutes)')
plt.ylabel('Concentration (kg/L)')
plt.title('Sensitivity to Flow Rate r')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Summary statistics
print("\nMODEL COMPARISON SUMMARY")
print("=======================")
print("All three models represent first-order ODEs with different characteristics:")
print("1. Logistic: Nonlinear, autonomous, sigmoid solution")
print("2. Cooling: Linear, autonomous, exponential decay")
print("3. Mixing: Linear, non-autonomous, exponential approach to equilibrium")</code></pre>
                </div>

                <div class="code-block r-code">
                    <h4 class="text-white font-semibold mb-3">
                        <i class="fab fa-r-project mr-2"></i>R Implementation: Case Studies
                    </h4>
                    <pre class="text-blue-300"><code># Case Studies in R
library(deSolve)
library(ggplot2)
library(dplyr)
library(gridExtra)
library(tidyr)

# Case Study 1: Logistic Growth
logistic_ode <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P * (1 - P/K)
    list(c(dP))
  })
}

# Analytical solution for logistic growth
logistic_analytical <- function(t, P0, r, K) {
  K * P0 / (P0 + (K - P0) * exp(-r * t))
}

# Parameters
r <- 0.1    # growth rate
K <- 1000   # carrying capacity
P0 <- 50    # initial population
times <- seq(0, 100, by = 0.1)

# Solve numerically
parms <- c(r = r, K = K)
initial <- c(P = P0)
logistic_num <- ode(y = initial, times = times, func = logistic_ode, parms = parms)

# Analytical solution
logistic_anal <- data.frame(
  time = times,
  P = logistic_analytical(times, P0, r, K)
)

# Case Study 2: Newton's Law of Cooling
cooling_ode <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dT <- -k * (T - T_ambient)
    list(c(dT))
  })
}

# Analytical solution for cooling
cooling_analytical <- function(t, T0, k, T_ambient) {
  T_ambient + (T0 - T_ambient) * exp(-k * t)
}

# Parameters
k <- 0.05         # cooling constant
T_ambient <- 20   # room temperature
T0 <- 90         # initial temperature
times_cool <- seq(0, 120, by = 0.1)

# Solve numerically
parms_cool <- c(k = k, T_ambient = T_ambient)
initial_cool <- c(T = T0)
cooling_num <- ode(y = initial_cool, times = times_cool, func = cooling_ode, parms = parms_cool)

# Analytical solution
cooling_anal <- data.frame(
  time = times_cool,
  T = cooling_analytical(times_cool, T0, k, T_ambient)
)

# Case Study 3: Mixing Problem
mixing_ode <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dS <- r * c_in - r * S / V
    list(c(dS))
  })
}

# Analytical solution for mixing
mixing_analytical <- function(t, S0, r, c_in, V) {
  c_in * V * (1 - exp(-r * t / V)) + S0 * exp(-r * t / V)
}

# Parameters
r_mix <- 10      # flow rate (L/min)
c_in <- 0.5      # input concentration (kg/L)
V <- 1000        # tank volume (L)
S0 <- 0          # initial salt amount (kg)
times_mix <- seq(0, 600, by = 1)

# Solve numerically
parms_mix <- c(r = r_mix, c_in = c_in, V = V)
initial_mix <- c(S = S0)
mixing_num <- ode(y = initial_mix, times = times_mix, func = mixing_ode, parms = parms_mix)

# Analytical solution
mixing_anal <- data.frame(
  time = times_mix,
  S = mixing_analytical(times_mix, S0, r_mix, c_in, V)
)

# Create plots
# Plot 1: Logistic Growth
p1 <- ggplot() +
  geom_line(data = as.data.frame(logistic_num), aes(x = time, y = P, color = "Numerical"), size = 1.2) +
  geom_line(data = logistic_anal, aes(x = time, y = P, color = "Analytical"), 
            size = 1.2, linetype = "dashed") +
  geom_hline(yintercept = K, color = "green", linetype = "dotted", alpha = 0.7) +
  geom_hline(yintercept = K/2, color = "orange", linetype = "dotted", alpha = 0.7) +
  labs(
    title = "Logistic Growth: Rabbit Population",
    x = "Time (days)",
    y = "Population",
    color = "Solution"
  ) +
  theme_minimal() +
  theme(legend.position = "bottom")

# Plot 2: Cooling
p2 <- ggplot() +
  geom_line(data = as.data.frame(cooling_num), aes(x = time, y = T, color = "Numerical"), size = 1.2) +
  geom_line(data = cooling_anal, aes(x = time, y = T, color = "Analytical"), 
            size = 1.2, linetype = "dashed") +
  geom_hline(yintercept = T_ambient, color = "green", linetype = "dotted", alpha = 0.7) +
  labs(
    title = "Newton's Law of Cooling: Coffee Temperature",
    x = "Time (minutes)",
    y = "Temperature (°C)",
    color = "Solution"
  ) +
  theme_minimal() +
  theme(legend.position = "bottom")

# Plot 3: Mixing (convert to concentration)
mixing_num_df <- as.data.frame(mixing_num)
mixing_num_df$concentration <- mixing_num_df$S / V
mixing_anal$concentration <- mixing_anal$S / V

p3 <- ggplot() +
  geom_line(data = mixing_num_df, aes(x = time, y = concentration, color = "Numerical"), size = 1.2) +
  geom_line(data = mixing_anal, aes(x = time, y = concentration, color = "Analytical"), 
            size = 1.2, linetype = "dashed") +
  geom_hline(yintercept = c_in, color = "green", linetype = "dotted", alpha = 0.7) +
  labs(
    title = "Mixing Problem: Salt Concentration",
    x = "Time (minutes)",
    y = "Concentration (kg/L)",
    color = "Solution"
  ) +
  theme_minimal() +
  theme(legend.position = "bottom")

# Plot 4: Growth rates
growth_rate_data <- data.frame(
  time = times,
  growth_rate = r * logistic_anal$P * (1 - logistic_anal$P / K)
)

p4 <- ggplot(growth_rate_data, aes(x = time, y = growth_rate)) +
  geom_line(color = "purple", size = 1.2) +
  labs(
    title = "Growth Rate vs Time",
    x = "Time (days)",
    y = "Growth Rate (dP/dt)"
  ) +
  theme_minimal()

# Display plots
grid.arrange(p1, p2, p3, p4, nrow = 2, ncol = 2)

# Sensitivity Analysis
cat("SENSITIVITY ANALYSIS\n")
cat("===================\n")

# Sensitivity to growth rate r
r_values <- c(0.05, 0.1, 0.15, 0.2)
sensitivity_data <- data.frame()

for (r_val in r_values) {
  temp_data <- data.frame(
    time = times,
    population = logistic_analytical(times, P0, r_val, K),
    parameter = paste("r =", r_val)
  )
  sensitivity_data <- rbind(sensitivity_data, temp_data)
}

# Plot sensitivity
sens_plot <- ggplot(sensitivity_data, aes(x = time, y = population, color = parameter)) +
  geom_line(size = 1.2) +
  labs(
    title = "Sensitivity to Growth Rate r",
    x = "Time (days)",
    y = "Population",
    color = "Parameter"
  ) +
  theme_minimal()

print(sens_plot)

# Calculate key metrics
cat("\nKEY METRICS\n")
cat("===========\n")

# Logistic growth metrics
time_to_half_capacity <- log(K/P0 - 1) / r
max_growth_rate <- r * K / 4

cat("Logistic Growth:\n")
cat(sprintf("  - Time to 50%% capacity: %.1f days\n", time_to_half_capacity))
cat(sprintf("  - Maximum growth rate: %.1f rabbits/day\n", max_growth_rate))

# Cooling metrics
temp_after_30min <- cooling_analytical(30, T0, k, T_ambient)
time_to_30C <- -log((30 - T_ambient)/(T0 - T_ambient)) / k

cat("\nCooling Problem:\n")
cat(sprintf("  - Temperature after 30 min: %.1f°C\n", temp_after_30min))
cat(sprintf("  - Time to reach 30°C: %.1f minutes\n", time_to_30C))

# Mixing metrics
time_to_90_percent <- -V/r_mix * log(0.1)

cat("\nMixing Problem:\n")
cat(sprintf("  - Final concentration: %.1f kg/L\n", c_in))
cat(sprintf("  - Time to 90%% of final: %.1f minutes\n", time_to_90_percent))

# Model comparison
cat("\nMODEL COMPARISON\n")
cat("================\n")
cat("1. Logistic Growth: Nonlinear, autonomous, sigmoid solution\n")
cat("2. Cooling: Linear, autonomous, exponential decay\n")
cat("3. Mixing: Linear, non-autonomous, exponential approach\n")

# Phase portrait for logistic growth (dP/dt vs P)
phase_data <- data.frame(
  P = seq(0, 1200, by = 10)
)
phase_data$dPdt <- r * phase_data$P * (1 - phase_data$P / K)

phase_plot <- ggplot(phase_data, aes(x = P, y = dPdt)) +
  geom_line(color = "blue", size = 1.2) +
  geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
  geom_vline(xintercept = K, linetype = "dashed", alpha = 0.5, color = "red") +
  labs(
    title = "Phase Portrait: Logistic Growth",
    x = "Population P",
    y = "Growth Rate dP/dt"
  ) +
  theme_minimal()

print(phase_plot)</code></pre>
                </div>
            </div>
        </div>

        <!-- Section 6: Exercises -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-dumbbell text-green-600 mr-3"></i>
                6. Practice Exercises
            </h2>

            <div class="prose max-w-none">
                <p class="text-lg text-gray-700 mb-6">
                    Apply the modeling concepts and techniques learned in this chapter to solve real-world problems.
                </p>

                <div class="exercise-box">
                    <h3 class="text-xl font-semibold text-green-800 mb-3">
                        <i class="fas fa-pencil-alt mr-2"></i>Exercise 1: Drug Concentration Model
                    </h3>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800">
                            A patient receives a drug intravenously. The drug is administered at a constant rate of 
                            5 mg/hour and is eliminated from the body at a rate proportional to the amount present, 
                            with elimination constant k = 0.1 per hour.
                        </p>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded border">
                            <h4 class="font-semibold text-gray-800 mb-2">Tasks:</h4>
                            <ol class="text-gray-700 space-y-1">
                                <li>a) Set up the differential equation for drug concentration</li>
                                <li>b) Identify the equilibrium concentration</li>
                                <li>c) Solve the differential equation</li>
                                <li>d) Determine the time to reach 90% of equilibrium</li>
                                <li>e) Implement and visualize the solution in Python/R</li>
                            </ol>
                        </div>
                        <div class="bg-blue-50 p-3 rounded">
                            <h4 class="font-semibold text-blue-800 mb-2">Hint:</h4>
                            <p class="text-blue-700 text-sm">
                                This is similar to the mixing problem, but with a constant input rate and proportional elimination.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="exercise-box">
                    <h3 class="text-xl font-semibold text-green-800 mb-3">
                        <i class="fas fa-balance-scale mr-2"></i>Exercise 2: Compound Interest vs. Continuous Compounding
                    </h3>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800">
                            Compare discrete compound interest with continuous compounding for a $10,000 investment 
                            at 5% annual interest rate over 20 years.
                        </p>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded border">
                            <h4 class="font-semibold text-gray-800 mb-2">Tasks:</h4>
                            <ol class="text-gray-700 space-y-1">
                                <li>a) Set up discrete model: $A_{n+1} = A_n(1 + r)$</li>
                                <li>b) Set up continuous model: $\frac{dA}{dt} = rA$</li>
                                <li>c) Compare solutions for different compounding frequencies</li>
                                <li>d) Calculate the difference after 20 years</li>
                                <li>e) Perform dimensional analysis</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="exercise-box">
                    <h3 class="text-xl font-semibold text-green-800 mb-3">
                        <i class="fas fa-car mr-2"></i>Exercise 3: Terminal Velocity Problem
                    </h3>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800">
                            A skydiver falls from rest. Air resistance is proportional to the square of velocity: 
                            $F_{\text{drag}} = kv^2$, where k = 0.1 kg/m.
                        </p>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded border">
                            <h4 class="font-semibold text-gray-800 mb-2">Tasks:</h4>
                            <ol class="text-gray-700 space-y-1">
                                <li>a) Use dimensional analysis to find the form of terminal velocity</li>
                                <li>b) Set up the differential equation for velocity</li>
                                <li>c) Find the terminal velocity for a 70 kg skydiver</li>
                                <li>d) Solve the equation numerically</li>
                                <li>e) Plot velocity vs time and discuss the approach to terminal velocity</li>
                            </ol>
                        </div>
                        <div class="bg-orange-50 p-3 rounded">
                            <h4 class="font-semibold text-orange-800 mb-2">Challenge:</h4>
                            <p class="text-orange-700 text-sm">
                                This is a nonlinear ODE. You'll need to solve it numerically and compare with the analytical solution using separation of variables.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="exercise-box">
                    <h3 class="text-xl font-semibold text-green-800 mb-3">
                        <i class="fas fa-virus mr-2"></i>Exercise 4: SIR Epidemic Model Setup
                    </h3>
                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-gray-800">
                            Model the spread of an infectious disease in a population. The population is divided into 
                            three groups: Susceptible (S), Infected (I), and Recovered (R).
                        </p>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded border">
                            <h4 class="font-semibold text-gray-800 mb-2">Tasks:</h4>
                            <ol class="text-gray-700 space-y-1">
                                <li>a) Identify the assumptions for the SIR model</li>
                                <li>b) Set up the system of differential equations</li>
                                <li>c) Verify that the total population remains constant</li>
                                <li>d) Identify the basic reproduction number R₀</li>
                                <li>e) Implement the model numerically for given parameters</li>
                            </ol>
                        </div>
                        <div class="bg-red-50 p-3 rounded">
                            <h4 class="font-semibold text-red-800 mb-2">Note:</h4>
                            <p class="text-red-700 text-sm">
                                This exercise prepares you for systems of ODEs, which will be covered in detail in later chapters.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="warning-box">
                    <h3 class="text-xl font-semibold text-orange-800 mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Solution Guidelines
                    </h3>
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded border">
                            <h4 class="font-semibold text-gray-800 mb-2">Problem-Solving Steps:</h4>
                            <ol class="text-gray-700 space-y-1">
                                <li>1. <strong>Understand the problem:</strong> Read carefully and identify what's being asked</li>
                                <li>2. <strong>Define variables:</strong> Use clear, descriptive variable names with units</li>
                                <li>3. <strong>State assumptions:</strong> List all simplifications and assumptions made</li>
                                <li>4. <strong>Set up equations:</strong> Translate the problem into mathematical form</li>
                                <li>5. <strong>Check dimensions:</strong> Verify dimensional consistency</li>
                                <li>6. <strong>Solve:</strong> Use analytical or numerical methods as appropriate</li>
                                <li>7. <strong>Interpret:</strong> Explain the solution in the context of the original problem</li>
                                <li>8. <strong>Validate:</strong> Check if the solution makes physical sense</li>
                            </ol>
                        </div>
                        <div class="bg-blue-50 p-3 rounded">
                            <h4 class="font-semibold text-blue-800 mb-2">Coding Best Practices:</h4>
                            <ul class="text-blue-700 space-y-1">
                                <li>• Use descriptive variable names that match the problem context</li>
                                <li>• Include units in comments</li>
                                <li>• Plot results to visualize the solution</li>
                                <li>• Perform sensitivity analysis on key parameters</li>
                                <li>• Compare numerical and analytical solutions when possible</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chapter Summary -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-bookmark text-blue-600 mr-3"></i>
                Chapter Summary
            </h2>

            <div class="prose max-w-none">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold text-blue-800 mb-4">Key Concepts Covered</h3>
                        <ul class="space-y-2 text-gray-700">
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Mathematical modeling process and cycle</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Translating word problems to mathematical expressions</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Dimensional analysis and unit consistency</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Different modeling paradigms (linear/nonlinear, discrete/continuous)</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Case studies leading to differential equations</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Model validation and interpretation</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-purple-800 mb-4">Skills Developed</h3>
                        <ul class="space-y-2 text-gray-700">
                            <li><i class="fas fa-cog text-blue-500 mr-2"></i>Problem identification and variable definition</li>
                            <li><i class="fas fa-cog text-blue-500 mr-2"></i>Assumption formulation and simplification</li>
                            <li><i class="fas fa-cog text-blue-500 mr-2"></i>Dimensional analysis techniques</li>
                            <li><i class="fas fa-cog text-blue-500 mr-2"></i>Model implementation in Python and R</li>
                            <li><i class="fas fa-cog text-blue-500 mr-2"></i>Solution visualization and interpretation</li>
                            <li><i class="fas fa-cog text-blue-500 mr-2"></i>Sensitivity analysis and parameter studies</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg mt-8 border-l-4 border-blue-500">
                    <h3 class="text-xl font-semibold text-gray-800 mb-3">
                        <i class="fas fa-arrow-right text-blue-600 mr-2"></i>Bridge to Differential Equations
                    </h3>
                    <p class="text-gray-700 mb-3">
                        This chapter has prepared you for the systematic study of differential equations by:
                    </p>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-3 rounded">
                            <h4 class="font-semibold text-blue-800 text-sm mb-1">Mathematical Foundation</h4>
                            <p class="text-blue-700 text-sm">Providing the mathematical language and tools needed to formulate and solve ODEs</p>
                        </div>
                        <div class="bg-green-50 p-3 rounded">
                            <h4 class="font-semibold text-green-800 text-sm mb-1">Physical Intuition</h4>
                            <p class="text-green-700 text-sm">Developing understanding of how real-world processes lead to differential equations</p>
                        </div>
                        <div class="bg-purple-50 p-3 rounded">
                            <h4 class="font-semibold text-purple-800 text-sm mb-1">Computational Skills</h4>
                            <p class="text-purple-700 text-sm">Building programming capabilities for numerical solution and visualization</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-road text-orange-600 mr-3"></i>
                What's Next?
            </h2>

            <div class="prose max-w-none">
                <p class="text-lg text-gray-700 mb-6">
                    With a solid foundation in mathematical modeling, we're ready to move on to the final preparatory 
                    chapter and then dive into differential equations proper.
                </p>

                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 p-6 rounded-lg border-l-4 border-orange-400">
                        <h3 class="text-xl font-semibold text-orange-800 mb-3">
                            <i class="fas fa-forward mr-2"></i>Chapter 5: Programming Foundations
                        </h3>
                        <p class="text-gray-700 mb-3">
                            Master the computational tools needed for ODE analysis:
                        </p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Advanced Python and R programming</li>
                            <li>• Numerical methods and algorithms</li>
                            <li>• Symbolic computation with SymPy and Ryacas</li>
                            <li>• Visualization and animation techniques</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-blue-50 to-purple-50 p-6 rounded-lg border-l-4 border-blue-400">
                        <h3 class="text-xl font-semibold text-blue-800 mb-3">
                            <i class="fas fa-calculator mr-2"></i>Part 2: First-Order ODEs
                        </h3>
                        <p class="text-gray-700 mb-3">
                            Begin the systematic study of differential equations:
                        </p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Definitions and classifications</li>
                            <li>• Separable equations</li>
                            <li>• Linear first-order equations</li>
                            <li>• Applications and modeling</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-gray-100 p-6 rounded-lg mt-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-3">
                        <i class="fas fa-lightbulb text-yellow-600 mr-2"></i>Study Tips for Success
                    </h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="space-y-2">
                            <h4 class="font-semibold text-gray-700">Practice Regularly</h4>
                            <p class="text-sm text-gray-600">Work through the exercises and implement the code examples yourself</p>
                        </div>
                        <div class="space-y-2">
                            <h4 class="font-semibold text-gray-700">Connect Concepts</h4>
                            <p class="text-sm text-gray-600">Look for connections between different modeling approaches and applications</p>
                        </div>
                        <div class="space-y-2">
                            <h4 class="font-semibold text-gray-700">Experiment</h4>
                            <p class="text-sm text-gray-600">Modify parameters and explore how changes affect model behavior</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-gray-500 text-sm">
            <p>© 2024 ODE Tutorial Series | Chapter 4: Introduction to Mathematical Modeling</p>
            <p>Part 1: Mathematical Foundations</p>
        </div>
    </div>

    <script>
        // Chart 1: Modeling Cycle
        const ctx1 = document.getElementById('modelingCycleChart').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: ['Problem Identification', 'Assumptions', 'Formulation', 'Solution', 'Validation'],
                datasets: [{
                    data: [20, 20, 20, 20, 20],
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Mathematical Modeling Cycle'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Chart 2: Model Comparison
        const ctx2 = document.getElementById('modelComparisonChart').getContext('2d');
        
        // Generate data for discrete vs continuous comparison
        const t = [];
        const discrete = [];
        const continuous = [];
        
        for (let i = 0; i <= 10; i++) {
            t.push(i);
            discrete.push(50 * Math.pow(1.1, i)); // Discrete growth
            continuous.push(50 * Math.exp(0.1 * i)); // Continuous growth
        }
        
        new Chart(ctx2, {
            type: 'line',
            data: {
                labels: t,
                datasets: [{
                    label: 'Discrete Model',
                    data: discrete,
                    borderColor: '#FF6384',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }, {
                    label: 'Continuous Model',
                    data: continuous,
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    borderDash: [5, 5]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Discrete vs Continuous Models: Population Growth'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Population'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
