<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 1: Review of Algebra and Functions - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-r.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <style>
        body { font-family: 'Inter', sans-serif; }
        .code-font { font-family: 'JetBrains Mono', monospace; }
        .math-display { overflow-x: auto; }
        pre[class*="language-"] { margin: 1rem 0; border-radius: 0.5rem; }
        .chart-container { height: 400px; margin: 1rem 0; }
        .section-break { border-top: 2px solid #e5e7eb; margin: 2rem 0; padding-top: 2rem; }
        .example-box { border-left: 4px solid #3b82f6; }
        .definition-box { border-left: 4px solid #10b981; }
        .warning-box { border-left: 4px solid #f59e0b; }
         {
            .no-print { display: none !important; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 leading-relaxed">
    <div class="max-w-6xl mx-auto p-6 bg-white shadow-lg">
        <!-- Header -->
        <header class="text-center mb-8 border-b-2 border-gray-200 pb-6">
            <h1 class="text-4xl font-bold text-blue-800 mb-2">
                <i class="fas fa-calculator mr-3"></i>
                Comprehensive ODE Tutorial
            </h1>
            <h2 class="text-2xl font-semibold text-gray-700 mb-2">Part 1: Mathematical Foundations</h2>
            <h3 class="text-xl font-medium text-gray-600">Chapter 1: Review of Algebra and Functions</h3>
            <div class="flex justify-center items-center mt-4 text-sm text-gray-500">
                <span><i class="fas fa-book mr-2"></i>Educational Content</span>
                <span class="mx-3">•</span>
                <span><i class="fas fa-code mr-2"></i>Python & R Integration</span>
                <span class="mx-3">•</span>
                <span><i class="fas fa-chart-line mr-2"></i>Interactive Visualizations</span>
            </div>
        </header>

        <!-- Table of Contents -->
        <nav class="mb-8 bg-blue-50 p-6 rounded-lg">
            <h3 class="text-lg font-semibold mb-4 text-blue-800">
                <i class="fas fa-list mr-2"></i>Chapter Contents
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <a href="#introduction" class="text-blue-600 hover:text-blue-800 transition">1.1 Introduction</a>
                <a href="#linear-functions" class="text-blue-600 hover:text-blue-800 transition">1.2 Linear Functions</a>
                <a href="#quadratic-functions" class="text-blue-600 hover:text-blue-800 transition">1.3 Quadratic Functions</a>
                <a href="#exponential-functions" class="text-blue-600 hover:text-blue-800 transition">1.4 Exponential Functions</a>
                <a href="#logarithmic-functions" class="text-blue-600 hover:text-blue-800 transition">1.5 Logarithmic Functions</a>
                <a href="#function-behavior" class="text-blue-600 hover:text-blue-800 transition">1.6 Function Behavior Analysis</a>
                <a href="#programming-tools" class="text-blue-600 hover:text-blue-800 transition">1.7 Programming Tools</a>
                <a href="#exercises" class="text-blue-600 hover:text-blue-800 transition">1.8 Exercises</a>
            </div>
        </nav>

        <!-- Section 1.1: Introduction -->
        <section id="introduction" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-play-circle text-blue-600 mr-3"></i>
                1.1 Introduction
            </h2>
            
            <div class="definition-box bg-green-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-green-800 mb-3">
                    <i class="fas fa-lightbulb mr-2"></i>Why Review Algebra and Functions?
                </h3>
                <p class="text-gray-700 leading-relaxed">
                    Before diving into differential equations, we must establish a solid foundation in algebraic functions. 
                    Differential equations describe how quantities change, and understanding the basic function types—linear, 
                    quadratic, exponential, and logarithmic—is crucial because:
                </p>
                <ul class="list-disc list-inside mt-4 text-gray-700 space-y-2">
                    <li>Many differential equations have solutions that are combinations of these function types</li>
                    <li>Modeling real-world phenomena often begins with recognizing these fundamental patterns</li>
                    <li>Graphical interpretation helps us understand solution behavior</li>
                    <li>Programming implementations require solid mathematical understanding</li>
                </ul>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">
                    <i class="fas fa-target mr-2"></i>Learning Objectives
                </h3>
                <p class="text-gray-700 mb-4">By the end of this chapter, you will be able to:</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <ul class="list-check space-y-2 text-gray-700">
                        <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Identify and work with linear, quadratic, exponential, and logarithmic functions</li>
                        <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Graph and interpret function behavior</li>
                        <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Analyze domain, range, and key characteristics</li>
                    </ul>
                    <ul class="list-check space-y-2 text-gray-700">
                        <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Implement functions in Python and R</li>
                        <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Create visualizations for function analysis</li>
                        <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Apply these concepts to mathematical modeling</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Section 1.2: Linear Functions -->
        <section id="linear-functions" class="section-break">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                1.2 Linear Functions
            </h2>

            <div class="definition-box bg-green-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-green-800 mb-3">Definition</h3>
                <p class="text-gray-700 mb-4">
                    A <strong>linear function</strong> is a function of the form:
                </p>
                <div class="math-display text-center text-lg">
                    $$f(x) = mx + b$$
                </div>
                <p class="text-gray-700 mt-4">
                    where $m$ is the <strong>slope</strong> and $b$ is the <strong>y-intercept</strong>.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div class="bg-white border rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Key Properties</h3>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>Slope ($m$):</strong> Rate of change, $m = \frac{\Delta y}{\Delta x}$</li>
                        <li><strong>Y-intercept ($b$):</strong> Value when $x = 0$</li>
                        <li><strong>Domain:</strong> All real numbers $(-\infty, \infty)$</li>
                        <li><strong>Range:</strong> All real numbers $(-\infty, \infty)$</li>
                        <li><strong>Graph:</strong> Straight line</li>
                    </ul>
                </div>
                
                <div class="bg-white border rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Special Cases</h3>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>$m > 0$:</strong> Increasing function</li>
                        <li><strong>$m < 0$:</strong> Decreasing function</li>
                        <li><strong>$m = 0$:</strong> Constant function $f(x) = b$</li>
                        <li><strong>$b = 0$:</strong> Function passes through origin</li>
                    </ul>
                </div>
            </div>

            <div class="example-box bg-blue-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-calculator mr-2"></i>Example 1.1: Linear Function Analysis
                </h3>
                <p class="text-gray-700 mb-3">Consider the function $f(x) = 2x - 3$</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <ul class="space-y-2 text-gray-700">
                        <li><strong>Slope:</strong> $m = 2$ (increasing)</li>
                        <li><strong>Y-intercept:</strong> $b = -3$</li>
                        <li><strong>X-intercept:</strong> Solve $2x - 3 = 0 \Rightarrow x = \frac{3}{2}$</li>
                    </ul>
                    <ul class="space-y-2 text-gray-700">
                        <li><strong>At $x = 0$:</strong> $f(0) = -3$</li>
                        <li><strong>At $x = 2$:</strong> $f(2) = 1$</li>
                        <li><strong>Rate of change:</strong> 2 units up per 1 unit right</li>
                    </ul>
                </div>
            </div>

            <!-- Linear Function Visualization -->
            <div class="bg-white border rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-area mr-2"></i>Linear Functions Visualization
                </h3>
                <div class="chart-container">
                    <canvas id="linearChart" style="height: 400px;"></canvas>
                </div>
            </div>

            <!-- Python Code for Linear Functions -->
            <div class="bg-gray-900 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">
                    <i class="fab fa-python mr-2"></i>Python Implementation
                </h3>
                <pre class="language-python"><code>import numpy as np
import matplotlib.pyplot as plt

# Define linear function
def linear_function(x, m, b):
    """
    Linear function: f(x) = mx + b
    
    Parameters:
    x: input values
    m: slope
    b: y-intercept
    """
    return m * x + b

# Create x values
x = np.linspace(-5, 5, 100)

# Define different linear functions
functions = [
    {"m": 2, "b": -3, "label": "f(x) = 2x - 3", "color": "blue"},
    {"m": -1, "b": 2, "label": "g(x) = -x + 2", "color": "red"},
    {"m": 0.5, "b": 1, "label": "h(x) = 0.5x + 1", "color": "green"},
    {"m": 0, "b": -1, "label": "k(x) = -1", "color": "orange"}
]

# Create the plot
plt.figure(figsize=(10, 8))
plt.grid(True, alpha=0.3)

for func in functions:
    y = linear_function(x, func["m"], func["b"])
    plt.plot(x, y, label=func["label"], color=func["color"], linewidth=2)

plt.xlabel("x", fontsize=12)
plt.ylabel("f(x)", fontsize=12)
plt.title("Linear Functions Comparison", fontsize=14, fontweight='bold')
plt.legend(fontsize=10)
plt.axhline(y=0, color='black', linewidth=0.5)
plt.axvline(x=0, color='black', linewidth=0.5)
plt.xlim(-5, 5)
plt.ylim(-8, 8)
plt.show()

# Calculate key properties
def analyze_linear_function(m, b):
    """Analyze properties of a linear function"""
    print(f"Function: f(x) = {m}x + {b}")
    print(f"Slope: {m}")
    print(f"Y-intercept: {b}")
    
    if m != 0:
        x_intercept = -b / m
        print(f"X-intercept: {x_intercept:.3f}")
    else:
        print("No x-intercept (horizontal line)")
    
    if m > 0:
        print("Function is increasing")
    elif m < 0:
        print("Function is decreasing")
    else:
        print("Function is constant")
    print("-" * 30)

# Analyze our example functions
for func in functions:
    analyze_linear_function(func["m"], func["b"])</code></pre>
            </div>

            <!-- R Code for Linear Functions -->
            <div class="bg-blue-900 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">
                    <i class="fab fa-r-project mr-2"></i>R Implementation
                </h3>
                <pre class="language-r"><code># Linear Functions in R
library(ggplot2)
library(dplyr)

# Define linear function
linear_function <- function(x, m, b) {
  return(m * x + b)
}

# Create x values
x <- seq(-5, 5, length.out = 100)

# Define different linear functions
functions_data <- data.frame(
  x = rep(x, 4),
  y = c(
    linear_function(x, 2, -3),    # f(x) = 2x - 3
    linear_function(x, -1, 2),    # g(x) = -x + 2
    linear_function(x, 0.5, 1),   # h(x) = 0.5x + 1
    linear_function(x, 0, -1)     # k(x) = -1
  ),
  Function = rep(c("f(x) = 2x - 3", "g(x) = -x + 2", 
                   "h(x) = 0.5x + 1", "k(x) = -1"), each = 100)
)

# Create the plot
ggplot(functions_data, aes(x = x, y = y, color = Function)) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = 0, color = "black", size = 0.5, alpha = 0.7) +
  geom_vline(xintercept = 0, color = "black", size = 0.5, alpha = 0.7) +
  labs(
    title = "Linear Functions Comparison",
    x = "x",
    y = "f(x)",
    color = "Function"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    legend.position = "bottom"
  ) +
  scale_color_manual(values = c("blue", "red", "green", "orange")) +
  xlim(-5, 5) +
  ylim(-8, 8)

# Function to analyze linear function properties
analyze_linear <- function(m, b) {
  cat("Function: f(x) =", m, "x +", b, "\n")
  cat("Slope:", m, "\n")
  cat("Y-intercept:", b, "\n")
  
  if (m != 0) {
    x_intercept <- -b / m
    cat("X-intercept:", round(x_intercept, 3), "\n")
  } else {
    cat("No x-intercept (horizontal line)\n")
  }
  
  if (m > 0) {
    cat("Function is increasing\n")
  } else if (m < 0) {
    cat("Function is decreasing\n")
  } else {
    cat("Function is constant\n")
  }
  cat(rep("-", 30), "\n")
}

# Analyze example functions
analyze_linear(2, -3)
analyze_linear(-1, 2)
analyze_linear(0.5, 1)
analyze_linear(0, -1)</code></pre>
            </div>
        </section>

        <!-- Section 1.3: Quadratic Functions -->
        <section id="quadratic-functions" class="section-break">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-project-diagram text-blue-600 mr-3"></i>
                1.3 Quadratic Functions
            </h2>

            <div class="definition-box bg-green-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-green-800 mb-3">Definition</h3>
                <p class="text-gray-700 mb-4">
                    A <strong>quadratic function</strong> is a function of the form:
                </p>
                <div class="math-display text-center text-lg">
                    $$f(x) = ax^2 + bx + c$$
                </div>
                <p class="text-gray-700 mt-4">
                    where $a \neq 0$, and $a$, $b$, $c$ are constants.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div class="bg-white border rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Key Properties</h3>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>Graph:</strong> Parabola</li>
                        <li><strong>Vertex:</strong> $\left(-\frac{b}{2a}, f\left(-\frac{b}{2a}\right)\right)$</li>
                        <li><strong>Axis of symmetry:</strong> $x = -\frac{b}{2a}$</li>
                        <li><strong>Y-intercept:</strong> $(0, c)$</li>
                        <li><strong>Domain:</strong> All real numbers</li>
                    </ul>
                </div>
                
                <div class="bg-white border rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Shape and Direction</h3>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>$a > 0$:</strong> Opens upward (minimum at vertex)</li>
                        <li><strong>$a < 0$:</strong> Opens downward (maximum at vertex)</li>
                        <li><strong>$|a|$ larger:</strong> Narrower parabola</li>
                        <li><strong>$|a|$ smaller:</strong> Wider parabola</li>
                    </ul>
                </div>
            </div>

            <div class="bg-yellow-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-yellow-800 mb-4">
                    <i class="fas fa-info-circle mr-2"></i>Finding Roots (X-intercepts)
                </h3>
                <p class="text-gray-700 mb-4">
                    The roots of a quadratic equation $ax^2 + bx + c = 0$ are found using the quadratic formula:
                </p>
                <div class="math-display text-center text-lg">
                    $$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$$
                </div>
                <p class="text-gray-700 mt-4">
                    The discriminant $\Delta = b^2 - 4ac$ determines the nature of roots:
                </p>
                <ul class="list-disc list-inside mt-3 text-gray-700 space-y-1">
                    <li>$\Delta > 0$: Two distinct real roots</li>
                    <li>$\Delta = 0$: One repeated real root</li>
                    <li>$\Delta < 0$: Two complex conjugate roots</li>
                </ul>
            </div>

            <div class="example-box bg-blue-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-calculator mr-2"></i>Example 1.2: Quadratic Function Analysis
                </h3>
                <p class="text-gray-700 mb-3">Consider the function $f(x) = -2x^2 + 4x + 1$</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="font-semibold text-gray-800 mb-2">Coefficients:</p>
                        <ul class="space-y-1 text-gray-700">
                            <li>$a = -2$ (opens downward)</li>
                            <li>$b = 4$</li>
                            <li>$c = 1$</li>
                        </ul>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-800 mb-2">Key Features:</p>
                        <ul class="space-y-1 text-gray-700">
                            <li>Vertex: $x = -\frac{4}{2(-2)} = 1$</li>
                            <li>$f(1) = -2(1)^2 + 4(1) + 1 = 3$</li>
                            <li>Vertex: $(1, 3)$ (maximum)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Quadratic Function Visualization -->
            <div class="bg-white border rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-area mr-2"></i>Quadratic Functions Visualization
                </h3>
                <div class="chart-container">
                    <canvas id="quadraticChart" style="height: 400px;"></canvas>
                </div>
            </div>

            <!-- Python Code for Quadratic Functions -->
            <div class="bg-gray-900 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">
                    <i class="fab fa-python mr-2"></i>Python Implementation
                </h3>
                <pre class="language-python"><code>import numpy as np
import matplotlib.pyplot as plt
import cmath

# Define quadratic function
def quadratic_function(x, a, b, c):
    """
    Quadratic function: f(x) = ax² + bx + c
    
    Parameters:
    x: input values
    a, b, c: coefficients
    """
    return a * x**2 + b * x + c

# Function to find vertex
def find_vertex(a, b, c):
    """Find vertex of parabola"""
    x_vertex = -b / (2 * a)
    y_vertex = quadratic_function(x_vertex, a, b, c)
    return x_vertex, y_vertex

# Function to find roots using quadratic formula
def find_roots(a, b, c):
    """Find roots of quadratic equation"""
    discriminant = b**2 - 4*a*c
    
    if discriminant >= 0:
        root1 = (-b + np.sqrt(discriminant)) / (2*a)
        root2 = (-b - np.sqrt(discriminant)) / (2*a)
        return root1, root2, "real"
    else:
        root1 = (-b + cmath.sqrt(discriminant)) / (2*a)
        root2 = (-b - cmath.sqrt(discriminant)) / (2*a)
        return root1, root2, "complex"

# Create x values
x = np.linspace(-5, 5, 100)

# Define different quadratic functions
functions = [
    {"a": 1, "b": 0, "c": 0, "label": "f(x) = x²", "color": "blue"},
    {"a": -2, "b": 4, "c": 1, "label": "g(x) = -2x² + 4x + 1", "color": "red"},
    {"a": 0.5, "b": -2, "c": 3, "label": "h(x) = 0.5x² - 2x + 3", "color": "green"},
    {"a": -1, "b": 0, "c": 4, "label": "k(x) = -x² + 4", "color": "orange"}
]

# Create the plot
plt.figure(figsize=(12, 10))
plt.grid(True, alpha=0.3)

for func in functions:
    y = quadratic_function(x, func["a"], func["b"], func["c"])
    plt.plot(x, y, label=func["label"], color=func["color"], linewidth=2)
    
    # Mark vertex
    x_v, y_v = find_vertex(func["a"], func["b"], func["c"])
    plt.plot(x_v, y_v, 'o', color=func["color"], markersize=8, 
             markerfacecolor='white', markeredgewidth=2)

plt.xlabel("x", fontsize=12)
plt.ylabel("f(x)", fontsize=12)
plt.title("Quadratic Functions Comparison", fontsize=14, fontweight='bold')
plt.legend(fontsize=10)
plt.axhline(y=0, color='black', linewidth=0.5)
plt.axvline(x=0, color='black', linewidth=0.5)
plt.xlim(-5, 5)
plt.ylim(-5, 10)
plt.show()

# Analyze quadratic functions
def analyze_quadratic(a, b, c):
    """Analyze properties of a quadratic function"""
    print(f"Function: f(x) = {a}x² + {b}x + {c}")
    
    # Vertex
    x_v, y_v = find_vertex(a, b, c)
    print(f"Vertex: ({x_v:.3f}, {y_v:.3f})")
    
    # Direction
    if a > 0:
        print("Opens upward (has minimum)")
    else:
        print("Opens downward (has maximum)")
    
    # Axis of symmetry
    print(f"Axis of symmetry: x = {x_v:.3f}")
    
    # Y-intercept
    print(f"Y-intercept: (0, {c})")
    
    # Roots
    root1, root2, root_type = find_roots(a, b, c)
    if root_type == "real":
        print(f"Roots: x₁ = {root1:.3f}, x₂ = {root2:.3f}")
    else:
        print(f"Complex roots: x₁ = {root1}, x₂ = {root2}")
    
    print("-" * 40)

# Analyze our example functions
for func in functions:
    analyze_quadratic(func["a"], func["b"], func["c"])</code></pre>
            </div>

            <!-- R Code for Quadratic Functions -->
            <div class="bg-blue-900 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">
                    <i class="fab fa-r-project mr-2"></i>R Implementation
                </h3>
                <pre class="language-r"><code># Quadratic Functions in R
library(ggplot2)
library(dplyr)

# Define quadratic function
quadratic_function <- function(x, a, b, c) {
  return(a * x^2 + b * x + c)
}

# Function to find vertex
find_vertex <- function(a, b, c) {
  x_vertex <- -b / (2 * a)
  y_vertex <- quadratic_function(x_vertex, a, b, c)
  return(c(x_vertex, y_vertex))
}

# Function to find roots
find_roots <- function(a, b, c) {
  discriminant <- b^2 - 4*a*c
  
  if (discriminant >= 0) {
    root1 <- (-b + sqrt(discriminant)) / (2*a)
    root2 <- (-b - sqrt(discriminant)) / (2*a)
    return(list(roots = c(root1, root2), type = "real"))
  } else {
    root1 <- complex(real = -b/(2*a), imaginary = sqrt(-discriminant)/(2*a))
    root2 <- complex(real = -b/(2*a), imaginary = -sqrt(-discriminant)/(2*a))
    return(list(roots = c(root1, root2), type = "complex"))
  }
}

# Create x values
x <- seq(-5, 5, length.out = 100)

# Define different quadratic functions
functions_list <- list(
  list(a = 1, b = 0, c = 0, label = "f(x) = x²", color = "blue"),
  list(a = -2, b = 4, c = 1, label = "g(x) = -2x² + 4x + 1", color = "red"),
  list(a = 0.5, b = -2, c = 3, label = "h(x) = 0.5x² - 2x + 3", color = "green"),
  list(a = -1, b = 0, c = 4, label = "k(x) = -x² + 4", color = "orange")
)

# Create data for plotting
plot_data <- data.frame()
vertex_data <- data.frame()

for (i in 1:length(functions_list)) {
  func <- functions_list[[i]]
  y <- quadratic_function(x, func$a, func$b, func$c)
  
  temp_data <- data.frame(
    x = x,
    y = y,
    Function = func$label,
    Color = func$color
  )
  plot_data <- rbind(plot_data, temp_data)
  
  # Add vertex data
  vertex <- find_vertex(func$a, func$b, func$c)
  vertex_temp <- data.frame(
    x = vertex[1],
    y = vertex[2],
    Function = func$label,
    Color = func$color
  )
  vertex_data <- rbind(vertex_data, vertex_temp)
}

# Create the plot
ggplot(plot_data, aes(x = x, y = y, color = Function)) +
  geom_line(size = 1.2) +
  geom_point(data = vertex_data, aes(x = x, y = y, color = Function), 
             size = 3, shape = 21, fill = "white", stroke = 2) +
  geom_hline(yintercept = 0, color = "black", size = 0.5, alpha = 0.7) +
  geom_vline(xintercept = 0, color = "black", size = 0.5, alpha = 0.7) +
  labs(
    title = "Quadratic Functions Comparison",
    subtitle = "Vertices marked with circles",
    x = "x",
    y = "f(x)",
    color = "Function"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    legend.position = "bottom"
  ) +
  scale_color_manual(values = c("blue", "red", "green", "orange")) +
  xlim(-5, 5) +
  ylim(-5, 10)

# Function to analyze quadratic properties
analyze_quadratic <- function(a, b, c) {
  cat("Function: f(x) =", a, "x² +", b, "x +", c, "\n")
  
  # Vertex
  vertex <- find_vertex(a, b, c)
  cat("Vertex: (", round(vertex[1], 3), ",", round(vertex[2], 3), ")\n")
  
  # Direction
  if (a > 0) {
    cat("Opens upward (has minimum)\n")
  } else {
    cat("Opens downward (has maximum)\n")
  }
  
  # Axis of symmetry
  cat("Axis of symmetry: x =", round(vertex[1], 3), "\n")
  
  # Y-intercept
  cat("Y-intercept: (0,", c, ")\n")
  
  # Roots
  roots_info <- find_roots(a, b, c)
  if (roots_info$type == "real") {
    cat("Roots: x₁ =", round(Re(roots_info$roots[1]), 3), 
        ", x₂ =", round(Re(roots_info$roots[2]), 3), "\n")
  } else {
    cat("Complex roots: x₁ =", roots_info$roots[1], 
        ", x₂ =", roots_info$roots[2], "\n")
  }
  
  cat(rep("-", 40), "\n")
}

# Analyze example functions
for (func in functions_list) {
  analyze_quadratic(func$a, func$b, func$c)
}</code></pre>
            </div>
        </section>

        <!-- Section 1.4: Exponential Functions -->
        <section id="exponential-functions" class="section-break">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                1.4 Exponential Functions
            </h2>

            <div class="definition-box bg-green-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-green-800 mb-3">Definition</h3>
                <p class="text-gray-700 mb-4">
                    An <strong>exponential function</strong> is a function of the form:
                </p>
                <div class="math-display text-center text-lg">
                    $$f(x) = ab^x$$
                </div>
                <p class="text-gray-700 mt-4">
                    where $a > 0$, $b > 0$, $b \neq 1$. The special case where $b = e$ (Euler's number ≈ 2.718) is called the <strong>natural exponential function</strong>: $f(x) = ae^x$.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div class="bg-white border rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Key Properties</h3>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>Domain:</strong> All real numbers $(-\infty, \infty)$</li>
                        <li><strong>Range:</strong> $(0, \infty)$ if $a > 0$</li>
                        <li><strong>Y-intercept:</strong> $(0, a)$</li>
                        <li><strong>Horizontal asymptote:</strong> $y = 0$</li>
                        <li><strong>Always positive</strong> (if $a > 0$)</li>
                    </ul>
                </div>
                
                <div class="bg-white border rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Behavior</h3>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>$b > 1$:</strong> Exponential growth</li>
                        <li><strong>$0 < b < 1$:</strong> Exponential decay</li>
                        <li><strong>Larger $|a|$:</strong> Steeper curve</li>
                        <li><strong>$a < 0$:</strong> Reflection across x-axis</li>
                    </ul>
                </div>
            </div>

            <div class="bg-yellow-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-yellow-800 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>Important Exponential Properties
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <p class="font-semibold text-gray-800 mb-2">Exponential Laws:</p>
                        <ul class="space-y-2 text-gray-700">
                            <li>$b^x \cdot b^y = b^{x+y}$</li>
                            <li>$\frac{b^x}{b^y} = b^{x-y}$</li>
                            <li>$(b^x)^y = b^{xy}$</li>
                            <li>$b^0 = 1$</li>
                        </ul>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-800 mb-2">Special Values:</p>
                        <ul class="space-y-2 text-gray-700">
                            <li>$e \approx 2.71828...$</li>
                            <li>$e^{\ln x} = x$</li>
                            <li>$\ln(e^x) = x$</li>
                            <li>$\frac{d}{dx}e^x = e^x$</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="example-box bg-blue-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-calculator mr-2"></i>Example 1.3: Exponential Growth and Decay
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <p class="font-semibold text-gray-800 mb-2">Growth: $f(x) = 2e^{0.5x}$</p>
                        <ul class="space-y-1 text-gray-700">
                            <li>$a = 2$, base $= e$, rate $= 0.5$</li>
                            <li>Y-intercept: $(0, 2)$</li>
                            <li>Doubling time: $\ln(2)/0.5 \approx 1.39$</li>
                        </ul>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-800 mb-2">Decay: $g(x) = 3e^{-0.2x}$</p>
                        <ul class="space-y-1 text-gray-700">
                            <li>$a = 3$, base $= e$, rate $= -0.2$</li>
                            <li>Y-intercept: $(0, 3)$</li>
                            <li>Half-life: $\ln(2)/0.2 \approx 3.47$</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Exponential Function Visualization -->
            <div class="bg-white border rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-area mr-2"></i>Exponential Functions Visualization
                </h3>
                <div class="chart-container">
                    <canvas id="exponentialChart" style="height: 400px;"></canvas>
                </div>
            </div>

            <!-- Python Code for Exponential Functions -->
            <div class="bg-gray-900 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">
                    <i class="fab fa-python mr-2"></i>Python Implementation
                </h3>
                <pre class="language-python"><code>import numpy as np
import matplotlib.pyplot as plt

# Define exponential function
def exponential_function(x, a, b):
    """
    Exponential function: f(x) = a * b^x
    
    Parameters:
    x: input values
    a: coefficient
    b: base
    """
    return a * (b ** x)

# Natural exponential function
def natural_exponential(x, a, k):
    """
    Natural exponential: f(x) = a * e^(kx)
    
    Parameters:
    x: input values
    a: coefficient
    k: growth/decay rate
    """
    return a * np.exp(k * x)

# Calculate doubling time and half-life
def doubling_time(k):
    """Calculate doubling time for exponential growth"""
    return np.log(2) / k

def half_life(k):
    """Calculate half-life for exponential decay"""
    return np.log(2) / abs(k)

# Create x values
x = np.linspace(-3, 3, 100)

# Define different exponential functions
functions = [
    {"type": "exp", "a": 1, "b": 2, "label": "f(x) = 2^x", "color": "blue"},
    {"type": "exp", "a": 1, "b": 0.5, "label": "g(x) = (0.5)^x", "color": "red"},
    {"type": "nat", "a": 1, "k": 1, "label": "h(x) = e^x", "color": "green"},
    {"type": "nat", "a": 2, "k": -0.5, "label": "k(x) = 2e^(-0.5x)", "color": "orange"}
]

# Create the plot
plt.figure(figsize=(12, 10))
plt.grid(True, alpha=0.3)

for func in functions:
    if func["type"] == "exp":
        y = exponential_function(x, func["a"], func["b"])
    else:  # natural exponential
        y = natural_exponential(x, func["a"], func["k"])
    
    plt.plot(x, y, label=func["label"], color=func["color"], linewidth=2)

plt.xlabel("x", fontsize=12)
plt.ylabel("f(x)", fontsize=12)
plt.title("Exponential Functions Comparison", fontsize=14, fontweight='bold')
plt.legend(fontsize=10)
plt.axhline(y=0, color='black', linewidth=0.5)
plt.axvline(x=0, color='black', linewidth=0.5)
plt.axhline(y=1, color='gray', linewidth=0.5, linestyle='--', alpha=0.7)
plt.xlim(-3, 3)
plt.ylim(0, 8)
plt.show()

# Analyze exponential functions
def analyze_exponential(a, b=None, k=None, func_type="exp"):
    """Analyze properties of exponential functions"""
    if func_type == "exp":
        print(f"Function: f(x) = {a} × {b}^x")
        print(f"Base: {b}")
        if b > 1:
            print("Type: Exponential growth")
        elif 0 < b < 1:
            print("Type: Exponential decay")
    else:  # natural exponential
        print(f"Function: f(x) = {a} × e^({k}x)")
        print(f"Growth/decay rate: {k}")
        if k > 0:
            print("Type: Exponential growth")
            print(f"Doubling time: {doubling_time(k):.3f}")
        elif k < 0:
            print("Type: Exponential decay")
            print(f"Half-life: {half_life(k):.3f}")
    
    print(f"Y-intercept: (0, {a})")
    print(f"Horizontal asymptote: y = 0")
    print("-" * 40)

# Applications: Population growth model
def population_model():
    """Example: Population growth model"""
    print("APPLICATION: Population Growth Model")
    print("P(t) = P₀ × e^(rt)")
    print("Where:")
    print("  P(t) = population at time t")
    print("  P₀ = initial population")
    print("  r = growth rate")
    print("  t = time")
    
    # Example parameters
    P0 = 1000  # initial population
    r = 0.05   # 5% growth rate
    t = np.linspace(0, 20, 100)
    
    P = P0 * np.exp(r * t)
    
    plt.figure(figsize=(10, 6))
    plt.plot(t, P, 'b-', linewidth=2, label=f'P(t) = {P0}e^({r}t)')
    plt.xlabel('Time (years)')
    plt.ylabel('Population')
    plt.title('Population Growth Model')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.show()
    
    print(f"After 10 years: {P0 * np.exp(r * 10):.0f}")
    print(f"Doubling time: {doubling_time(r):.1f} years")
    print("-" * 50)

# Analyze our example functions
for func in functions:
    if func["type"] == "exp":
        analyze_exponential(func["a"], b=func["b"], func_type="exp")
    else:
        analyze_exponential(func["a"], k=func["k"], func_type="nat")

# Show application
population_model()</code></pre>
            </div>
        </section>

        <!-- Section 1.5: Logarithmic Functions -->
        <section id="logarithmic-functions" class="section-break">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-chart-bar text-blue-600 mr-3"></i>
                1.5 Logarithmic Functions
            </h2>

            <div class="definition-box bg-green-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-green-800 mb-3">Definition</h3>
                <p class="text-gray-700 mb-4">
                    A <strong>logarithmic function</strong> is the inverse of an exponential function:
                </p>
                <div class="math-display text-center text-lg">
                    $$f(x) = \log_b(x) \text{ where } b^{f(x)} = x$$
                </div>
                <p class="text-gray-700 mt-4">
                    Common logarithms: $\log_{10}(x) = \log(x)$ and natural logarithms: $\log_e(x) = \ln(x)$
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div class="bg-white border rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Key Properties</h3>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>Domain:</strong> $(0, \infty)$</li>
                        <li><strong>Range:</strong> All real numbers $(-\infty, \infty)$</li>
                        <li><strong>X-intercept:</strong> $(1, 0)$</li>
                        <li><strong>Vertical asymptote:</strong> $x = 0$</li>
                        <li><strong>Increasing function</strong> (if $b > 1$)</li>
                    </ul>
                </div>
                
                <div class="bg-white border rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Logarithm Laws</h3>
                    <ul class="space-y-3 text-gray-700">
                        <li>$\log_b(xy) = \log_b(x) + \log_b(y)$</li>
                        <li>$\log_b\left(\frac{x}{y}\right) = \log_b(x) - \log_b(y)$</li>
                        <li>$\log_b(x^r) = r\log_b(x)$</li>
                        <li>$\log_b(1) = 0$, $\log_b(b) = 1$</li>
                    </ul>
                </div>
            </div>

            <!-- Programming Exercises Section -->
            <div class="bg-blue-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-laptop-code mr-2"></i>Programming Integration
                </h3>
                <p class="text-gray-700 mb-4">
                    Both Python and R provide comprehensive support for mathematical functions. Here's how to work with the functions we've covered:
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <p class="font-semibold text-gray-800 mb-2">Python Libraries:</p>
                        <ul class="list-disc list-inside text-gray-700 space-y-1">
                            <li><code class="bg-gray-200 px-1 rounded">numpy</code> - Mathematical functions</li>
                            <li><code class="bg-gray-200 px-1 rounded">matplotlib</code> - Plotting</li>
                            <li><code class="bg-gray-200 px-1 rounded">scipy</code> - Scientific computing</li>
                            <li><code class="bg-gray-200 px-1 rounded">sympy</code> - Symbolic mathematics</li>
                        </ul>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-800 mb-2">R Packages:</p>
                        <ul class="list-disc list-inside text-gray-700 space-y-1">
                            <li><code class="bg-gray-200 px-1 rounded">base</code> - Built-in functions</li>
                            <li><code class="bg-gray-200 px-1 rounded">ggplot2</code> - Advanced plotting</li>
                            <li><code class="bg-gray-200 px-1 rounded">dplyr</code> - Data manipulation</li>
                            <li><code class="bg-gray-200 px-1 rounded">pracma</code> - Practical mathematics</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Comprehensive Comparison Visualization -->
            <div class="bg-white border rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-area mr-2"></i>All Function Types Comparison
                </h3>
                <div class="chart-container">
                    <canvas id="allFunctionsChart" style="height: 400px;"></canvas>
                </div>
            </div>

            <!-- Final Comprehensive Code Example -->
            <div class="bg-gray-900 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">
                    <i class="fab fa-python mr-2"></i>Complete Function Analysis Tool (Python)
                </h3>
                <pre class="language-python"><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import fsolve
import warnings
warnings.filterwarnings('ignore')

class FunctionAnalyzer:
    """Comprehensive function analysis tool"""
    
    def __init__(self):
        self.x_range = np.linspace(-5, 5, 1000)
    
    def linear(self, x, m, b):
        """Linear function: f(x) = mx + b"""
        return m * x + b
    
    def quadratic(self, x, a, b, c):
        """Quadratic function: f(x) = ax² + bx + c"""
        return a * x**2 + b * x + c
    
    def exponential(self, x, a, k):
        """Exponential function: f(x) = ae^(kx)"""
        return a * np.exp(k * x)
    
    def logarithmic(self, x, a, b):
        """Logarithmic function: f(x) = a*ln(x) + b"""
        return a * np.log(np.abs(x) + 1e-10) + b  # Avoid log(0)
    
    def analyze_function(self, func_type, params, x_range=None):
        """Analyze a function and return key properties"""
        if x_range is None:
            x_range = self.x_range
        
        analysis = {"type": func_type, "parameters": params}
        
        if func_type == "linear":
            m, b = params
            y = self.linear(x_range, m, b)
            analysis.update({
                "slope": m,
                "y_intercept": b,
                "x_intercept": -b/m if m != 0 else None,
                "domain": "(-∞, ∞)",
                "range": "(-∞, ∞)" if m != 0 else f"{{{b}}}",
                "increasing": m > 0,
                "function": lambda x: self.linear(x, m, b)
            })
        
        elif func_type == "quadratic":
            a, b, c = params
            y = self.quadratic(x_range, a, b, c)
            vertex_x = -b / (2*a)
            vertex_y = self.quadratic(vertex_x, a, b, c)
            discriminant = b**2 - 4*a*c
            
            analysis.update({
                "vertex": (vertex_x, vertex_y),
                "axis_of_symmetry": vertex_x,
                "y_intercept": c,
                "discriminant": discriminant,
                "opens_upward": a > 0,
                "domain": "(-∞, ∞)",
                "range": f"[{vertex_y}, ∞)" if a > 0 else f"(-∞, {vertex_y}]",
                "function": lambda x: self.quadratic(x, a, b, c)
            })
        
        elif func_type == "exponential":
            a, k = params
            x_pos = x_range[x_range >= -10]  # Avoid overflow
            y = self.exponential(x_pos, a, k)
            
            analysis.update({
                "coefficient": a,
                "growth_rate": k,
                "y_intercept": a,
                "horizontal_asymptote": 0,
                "domain": "(-∞, ∞)",
                "range": "(0, ∞)" if a > 0 else "(-∞, 0)",
                "exponential_growth": k > 0,
                "doubling_time": np.log(2)/k if k > 0 else None,
                "half_life": np.log(2)/abs(k) if k < 0 else None,
                "function": lambda x: self.exponential(x, a, k)
            })
        
        elif func_type == "logarithmic":
            a, b = params
            x_pos = x_range[x_range > 0]
            y = self.logarithmic(x_pos, a, b)
            
            analysis.update({
                "coefficient": a,
                "vertical_shift": b,
                "x_intercept": np.exp(-b/a) if a != 0 else None,
                "vertical_asymptote": 0,
                "domain": "(0, ∞)",
                "range": "(-∞, ∞)",
                "increasing": a > 0,
                "function": lambda x: self.logarithmic(x, a, b)
            })
        
        return analysis
    
    def plot_comparison(self, functions_list):
        """Plot multiple functions for comparison"""
        plt.figure(figsize=(14, 10))
        colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']
        
        for i, (func_type, params, label) in enumerate(functions_list):
            color = colors[i % len(colors)]
            
            if func_type == "linear":
                y = self.linear(self.x_range, *params)
                plt.plot(self.x_range, y, color=color, linewidth=2, label=label)
            
            elif func_type == "quadratic":
                y = self.quadratic(self.x_range, *params)
                plt.plot(self.x_range, y, color=color, linewidth=2, label=label)
            
            elif func_type == "exponential":
                x_range = np.linspace(-3, 3, 1000)
                y = self.exponential(x_range, *params)
                # Clip extreme values for better visualization
                y = np.clip(y, -50, 50)
                plt.plot(x_range, y, color=color, linewidth=2, label=label)
            
            elif func_type == "logarithmic":
                x_range = np.linspace(0.1, 5, 1000)
                y = self.logarithmic(x_range, *params)
                plt.plot(x_range, y, color=color, linewidth=2, label=label)
        
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='black', linewidth=0.5)
        plt.axvline(x=0, color='black', linewidth=0.5)
        plt.xlabel('x', fontsize=12)
        plt.ylabel('f(x)', fontsize=12)
        plt.title('Function Types Comparison', fontsize=14, fontweight='bold')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.tight_layout()
        plt.show()
    
    def print_analysis(self, analysis):
        """Print formatted analysis results"""
        print(f"\n{'='*50}")
        print(f"FUNCTION ANALYSIS: {analysis['type'].upper()}")
        print(f"Parameters: {analysis['parameters']}")
        print(f"{'='*50}")
        
        for key, value in analysis.items():
            if key not in ['type', 'parameters', 'function']:
                if isinstance(value, tuple):
                    print(f"{key.replace('_', ' ').title()}: {value}")
                elif isinstance(value, (int, float)):
                    print(f"{key.replace('_', ' ').title()}: {value:.3f}")
                else:
                    print(f"{key.replace('_', ' ').title()}: {value}")

# Usage example
analyzer = FunctionAnalyzer()

# Define functions to analyze
functions_to_analyze = [
    ("linear", (2, -1), "f(x) = 2x - 1"),
    ("quadratic", (1, -2, 1), "g(x) = x² - 2x + 1"),
    ("exponential", (1, 0.5), "h(x) = e^(0.5x)"),
    ("logarithmic", (2, 1), "k(x) = 2ln(x) + 1")
]

# Analyze each function
for func_type, params, label in functions_to_analyze:
    analysis = analyzer.analyze_function(func_type, params)
    analyzer.print_analysis(analysis)

# Create comparison plot
analyzer.plot_comparison(functions_to_analyze)

# Interactive function explorer
def explore_function():
    """Interactive function exploration"""
    print("\n" + "="*60)
    print("INTERACTIVE FUNCTION EXPLORER")
    print("="*60)
    
    while True:
        print("\nChoose function type:")
        print("1. Linear (mx + b)")
        print("2. Quadratic (ax² + bx + c)")
        print("3. Exponential (ae^(kx))")
        print("4. Logarithmic (a*ln(x) + b)")
        print("5. Exit")
        
        choice = input("\nEnter choice (1-5): ")
        
        if choice == '5':
            break
        
        try:
            if choice == '1':
                m = float(input("Enter slope (m): "))
                b = float(input("Enter y-intercept (b): "))
                analysis = analyzer.analyze_function("linear", (m, b))
            
            elif choice == '2':
                a = float(input("Enter coefficient of x² (a): "))
                b = float(input("Enter coefficient of x (b): "))
                c = float(input("Enter constant term (c): "))
                analysis = analyzer.analyze_function("quadratic", (a, b, c))
            
            elif choice == '3':
                a = float(input("Enter coefficient (a): "))
                k = float(input("Enter growth rate (k): "))
                analysis = analyzer.analyze_function("exponential", (a, k))
            
            elif choice == '4':
                a = float(input("Enter coefficient (a): "))
                b = float(input("Enter vertical shift (b): "))
                analysis = analyzer.analyze_function("logarithmic", (a, b))
            
            else:
                print("Invalid choice!")
                continue
            
            analyzer.print_analysis(analysis)
            
        except ValueError:
            print("Please enter valid numbers!")
        except Exception as e:
            print(f"Error: {e}")

# Uncomment to run interactive explorer
# explore_function()</code></pre>
            </div>
        </section>

        <!-- Section 1.6: Function Behavior Analysis -->
        <section id="function-behavior" class="section-break">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-search-plus text-blue-600 mr-3"></i>
                1.6 Function Behavior Analysis
            </h2>

            <div class="bg-blue-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">Key Concepts for Function Analysis</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>Domain:</strong> Set of all possible input values</li>
                        <li><strong>Range:</strong> Set of all possible output values</li>
                        <li><strong>Intercepts:</strong> Points where graph crosses axes</li>
                        <li><strong>Asymptotes:</strong> Lines the graph approaches</li>
                    </ul>
                    <ul class="space-y-3 text-gray-700">
                        <li><strong>Increasing/Decreasing:</strong> Function behavior trends</li>
                        <li><strong>Concavity:</strong> Curve direction (up or down)</li>
                        <li><strong>Extrema:</strong> Maximum and minimum points</li>
                        <li><strong>Symmetry:</strong> Even, odd, or neither</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Section 1.7: Programming Tools -->
        <section id="programming-tools" class="section-break">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-code text-blue-600 mr-3"></i>
                1.7 Programming Tools Setup
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fab fa-python mr-2"></i>Python Setup
                    </h3>
                    <pre class="language-python"><code># Essential imports for ODE work
import numpy as np
import matplotlib.pyplot as plt
import scipy as sp
from scipy.integrate import odeint, solve_ivp
import sympy as sym

# Configure plotting
plt.style.use('seaborn-v0_8')
plt.rcParams['figure.figsize'] = (10, 6)
plt.rcParams['font.size'] = 12</code></pre>
                </div>
                
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fab fa-r-project mr-2"></i>R Setup
                    </h3>
                    <pre class="language-r"><code># Essential packages for ODE work
library(deSolve)    # ODE solvers
library(ggplot2)    # Advanced plotting
library(dplyr)      # Data manipulation
library(pracma)     # Practical mathematics

# Set plot theme
theme_set(theme_minimal())</code></pre>
                </div>
            </div>
        </section>

        <!-- Section 1.8: Exercises -->
        <section id="exercises" class="section-break">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-pencil-alt text-blue-600 mr-3"></i>
                1.8 Practice Exercises
            </h2>

            <div class="space-y-6">
                <div class="example-box bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-800 mb-4">Exercise 1: Function Identification</h3>
                    <p class="text-gray-700 mb-4">Identify the type of each function and find key properties:</p>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>$f(x) = 3x - 7$</li>
                        <li>$g(x) = 2x^2 - 5x + 1$</li>
                        <li>$h(x) = 4e^{-0.3x}$</li>
                        <li>$k(x) = \ln(2x) + 3$</li>
                    </ol>
                </div>

                <div class="example-box bg-green-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-green-800 mb-4">Exercise 2: Programming Challenge</h3>
                    <p class="text-gray-700 mb-4">Create a program that:</p>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Takes function parameters as input</li>
                        <li>Plots the function over a specified domain</li>
                        <li>Finds and marks key points (intercepts, vertex, etc.)</li>
                        <li>Displays function properties in a formatted table</li>
                    </ol>
                </div>

                <div class="example-box bg-yellow-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-yellow-800 mb-4">Exercise 3: Real-World Modeling</h3>
                    <p class="text-gray-700 mb-4">Model these scenarios with appropriate functions:</p>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>A ball thrown upward (height vs. time)</li>
                        <li>Radioactive decay of a substance</li>
                        <li>Population growth in a city</li>
                        <li>Sound intensity vs. distance from source</li>
                    </ol>
                </div>
            </div>
        </section>

        <!-- Summary and Next Steps -->
        <section class="section-break">
            <div class="bg-gradient-to-r from-blue-50 to-green-50 p-8 rounded-lg">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                    Chapter Summary
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">What We Covered</h3>
                        <ul class="space-y-2 text-gray-700">
                            <li>✓ Linear functions and their properties</li>
                            <li>✓ Quadratic functions and parabolas</li>
                            <li>✓ Exponential growth and decay</li>
                            <li>✓ Logarithmic functions as inverses</li>
                            <li>✓ Function behavior analysis</li>
                            <li>✓ Programming implementations</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Next Steps</h3>
                        <ul class="space-y-2 text-gray-700">
                            <li>→ Chapter 2: Review of Trigonometry</li>
                            <li>→ Chapter 3: Basics of Calculus</li>
                            <li>→ Chapter 4: Mathematical Modeling</li>
                            <li>→ Apply these concepts to differential equations</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg border-l-4 border-blue-500">
                    <h3 class="text-lg font-semibold text-blue-800 mb-3">
                        <i class="fas fa-lightbulb mr-2"></i>Key Takeaway
                    </h3>
                    <p class="text-gray-700">
                        Understanding these fundamental function types is crucial for differential equations because 
                        many solutions involve combinations of linear, exponential, and trigonometric functions. 
                        The programming skills developed here will be essential for numerical solutions and visualizations 
                        throughout the course.
                    </p>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="mt-12 pt-8 border-t-2 border-gray-200 text-center text-gray-600">
            <p class="mb-2">
                <i class="fas fa-book mr-2"></i>
                <strong>Comprehensive ODE Tutorial - Chapter 1</strong>
            </p>
            <p class="text-sm">
                Part of the 300-page ODE Tutorial Series | 
                <i class="fas fa-code mr-1"></i>Python & R Integration | 
                <i class="fas fa-chart-line mr-1"></i>Interactive Learning
            </p>
        </footer>
    </div>

    <script>
        // Wait for MathJax to load before creating charts
        window.addEventListener('load', function() {
            setTimeout(createCharts, 1000);
        });

        function createCharts() {
            // Linear Functions Chart
            const linearCtx = document.getElementById('linearChart').getContext('2d');
            const linearChart = new Chart(linearCtx, {
                type: 'line',
                data: {
                    datasets: [
                        {
                            label: 'f(x) = 2x - 3',
                            data: generatePoints(-5, 5, x => 2*x - 3),
                            borderColor: 'blue',
                            backgroundColor: 'rgba(0, 0, 255, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'g(x) = -x + 2',
                            data: generatePoints(-5, 5, x => -x + 2),
                            borderColor: 'red',
                            backgroundColor: 'rgba(255, 0, 0, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'h(x) = 0.5x + 1',
                            data: generatePoints(-5, 5, x => 0.5*x + 1),
                            borderColor: 'green',
                            backgroundColor: 'rgba(0, 255, 0, 0.1)',
                            borderWidth: 2,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: { display: true, text: 'x' }
                        },
                        y: {
                            title: { display: true, text: 'f(x)' }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Linear Functions Comparison'
                        }
                    }
                }
            });

            // Quadratic Functions Chart
            const quadraticCtx = document.getElementById('quadraticChart').getContext('2d');
            const quadraticChart = new Chart(quadraticCtx, {
                type: 'line',
                data: {
                    datasets: [
                        {
                            label: 'f(x) = x²',
                            data: generatePoints(-3, 3, x => x*x),
                            borderColor: 'blue',
                            backgroundColor: 'rgba(0, 0, 255, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'g(x) = -2x² + 4x + 1',
                            data: generatePoints(-2, 4, x => -2*x*x + 4*x + 1),
                            borderColor: 'red',
                            backgroundColor: 'rgba(255, 0, 0, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'h(x) = 0.5x² - 2x + 3',
                            data: generatePoints(-1, 5, x => 0.5*x*x - 2*x + 3),
                            borderColor: 'green',
                            backgroundColor: 'rgba(0, 255, 0, 0.1)',
                            borderWidth: 2,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: { display: true, text: 'x' }
                        },
                        y: {
                            title: { display: true, text: 'f(x)' }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Quadratic Functions Comparison'
                        }
                    }
                }
            });

            // Exponential Functions Chart
            const exponentialCtx = document.getElementById('exponentialChart').getContext('2d');
            const exponentialChart = new Chart(exponentialCtx, {
                type: 'line',
                data: {
                    datasets: [
                        {
                            label: 'f(x) = 2^x',
                            data: generatePoints(-3, 3, x => Math.pow(2, x)),
                            borderColor: 'blue',
                            backgroundColor: 'rgba(0, 0, 255, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'g(x) = (0.5)^x',
                            data: generatePoints(-3, 3, x => Math.pow(0.5, x)),
                            borderColor: 'red',
                            backgroundColor: 'rgba(255, 0, 0, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'h(x) = e^x',
                            data: generatePoints(-2, 2, x => Math.exp(x)),
                            borderColor: 'green',
                            backgroundColor: 'rgba(0, 255, 0, 0.1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'k(x) = 2e^(-0.5x)',
                            data: generatePoints(-2, 4, x => 2*Math.exp(-0.5*x)),
                            borderColor: 'orange',
                            backgroundColor: 'rgba(255, 165, 0, 0.1)',
                            borderWidth: 2,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: { display: true, text: 'x' }
                        },
                        y: {
                            title: { display: true, text: 'f(x)' },
                            max: 8
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Exponential Functions Comparison'
                        }
                    }
                }
            });

            // Combined Functions Chart
            const allFunctionsCtx = document.getElementById('allFunctionsChart').getContext('2d');
            const allFunctionsChart = new Chart(allFunctionsCtx, {
                type: 'line',
                data: {
                    datasets: [
                        {
                            label: 'Linear: f(x) = x',
                            data: generatePoints(-3, 3, x => x),
                            borderColor: 'blue',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'Quadratic: g(x) = x²',
                            data: generatePoints(-2, 2, x => x*x),
                            borderColor: 'red',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'Exponential: h(x) = e^x',
                            data: generatePoints(-1, 1.5, x => Math.exp(x)),
                            borderColor: 'green',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: 'Logarithmic: k(x) = ln(x)',
                            data: generatePoints(0.1, 3, x => Math.log(x)),
                            borderColor: 'orange',
                            borderWidth: 2,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: { display: true, text: 'x' },
                            min: -3,
                            max: 3
                        },
                        y: {
                            title: { display: true, text: 'f(x)' },
                            min: -3,
                            max: 5
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'All Function Types Comparison'
                        }
                    }
                }
            });
        }

        function generatePoints(start, end, func, numPoints = 100) {
            const points = [];
            const step = (end - start) / numPoints;
            for (let i = 0; i <= numPoints; i++) {
                const x = start + i * step;
                try {
                    const y = func(x);
                    if (isFinite(y)) {
                        points.push({x: x, y: y});
                    }
                } catch (e) {
                    // Skip invalid points
                }
            }
            return points;
        }

        // Add smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDn7iICVVugdIGg4TOdpJLuLEztoAZ6pyLH9wQDTtQMNwnQLTdcsZMrie%2FfDuREIdFyrKIzmP%2F7sGM2640ZfRzG%2FW50M%2BD2F6BXwAzeHJ%2FB%2Fw5sFFt701pJfwwjZ2KuL%2F31gOliRN4U8BMi8nG74fHXE5JoWYkt1YlrmtYunhbCkpdrr7RROO6r6yfrEMwRfoN2X%2F9nus6ftnnU79RacGARB0RotPYBBZs82AtACgzqf5gu4jPRzsl3UoPE2elsI%2Bz7x%2BdqgvyhPG6YnnR0xbvZjtiU%2BGRTSpPGSdrwYx6Nq%2FuCZkgD4vSaiqJhEjjcidsRtPX61444qkkcDIvW44GIB5lgVe%2FYAn%2BS6HUB%2BlD1RWDriytTw6JQZW3hPBFpX2glJAXoOPKvncRC6GqxbV%2FfDU0344CbO6Ix%2FB%2FdHsIlzzLjRD9lSnVmR50yH5mpc8EXp9IwKmf7%2Fg72NASJBVv0t%2Bij9xlnxweWWHeFkO1IV5P9i0TWOgYDeK418w7JszjVZXrnhleaXBbxKa26%2FPzpI%3D";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDn7iICVVugdIGg4TOdpJLuLEztoAZ6pyLH9wQDTtQMNwnQLTdcsZMrie/fDuREIdFyrKIzmP/7sGM2640ZfRzG/W50M+D2F6BXwAzeHJ/B/w5sFFt701pJfwwjZ2KuL/31gOliRN4U8BMi8nG74fHXE5JoWYkt1YlrmtYunhbCkpdrr7RROO6r6yfrEMwRfoN2X/9nus6ftnnU79RacGARB0RotPYBBZs82AtACgzqf5gu4jPRzsl3UoPE2elsI+z7x+dqgvyhPG6YnnR0xbvZjtiU+GRTSpPGSdrwYx6Nq/uCZkgD4vSaiqJhEjjcidsRtPX61444qkkcDIvW44GIB5lgVe/YAn+S6HUB+lD1RWDriytTw6JQZW3hPBFpX2glJAXoOPKvncRC6GqxbV/fDU0344CbO6Ix/B/dHsIlzzLjRD9lSnVmR50yH5mpc8EXp9IwKmf7/g72NASJBVv0t+ij9xlnxweWWHeFkO1IV5P9i0TWOgYDeK418w7JszjVZXrnhleaXBbxKa26/PzpI=";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    