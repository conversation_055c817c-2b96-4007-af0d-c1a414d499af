﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 6: What is a Differential Equation? - ODE Tutorial</title>
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
        }
        
        .code-font {
            font-family: 'JetBrains Mono', monospace;
        }
        
        .math-display {
            margin: 1.5rem 0;
            text-align: center;
        }
        
        .definition-box {
            border-left: 4px solid #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        }
        
        .theorem-box {
            border-left: 4px solid #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        }
        
        .example-box {
            border-left: 4px solid #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }
        
        .warning-box {
            border-left: 4px solid #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        }
        
        .chart-container {
            position: relative;
            height: 500px;
            margin: 2rem 0;
        }
        
        .code-block {
            background: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .code-header {
            background: #2d2d2d;
            color: #fff;
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .language-tabs {
            display: flex;
            gap: 0.5rem;
        }
        
        .language-tab {
            padding: 0.25rem 0.75rem;
            background: #404040;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .language-tab.active {
            background: #3b82f6;
        }
        
        .language-tab:hover {
            background: #525252;
        }
        
        .language-tab.active:hover {
            background: #2563eb;
        }
        
        .language-content {
            position: relative;
        }
        
        .language-panel {
            display: none;
        }
        
        .language-panel.active {
            display: block;
        }
        
        .section-divider {
            height: 2px;
            background: linear-gradient(90deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%);
            margin: 3rem 0;
            border-radius: 1px;
        }
        
        .interactive-demo {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            background: #f9fafb;
        }
        
        .slider-container {
            margin: 1rem 0;
        }
        
        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e5e7eb;
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
        }
        
        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            border: none;
        }
        
        .toc {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
        }
        
        .toc a {
            color: #475569;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }
        
        .toc a:hover {
            color: #3b82f6;
        }
        
        .toc li li {
            margin-left: 1rem;
            font-size: 0.9rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #fef3c7 0%, #fef3c7 100%);
            background-size: 100% 50%;
            background-repeat: no-repeat;
            background-position: 0 85%;
            padding: 0 0.2rem;
        }
          .citation {
            font-size: 0.9rem;
            color: #6b7280;
            font-style: italic;
        }
        
        @media print {
            .no-print {
                display: none;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            body {
                font-size: 12pt;
                line-height: 1.4;
            }
            
            .chart-container {
                height: 400px;
            }
        }
    </style>
    
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                tags: 'ams'
            },
            options: {
                renderActions: {
                    findScript: [10, function (doc) {
                        for (const node of document.querySelectorAll('script[type^="math/tex"]')) {
                            const display = !!node.type.match(/; *mode=display/);
                            const math = new doc.options.MathItem(node.textContent, doc.inputJax[0], display);
                            const text = document.createTextNode('');
                            node.parentNode.replaceChild(text, node);
                            math.start = {node: text, delim: '', n: 0};
                            math.end = {node: text, delim: '', n: 0};
                            doc.math.push(math);
                        }
                    }, '']
                }
            }
        };
    </script>
</head>
<body class="bg-white">
    <!-- Header -->
    <header class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div class="container mx-auto px-6 py-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold mb-2">
                        <i class="fas fa-function mr-3"></i>
                        Chapter 6: What is a Differential Equation?
                    </h1>
                    <p class="text-blue-100 text-lg">Part 2: First-Order Differential Equations • Chapter 1</p>
                    <div class="flex items-center mt-4 space-x-6">
                        <span class="flex items-center">
                            <i class="fas fa-book mr-2"></i>
                            Foundation Concepts
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-code mr-2"></i>
                            Python & R
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-chart-line mr-2"></i>
                            Visualizations
                        </span>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="bg-blue-500 bg-opacity-30 rounded-lg p-4">
                        <h3 class="font-semibold mb-2">Learning Objectives</h3>
                        <ul class="text-sm space-y-1">                            <li>• Understand ODE definitions and terminology</li>
                            <li>• Classify differential equations by type</li>
                            <li>• Recognize solution concepts</li>
                            <li>• Visualize differential equations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        
        <!-- Table of Contents -->
        <div class="toc">
            <h2 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-list mr-2 text-blue-600"></i>
                Table of Contents
            </h2>
            <ul>
                <li><a href="#introduction">6.1 Introduction to Differential Equations</a></li>
                <li><a href="#definitions">6.2 Definitions and Basic Terminology</a></li>
                <li><a href="#classification">6.3 Classification of Differential Equations</a>
                    <ul>
                        <li><a href="#order">6.3.1 Order of Differential Equations</a></li>
                        <li><a href="#linearity">6.3.2 Linear vs Nonlinear</a></li>
                        <li><a href="#ode-pde">6.3.3 Ordinary vs Partial</a></li>
                    </ul>
                </li>
                <li><a href="#solutions">6.4 Solution Concepts</a>
                    <ul>
                        <li><a href="#general-solutions">6.4.1 General Solutions</a></li>
                        <li><a href="#particular-solutions">6.4.2 Particular Solutions</a></li>
                        <li><a href="#singular-solutions">6.4.3 Singular Solutions</a></li>
                    </ul>
                </li>
                <li><a href="#initial-boundary">6.5 Initial Value Problems vs Boundary Value Problems</a></li>
                <li><a href="#existence-uniqueness">6.6 Existence and Uniqueness</a></li>
                <li><a href="#visualization">6.7 Visualizing Differential Equations</a></li>
                <li><a href="#practical-examples">6.8 Practical Examples</a></li>
                <li><a href="#verification">6.9 Verification of Solutions</a></li>
                <li><a href="#exercises">6.10 Exercises and Projects</a></li>
            </ul>
        </div>

        <!-- Section 6.1: Introduction -->
        <section id="introduction" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-play-circle mr-3 text-blue-600"></i>
                6.1 Introduction to Differential Equations
            </h2>
            
            <div class="prose max-w-none">
                <p class="text-lg mb-6">
                    Welcome to the world of differential equations! After building our mathematical foundations in Part 1, 
                    we're now ready to explore one of the most powerful and widely-used tools in mathematics, science, 
                    and engineering. <span class="highlight">Differential equations describe how quantities change</span> and are 
                    fundamental to understanding dynamic systems in virtually every field of study.
                </p>

                <div class="definition-box p-6 rounded-lg mb-6">
                    <h3 class="text-xl font-semibold mb-3 flex items-center">
                        <i class="fas fa-lightbulb mr-2 text-blue-600"></i>
                        What Makes ODEs Special?
                    </h3>
                    <p class="mb-4">
                        Differential equations are unique because they don't just describe static relationships between 
                        variablesâ€”they describe <strong>how things change</strong>. While algebraic equations might tell us 
                        that $y = x^2$, a differential equation tells us something like "the rate of change of $y$ 
                        is proportional to $y$ itself": $\frac{dy}{dx} = ky$.
                    </p>
                    <p>
                        This fundamental shift from describing <em>what is</em> to describing <em>how things change</em> 
                        opens up the mathematical modeling of dynamic processes throughout nature, technology, and society.
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mb-8">
                    <div class="example-box p-6 rounded-lg">
                        <h4 class="font-semibold mb-3 flex items-center">
                            <i class="fas fa-seedling mr-2 text-green-600"></i>
                            Population Growth
                        </h4>
                        <p class="text-sm mb-2">
                            "The rate of population growth is proportional to the current population"
                        </p>
                        <div class="math-display">
                            $$\frac{dP}{dt} = kP$$
                        </div>
                    </div>
                    
                    <div class="example-box p-6 rounded-lg">
                        <h4 class="font-semibold mb-3 flex items-center">
                            <i class="fas fa-thermometer-half mr-2 text-red-600"></i>
                            Newton's Law of Cooling
                        </h4>
                        <p class="text-sm mb-2">
                            "The rate of temperature change is proportional to the temperature difference"
                        </p>
                        <div class="math-display">
                            $$\frac{dT}{dt} = -k(T - T_{\text{env}})$$
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 p-6 rounded-lg mb-8">
                    <h3 class="text-lg font-semibold mb-4">Why Study Differential Equations?</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium mb-2 text-blue-600">Scientific Applications</h4>
                            <ul class="text-sm space-y-1">                                <li>• Physics: Motion, waves, quantum mechanics</li>
                                <li>• Biology: Population dynamics, epidemiology</li>
                                <li>• Chemistry: Reaction rates, concentration</li>
                                <li>• Economics: Market dynamics, growth models</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-green-600">Engineering Applications</h4>
                            <ul class="text-sm space-y-1">                                <li>• Control systems and automation</li>
                                <li>• Circuit analysis and design</li>
                                <li>• Structural analysis and vibrations</li>
                                <li>• Signal processing and communications</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.2: Definitions and Basic Terminology -->
        <section id="definitions" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-book mr-3 text-blue-600"></i>
                6.2 Definitions and Basic Terminology
            </h2>

            <div class="definition-box p-6 rounded-lg mb-6">
                <h3 class="text-xl font-semibold mb-3 flex items-center">
                    <i class="fas fa-flag mr-2 text-blue-600"></i>
                    Fundamental Definition
                </h3>
                <p class="mb-4">
                    A <strong>differential equation</strong> is an equation that relates a function to one or more of its derivatives.
                </p>
                <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                    <p class="font-mono text-sm">
                        If $y = f(x)$ is our unknown function, then a differential equation involves 
                        $y$, $\frac{dy}{dx}$, $\frac{d^2y}{dx^2}$, etc.
                    </p>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 class="font-semibold mb-3 text-lg">Key Components</h4>
                    <ul class="space-y-2">
                        <li><strong>Dependent Variable:</strong> The unknown function (often $y$)</li>
                        <li><strong>Independent Variable:</strong> The variable the function depends on (often $x$ or $t$)</li>
                        <li><strong>Derivatives:</strong> The rates of change ($y'$, $y''$, etc.)</li>
                        <li><strong>Parameters:</strong> Constants that affect the equation's behavior</li>
                    </ul>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 class="font-semibold mb-3 text-lg">Notation Systems</h4>
                    <div class="space-y-3">
                        <div>
                            <p class="font-medium">Leibniz Notation:</p>
                            <p class="font-mono text-sm">$\frac{dy}{dx}$, $\frac{d^2y}{dx^2}$</p>
                        </div>
                        <div>
                            <p class="font-medium">Prime Notation:</p>
                            <p class="font-mono text-sm">$y'$, $y''$, $y'''$</p>
                        </div>
                        <div>
                            <p class="font-medium">Dot Notation (for time):</p>
                            <p class="font-mono text-sm">$\dot{y}$, $\ddot{y}$</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="example-box p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold mb-4">Examples of Differential Equations</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium mb-2">Simple Examples:</h4>
                        <div class="space-y-3 font-mono text-sm">
                            <div>$\frac{dy}{dx} = 2x$ <span class="text-gray-600 font-sans text-xs">(rate equals twice the input)</span></div>
                            <div>$y' + 3y = 0$ <span class="text-gray-600 font-sans text-xs">(exponential decay)</span></div>
                            <div>$y'' + y = 0$ <span class="text-gray-600 font-sans text-xs">(harmonic oscillator)</span></div>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-medium mb-2">More Complex Examples:</h4>
                        <div class="space-y-3 font-mono text-sm">
                            <div>$\frac{dy}{dx} = \frac{x+y}{x-y}$ <span class="text-gray-600 font-sans text-xs">(nonlinear)</span></div>
                            <div>$y'' + \sin(y) = 0$ <span class="text-gray-600 font-sans text-xs">(pendulum equation)</span></div>
                            <div>$\frac{dy}{dx} = y^2 - x$ <span class="text-gray-600 font-sans text-xs">(nonlinear growth)</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.3: Classification -->
        <section id="classification" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-layer-group mr-3 text-blue-600"></i>
                6.3 Classification of Differential Equations
            </h2>

            <p class="text-lg mb-6">
                Understanding how to classify differential equations is crucial because different types require different 
                solution methods. Let's explore the main classification schemes.
            </p>

            <!-- Order Classification -->
            <div id="order" class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">6.3.1 Order of Differential Equations</h3>
                
                <div class="definition-box p-6 rounded-lg mb-6">
                    <h4 class="text-lg font-semibold mb-3">Definition: Order</h4>
                    <p class="mb-4">
                        The <strong>order</strong> of a differential equation is the highest derivative that appears in the equation.
                    </p>
                    <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                        <div class="grid md:grid-cols-3 gap-4 text-center">
                            <div>
                                <p class="font-semibold text-blue-600">First Order</p>
                                <p class="font-mono text-sm">$\frac{dy}{dx} = f(x,y)$</p>
                            </div>
                            <div>
                                <p class="font-semibold text-green-600">Second Order</p>
                                <p class="font-mono text-sm">$\frac{d^2y}{dx^2} = f(x,y,y')$</p>
                            </div>
                            <div>
                                <p class="font-semibold text-purple-600">nth Order</p>
                                <p class="font-mono text-sm">$y^{(n)} = f(x,y,y',...,y^{(n-1)})$</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid md:grid-cols-3 gap-4 mb-6">
                    <div class="example-box p-4 rounded-lg">
                        <h5 class="font-semibold mb-2 text-blue-600">First Order Examples</h5>
                        <div class="space-y-2 font-mono text-sm">
                            <div>$\frac{dy}{dx} = 3x + 2$</div>
                            <div>$y' = xy$</div>
                            <div>$\frac{dP}{dt} = rP$</div>
                        </div>
                    </div>
                    
                    <div class="example-box p-4 rounded-lg">
                        <h5 class="font-semibold mb-2 text-green-600">Second Order Examples</h5>
                        <div class="space-y-2 font-mono text-sm">
                            <div>$y'' + y' + y = 0$</div>
                            <div>$\frac{d^2y}{dx^2} = -ky$</div>
                            <div>$m\ddot{x} + c\dot{x} + kx = F(t)$</div>
                        </div>
                    </div>
                    
                    <div class="example-box p-4 rounded-lg">
                        <h5 class="font-semibold mb-2 text-purple-600">Higher Order Examples</h5>
                        <div class="space-y-2 font-mono text-sm">
                            <div>$y''' + y'' + y' + y = 0$</div>
                            <div>$y^{(4)} + y = x$</div>
                            <div>$\frac{d^5y}{dx^5} = \sin(x)$</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Linearity Classification -->
            <div id="linearity" class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">6.3.2 Linear vs Nonlinear</h3>
                
                <div class="definition-box p-6 rounded-lg mb-6">
                    <h4 class="text-lg font-semibold mb-3">Definition: Linearity</h4>
                    <p class="mb-4">
                        A differential equation is <strong>linear</strong> if it can be written in the form:
                    </p>
                    <div class="math-display">
                        $$a_n(x)\frac{d^ny}{dx^n} + a_{n-1}(x)\frac{d^{n-1}y}{dx^{n-1}} + \cdots + a_1(x)\frac{dy}{dx} + a_0(x)y = g(x)$$
                    </div>
                    <p class="mt-4">
                        Key characteristics of linear equations:
                    </p>
                    <ul class="list-disc pl-6 mt-2 space-y-1">
                        <li>The dependent variable $y$ and all its derivatives appear to the first power only</li>
                        <li>No products of $y$ and its derivatives</li>
                        <li>No transcendental functions of $y$ (like $\sin(y)$, $e^y$, etc.)</li>
                        <li>Coefficients can be functions of the independent variable</li>
                    </ul>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <h4 class="font-semibold mb-3 text-green-700 flex items-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            Linear Examples
                        </h4>
                        <div class="space-y-3">
                            <div>
                                <p class="font-mono text-sm">$\frac{dy}{dx} + 2y = x^2$</p>
                                <p class="text-xs text-gray-600">First-order linear</p>
                            </div>
                            <div>
                                <p class="font-mono text-sm">$y'' + 3xy' + y = \cos(x)$</p>
                                <p class="text-xs text-gray-600">Second-order linear</p>
                            </div>
                            <div>
                                <p class="font-mono text-sm">$x^2y'' + xy' + (x^2-1)y = 0$</p>
                                <p class="text-xs text-gray-600">Bessel's equation</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <h4 class="font-semibold mb-3 text-red-700 flex items-center">
                            <i class="fas fa-times-circle mr-2"></i>
                            Nonlinear Examples
                        </h4>
                        <div class="space-y-3">
                            <div>
                                <p class="font-mono text-sm">$\frac{dy}{dx} = y^2$</p>
                                <p class="text-xs text-gray-600">$y$ squared term</p>
                            </div>
                            <div>
                                <p class="font-mono text-sm">$y'' + \sin(y) = 0$</p>
                                <p class="text-xs text-gray-600">Transcendental function of $y$</p>
                            </div>
                            <div>
                                <p class="font-mono text-sm">$y' + yy'' = x$</p>
                                <p class="text-xs text-gray-600">Product of $y$ and $y''$</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="theorem-box p-6 rounded-lg mb-6">
                    <h4 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-star mr-2 text-green-600"></i>
                        Why Linearity Matters
                    </h4>
                    <p class="mb-4">Linear differential equations have several important properties:</p>
                    <ul class="list-disc pl-6 space-y-2">
                        <li><strong>Superposition Principle:</strong> If $y_1$ and $y_2$ are solutions, then $c_1y_1 + c_2y_2$ is also a solution</li>
                        <li><strong>Existence and Uniqueness:</strong> Well-developed theory guarantees solution existence and uniqueness</li>
                        <li><strong>Systematic Solution Methods:</strong> General techniques exist for solving linear equations</li>
                        <li><strong>Computational Advantages:</strong> Easier to solve numerically and analytically</li>
                    </ul>
                </div>
            </div>

            <!-- ODE vs PDE -->
            <div id="ode-pde" class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">6.3.3 Ordinary vs Partial Differential Equations</h3>
                
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="definition-box p-6 rounded-lg">
                        <h4 class="text-lg font-semibold mb-3 text-blue-600">Ordinary Differential Equations (ODEs)</h4>
                        <p class="mb-4">
                            An ODE contains derivatives with respect to <strong>only one</strong> independent variable.
                        </p>
                        <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                            <p class="font-mono text-sm mb-2">$\frac{dy}{dx} = 3x + 2y$</p>
                            <p class="text-xs text-gray-600">Only derivatives with respect to $x$</p>
                        </div>
                        <p class="mt-4 text-sm">
                            <strong>Focus of this course:</strong> We'll study ODEs exclusively, which describe 
                            systems depending on a single variable (often time).
                        </p>
                    </div>
                    
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                        <h4 class="text-lg font-semibold mb-3 text-purple-600">Partial Differential Equations (PDEs)</h4>
                        <p class="mb-4">
                            A PDE contains partial derivatives with respect to <strong>two or more</strong> independent variables.
                        </p>
                        <div class="bg-white p-4 rounded border-l-4 border-purple-300">
                            <p class="font-mono text-sm mb-2">$\frac{\partial u}{\partial t} = \frac{\partial^2 u}{\partial x^2}$</p>
                            <p class="text-xs text-gray-600">Heat equation: derivatives w.r.t. both $t$ and $x$</p>
                        </div>
                        <p class="mt-4 text-sm">
                            <strong>Advanced topic:</strong> PDEs describe phenomena like heat conduction, 
                            wave propagation, and fluid flowâ€”topics for advanced courses.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.4: Solution Concepts -->
        <section id="solutions" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-puzzle-piece mr-3 text-blue-600"></i>
                6.4 Solution Concepts
            </h2>

            <p class="text-lg mb-6">
                Understanding what constitutes a "solution" to a differential equation is fundamental. Unlike algebraic 
                equations where solutions are numbers, differential equation solutions are <strong>functions</strong>.
            </p>

            <div class="definition-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-semibold mb-3">What is a Solution?</h3>
                <p class="mb-4">
                    A <strong>solution</strong> to a differential equation is a function that, when substituted into the 
                    equation along with its derivatives, satisfies the equation identically.
                </p>
                <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                    <p class="mb-2">
                        <strong>Example:</strong> Consider the differential equation $\frac{dy}{dx} = 2x$
                    </p>
                    <p class="mb-2">
                        <strong>Proposed solution:</strong> $y = x^2 + C$
                    </p>
                    <p class="mb-2">
                        <strong>Verification:</strong> $\frac{dy}{dx} = \frac{d}{dx}(x^2 + C) = 2x$ âœ“
                    </p>
                </div>
            </div>

            <!-- General Solutions -->
            <div id="general-solutions" class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">6.4.1 General Solutions</h3>
                
                <div class="theorem-box p-6 rounded-lg mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-green-600">General Solution</h4>
                    <p class="mb-4">
                        The <strong>general solution</strong> of an $n$-th order differential equation is a family of 
                        solutions containing $n$ arbitrary constants.
                    </p>
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <p class="mb-2">For a first-order ODE: General solution has <strong>1</strong> arbitrary constant</p>
                        <p>For a second-order ODE: General solution has <strong>2</strong> arbitrary constants</p>
                    </div>
                </div>

                <div class="example-box p-6 rounded-lg mb-6">
                    <h4 class="font-semibold mb-3">Example: Second-Order ODE</h4>
                    <p class="mb-2">Consider the differential equation: $y'' + y = 0$</p>
                    <p class="mb-2"><strong>General solution:</strong> $y = C_1\cos(x) + C_2\sin(x)$</p>
                    <p class="text-sm text-gray-600">
                        This represents infinitely many solutionsâ€”one for each choice of constants $C_1$ and $C_2$.
                    </p>
                </div>

                <!-- Interactive Demo for General Solutions -->
                <div class="interactive-demo">
                    <h4 class="font-semibold mb-4">Interactive: General Solution Family</h4>
                    <p class="mb-4">Explore how the constants $C_1$ and $C_2$ affect the solution $y = C_1\cos(x) + C_2\sin(x)$:</p>
                    
                    <div class="grid md:grid-cols-2 gap-4 mb-4">
                        <div class="slider-container">
                            <label class="block text-sm font-medium mb-2">$C_1$ (Cosine coefficient): <span id="c1-value">1.0</span></label>
                            <input type="range" id="c1-slider" class="slider" min="-3" max="3" step="0.1" value="1">
                        </div>
                        <div class="slider-container">
                            <label class="block text-sm font-medium mb-2">$C_2$ (Sine coefficient): <span id="c2-value">0.0</span></label>
                            <input type="range" id="c2-slider" class="slider" min="-3" max="3" step="0.1" value="0">
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="generalSolutionChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Particular Solutions -->
            <div id="particular-solutions" class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">6.4.2 Particular Solutions</h3>
                
                <div class="theorem-box p-6 rounded-lg mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-green-600">Particular Solution</h4>
                    <p class="mb-4">
                        A <strong>particular solution</strong> is obtained from the general solution by specifying 
                        values for the arbitrary constants. This typically requires additional conditions.
                    </p>
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <p class="mb-2">
                            <strong>Initial Conditions:</strong> Specify the function and its derivatives at a particular point
                        </p>
                        <p class="mb-2">
                            <strong>Example:</strong> $y(0) = 1$, $y'(0) = 0$ for the equation $y'' + y = 0$
                        </p>
                        <p>
                            <strong>Result:</strong> Unique particular solution $y = \cos(x)$
                        </p>
                    </div>
                </div>

                <div class="example-box p-6 rounded-lg mb-6">
                    <h4 class="font-semibold mb-3">Step-by-Step Example</h4>
                    <div class="space-y-3">
                        <div class="bg-white p-4 rounded border-l-4 border-yellow-300">
                            <p class="font-semibold">Step 1: Start with general solution</p>
                            <p class="font-mono">$y = C_1\cos(x) + C_2\sin(x)$</p>
                        </div>
                        <div class="bg-white p-4 rounded border-l-4 border-yellow-300">
                            <p class="font-semibold">Step 2: Apply initial condition $y(0) = 1$</p>
                            <p class="font-mono">$1 = C_1\cos(0) + C_2\sin(0) = C_1 \cdot 1 + C_2 \cdot 0 = C_1$</p>
                            <p>So $C_1 = 1$</p>
                        </div>
                        <div class="bg-white p-4 rounded border-l-4 border-yellow-300">
                            <p class="font-semibold">Step 3: Apply initial condition $y'(0) = 0$</p>
                            <p class="font-mono">$y' = -C_1\sin(x) + C_2\cos(x)$</p>
                            <p class="font-mono">$0 = -C_1\sin(0) + C_2\cos(0) = 0 + C_2 \cdot 1 = C_2$</p>
                            <p>So $C_2 = 0$</p>
                        </div>
                        <div class="bg-white p-4 rounded border-l-4 border-yellow-300">
                            <p class="font-semibold">Step 4: Particular solution</p>
                            <p class="font-mono">$y = 1 \cdot \cos(x) + 0 \cdot \sin(x) = \cos(x)$</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Singular Solutions -->
            <div id="singular-solutions" class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">6.4.3 Singular Solutions</h3>
                
                <div class="warning-box p-6 rounded-lg mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-red-600">Singular Solutions</h4>
                    <p class="mb-4">
                        A <strong>singular solution</strong> is a solution that cannot be obtained from the general 
                        solution by specifying values of the arbitrary constants. These are special solutions that 
                        often arise in nonlinear equations.
                    </p>
                    <div class="bg-white p-4 rounded border-l-4 border-red-300">
                        <p class="mb-2">
                            <strong>Example:</strong> Consider $\left(\frac{dy}{dx}\right)^2 = 4y$
                        </p>
                        <p class="mb-2">
                            <strong>General solution:</strong> $y = (x + C)^2$
                        </p>
                        <p>
                            <strong>Singular solution:</strong> $y = 0$ (cannot be obtained from general solution)
                        </p>
                    </div>
                </div>

                <div class="bg-gray-50 p-6 rounded-lg">
                    <h4 class="font-semibold mb-3">Note on Singular Solutions</h4>
                    <p class="text-sm">
                        Singular solutions are more common in nonlinear differential equations and represent 
                        special cases where the standard solution methods may not apply. We'll encounter them 
                        occasionally but won't focus on them in this introductory course.
                    </p>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.5: Initial Value Problems vs Boundary Value Problems -->
        <section id="initial-boundary" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-crosshairs mr-3 text-blue-600"></i>
                6.5 Initial Value Problems vs Boundary Value Problems
            </h2>

            <p class="text-lg mb-6">
                To find a particular solution, we need additional information beyond the differential equation itself. 
                This information comes in the form of <strong>conditions</strong> that specify the behavior of the solution.
            </p>

            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <div class="definition-box p-6 rounded-lg">
                    <h3 class="text-xl font-semibold mb-3 text-blue-600">Initial Value Problem (IVP)</h3>
                    <p class="mb-4">
                        An IVP specifies the value of the solution and its derivatives at a <strong>single point</strong> 
                        (usually the initial point).
                    </p>
                    <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                        <p class="font-semibold mb-2">General Form:</p>
                        <div class="font-mono text-sm space-y-1">
                            <p>$y^{(n)} = f(x, y, y', \ldots, y^{(n-1)})$</p>
                            <p>$y(x_0) = y_0$</p>
                            <p>$y'(x_0) = y_1$</p>
                            <p>$\vdots$</p>
                            <p>$y^{(n-1)}(x_0) = y_{n-1}$</p>
                        </div>
                    </div>
                    <div class="mt-4 text-sm">
                        <strong>Applications:</strong> Time-dependent problems where we know the initial state 
                        (position, velocity, concentration, etc.) at $t = 0$.
                    </div>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-3 text-green-600">Boundary Value Problem (BVP)</h3>
                    <p class="mb-4">
                        A BVP specifies conditions at <strong>two or more different points</strong>, typically 
                        at the boundaries of an interval.
                    </p>
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <p class="font-semibold mb-2">General Form:</p>
                        <div class="font-mono text-sm space-y-1">
                            <p>$y'' = f(x, y, y')$</p>
                            <p>$y(a) = \alpha$</p>
                            <p>$y(b) = \beta$</p>
                        </div>
                    </div>
                    <div class="mt-4 text-sm">
                        <strong>Applications:</strong> Steady-state problems, structural analysis, 
                        heat conduction with fixed boundary temperatures.
                    </div>
                </div>
            </div>

            <div class="example-box p-6 rounded-lg mb-8">
                <h3 class="text-lg font-semibold mb-4">Comparative Examples</h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                        <h4 class="font-semibold mb-2 text-blue-600">IVP Example</h4>
                        <div class="font-mono text-sm space-y-2">
                            <p>$y'' + y = 0$</p>
                            <p>$y(0) = 1$</p>
                            <p>$y'(0) = 0$</p>
                        </div>
                        <p class="mt-2 text-sm">
                            <strong>Physical interpretation:</strong> A mass-spring system with initial displacement 
                            1 and initial velocity 0.
                        </p>
                        <p class="mt-2 text-sm font-medium">
                            <strong>Solution:</strong> $y = \cos(x)$
                        </p>
                    </div>
                    
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <h4 class="font-semibold mb-2 text-green-600">BVP Example</h4>
                        <div class="font-mono text-sm space-y-2">
                            <p>$y'' + y = 0$</p>
                            <p>$y(0) = 0$</p>
                            <p>$y(\pi) = 0$</p>
                        </div>
                        <p class="mt-2 text-sm">
                            <strong>Physical interpretation:</strong> A vibrating string fixed at both ends.
                        </p>
                        <p class="mt-2 text-sm font-medium">
                            <strong>Solution:</strong> $y = C\sin(x)$ for any constant $C$
                        </p>
                    </div>
                </div>
            </div>

            <div class="theorem-box p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold mb-3 text-green-600">Key Differences</h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b border-green-200">
                                <th class="text-left p-2">Aspect</th>
                                <th class="text-left p-2">Initial Value Problem</th>
                                <th class="text-left p-2">Boundary Value Problem</th>
                            </tr>
                        </thead>
                        <tbody class="space-y-2">
                            <tr class="border-b border-green-100">
                                <td class="p-2 font-medium">Conditions</td>
                                <td class="p-2">All at one point</td>
                                <td class="p-2">At multiple points</td>
                            </tr>
                            <tr class="border-b border-green-100">
                                <td class="p-2 font-medium">Solution existence</td>
                                <td class="p-2">Usually unique</td>
                                <td class="p-2">May have 0, 1, or âˆž solutions</td>
                            </tr>
                            <tr class="border-b border-green-100">
                                <td class="p-2 font-medium">Typical applications</td>
                                <td class="p-2">Time evolution</td>
                                <td class="p-2">Steady-state problems</td>
                            </tr>
                            <tr class="border-b border-green-100">
                                <td class="p-2 font-medium">Numerical methods</td>
                                <td class="p-2">March forward in time</td>
                                <td class="p-2">Simultaneous equations</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.6: Existence and Uniqueness -->
        <section id="existence-uniqueness" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-balance-scale mr-3 text-blue-600"></i>
                6.6 Existence and Uniqueness
            </h2>

            <p class="text-lg mb-6">
                Before attempting to solve a differential equation, it's important to know whether a solution exists 
                and whether it's unique. These fundamental questions are addressed by existence and uniqueness theorems.
            </p>

            <div class="theorem-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-semibold mb-3 text-green-600">
                    <i class="fas fa-star mr-2"></i>
                    Existence and Uniqueness Theorem (First-Order IVP)
                </h3>
                <p class="mb-4">
                    Consider the initial value problem:
                </p>
                <div class="math-display">
                    $$\frac{dy}{dx} = f(x,y), \quad y(x_0) = y_0$$
                </div>
                <p class="mb-4">
                    If $f(x,y)$ and $\frac{\partial f}{\partial y}$ are continuous in some rectangle containing 
                    the point $(x_0, y_0)$, then there exists a unique solution $y = \phi(x)$ in some interval 
                    containing $x_0$.
                </p>
                <div class="bg-white p-4 rounded border-l-4 border-green-300">
                    <p class="font-semibold mb-2">What this means:</p>
                    <ul class="list-disc pl-4 space-y-1">
                        <li><strong>Existence:</strong> A solution exists</li>
                        <li><strong>Uniqueness:</strong> The solution is unique</li>
                        <li><strong>Local:</strong> The solution exists in some interval, possibly small</li>
                    </ul>
                </div>
            </div>

            <div class="example-box p-6 rounded-lg mb-8">
                <h3 class="text-lg font-semibold mb-4">Examples: Applying the Theorem</h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <h4 class="font-semibold mb-2 text-green-600">âœ“ Theorem Applies</h4>
                        <div class="font-mono text-sm mb-2">$\frac{dy}{dx} = x + y$, $y(0) = 1$</div>
                        <div class="text-sm space-y-1">
                            <p>$f(x,y) = x + y$ is continuous everywhere</p>
                            <p>$\frac{\partial f}{\partial y} = 1$ is continuous everywhere</p>
                            <p><strong>Result:</strong> Unique solution exists</p>
                        </div>
                    </div>
                    
                    <div class="bg-white p-4 rounded border-l-4 border-red-300">
                        <h4 class="font-semibold mb-2 text-red-600">âš  Theorem Doesn't Apply</h4>
                        <div class="font-mono text-sm mb-2">$\frac{dy}{dx} = \sqrt{y}$, $y(0) = 0$</div>
                        <div class="text-sm space-y-1">
                            <p>$f(x,y) = \sqrt{y}$ is continuous for $y \geq 0$</p>
                            <p>$\frac{\partial f}{\partial y} = \frac{1}{2\sqrt{y}}$ is not continuous at $y = 0$</p>
                            <p><strong>Result:</strong> Multiple solutions exist!</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="warning-box p-6 rounded-lg mb-8">
                <h3 class="text-lg font-semibold mb-3 text-red-600">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    When Uniqueness Fails
                </h3>
                <p class="mb-4">
                    The equation $\frac{dy}{dx} = \sqrt{y}$ with $y(0) = 0$ has infinitely many solutions:
                </p>
                <div class="bg-white p-4 rounded border-l-4 border-red-300">
                    <div class="font-mono text-sm space-y-2">
                        <p>$y_1(x) = 0$ for all $x$</p>
                        <p>$y_2(x) = \begin{cases} 0 & \text{if } x \leq a \\ \frac{(x-a)^2}{4} & \text{if } x > a \end{cases}$ for any $a \geq 0$</p>
                    </div>
                </div>
                <p class="mt-4 text-sm">
                    This illustrates why the conditions of the existence and uniqueness theorem are important!
                </p>
            </div>

            <div class="interactive-demo">
                <h4 class="font-semibold mb-4">Interactive: Visualizing Non-Unique Solutions</h4>
                <p class="mb-4">
                    Explore the multiple solutions to $\frac{dy}{dx} = \sqrt{y}$ with $y(0) = 0$:
                </p>
                
                <div class="slider-container mb-4">
                    <label class="block text-sm font-medium mb-2">Branching point $a$: <span id="a-value">1.0</span></label>
                    <input type="range" id="a-slider" class="slider" min="0" max="3" step="0.1" value="1">
                </div>
                
                <div class="chart-container">
                    <canvas id="nonUniqueChart"></canvas>
                </div>
            </div>

            <div class="theorem-box p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-3 text-green-600">Practical Implications</h3>
                <ul class="list-disc pl-6 space-y-2">
                    <li><strong>Modeling Confidence:</strong> When conditions are met, we can be confident our model has a unique, well-defined solution</li>
                    <li><strong>Numerical Methods:</strong> Algorithms can be designed knowing a unique solution exists</li>
                    <li><strong>Parameter Sensitivity:</strong> Small changes in initial conditions lead to small changes in solutions (when uniqueness holds)</li>
                    <li><strong>Problem Diagnosis:</strong> If numerical methods give different answers, check if uniqueness conditions are violated</li>
                </ul>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.7: Visualizing Differential Equations -->
        <section id="visualization" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-eye mr-3 text-blue-600"></i>
                6.7 Visualizing Differential Equations
            </h2>

            <p class="text-lg mb-6">
                One of the most powerful ways to understand differential equations is through visualization. 
                Even when we can't solve an equation analytically, we can still understand its behavior graphically.
            </p>

            <div class="definition-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-semibold mb-3">Direction Fields (Slope Fields)</h3>
                <p class="mb-4">
                    For a first-order differential equation $\frac{dy}{dx} = f(x,y)$, we can create a 
                    <strong>direction field</strong> by plotting small line segments with slope $f(x,y)$ 
                    at various points $(x,y)$ in the plane.
                </p>
                <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                    <p class="mb-2">
                        <strong>Key Insight:</strong> Solutions to the differential equation are curves that 
                        are tangent to the direction field at every point.
                    </p>
                    <p>
                        <strong>Advantage:</strong> We can visualize solution behavior without solving the equation!
                    </p>
                </div>
            </div>

            <!-- Interactive Direction Field Demo -->
            <div class="interactive-demo">
                <h4 class="font-semibold mb-4">Interactive: Direction Fields</h4>
                <p class="mb-4">
                    Select a differential equation to see its direction field:
                </p>
                
                <div class="mb-4">
                    <select id="equation-select" class="border border-gray-300 rounded px-3 py-2">
                        <option value="xy">dy/dx = x + y</option>
                        <option value="x">dy/dx = x</option>
                        <option value="y">dy/dx = y</option>
                        <option value="sinx">dy/dx = sin(x)</option>
                        <option value="xy2">dy/dx = x - y</option>
                    </select>
                </div>
                
                <div class="chart-container">
                    <canvas id="directionFieldChart"></canvas>
                </div>
                
                <p class="text-sm mt-4 text-gray-600">
                    <strong>Instructions:</strong> The arrows show the direction field. Solution curves follow 
                    these arrows, always staying tangent to them.
                </p>
            </div>

            <!-- Code Examples -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Creating Direction Fields with Code</h3>
                
                <div class="code-block mb-6">
                    <div class="code-header">
                        <span class="text-sm">Direction Field Visualization</span>
                        <div class="language-tabs">
                            <span class="language-tab active" data-lang="python">Python</span>
                            <span class="language-tab" data-lang="r">R</span>
                        </div>
                    </div>
                    <div class="language-content">
                        <div class="language-panel active" data-lang="python">
<pre><code class="language-python">import numpy as np
import matplotlib.pyplot as plt

def direction_field(f, x_range, y_range, density=20):
    """
    Create a direction field for the ODE dy/dx = f(x,y)
    
    Parameters:
    f: function defining dy/dx = f(x,y)
    x_range: tuple (x_min, x_max)
    y_range: tuple (y_min, y_max)
    density: number of points along each axis
    """
    x = np.linspace(x_range[0], x_range[1], density)
    y = np.linspace(y_range[0], y_range[1], density)
    X, Y = np.meshgrid(x, y)
    
    # Calculate slopes at each point
    DX = np.ones_like(X)
    DY = f(X, Y)
    
    # Normalize arrows for better visualization
    M = np.sqrt(DX**2 + DY**2)
    DX_norm = DX / M
    DY_norm = DY / M
    
    # Create the plot
    plt.figure(figsize=(10, 8))
    plt.quiver(X, Y, DX_norm, DY_norm, 
               angles='xy', scale_units='xy', scale=1, 
               color='blue', alpha=0.6, width=0.003)
    
    plt.xlabel('x')
    plt.ylabel('y')
    plt.title('Direction Field for dy/dx = f(x,y)')
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    return plt.gcf()

# Example: dy/dx = x + y
def f_example(x, y):
    return x + y

# Create and display direction field
fig = direction_field(f_example, (-3, 3), (-3, 3))
plt.show()

# Add solution curves
def plot_solution_curves(f, x_range, y_range, initial_conditions):
    """
    Plot solution curves using Euler's method
    """
    from scipy.integrate import odeint
    
    x = np.linspace(x_range[0], x_range[1], 100)
    
    plt.figure(figsize=(10, 8))
    
    # Plot direction field
    direction_field(f, x_range, y_range, density=15)
    
    # Plot solution curves
    for y0 in initial_conditions:
        def ode_func(y, x):
            return f(x, y)
        
        y_sol = odeint(ode_func, y0, x)
        plt.plot(x, y_sol, 'r-', linewidth=2, alpha=0.8)
        plt.plot(x[0], y0, 'ro', markersize=8)  # Initial condition
    
    plt.xlabel('x')
    plt.ylabel('y')
    plt.title('Direction Field with Solution Curves')
    plt.legend(['Direction Field', 'Solution Curves', 'Initial Conditions'])
    plt.grid(True, alpha=0.3)

# Example with solution curves
initial_conditions = [-2, -1, 0, 1, 2]
plot_solution_curves(f_example, (-2, 2), (-3, 3), initial_conditions)
plt.show()</code></pre>
                        </div>
                        <div class="language-panel" data-lang="r">
<pre><code class="language-r">library(ggplot2)
library(deSolve)

# Function to create direction field
direction_field <- function(f, x_range, y_range, density = 20) {
  # Create grid
  x <- seq(x_range[1], x_range[2], length.out = density)
  y <- seq(y_range[1], y_range[2], length.out = density)
  grid <- expand.grid(x = x, y = y)
  
  # Calculate slopes
  grid$dx <- 1
  grid$dy <- f(grid$x, grid$y)
  
  # Normalize for better visualization
  magnitude <- sqrt(grid$dx^2 + grid$dy^2)
  grid$dx_norm <- grid$dx / magnitude * 0.1
  grid$dy_norm <- grid$dy / magnitude * 0.1
  
  # Create plot
  p <- ggplot(grid, aes(x = x, y = y)) +
    geom_segment(aes(xend = x + dx_norm, yend = y + dy_norm),
                arrow = arrow(length = unit(0.1, "cm")),
                color = "blue", alpha = 0.6) +
    labs(title = "Direction Field for dy/dx = f(x,y)",
         x = "x", y = "y") +
    theme_minimal() +
    coord_equal()
  
  return(p)
}

# Example function: dy/dx = x + y
f_example <- function(x, y) {
  return(x + y)
}

# Create direction field
p1 <- direction_field(f_example, c(-3, 3), c(-3, 3))
print(p1)

# Function to add solution curves
plot_with_solutions <- function(f, x_range, y_range, initial_conditions) {
  # Create direction field
  p <- direction_field(f, x_range, y_range, density = 15)
  
  # Add solution curves using numerical integration
  solution_data <- data.frame()
  
  for (i in seq_along(initial_conditions)) {
    y0 <- initial_conditions[i]
    
    # Define ODE function for deSolve
    ode_func <- function(t, y, parms) {
      list(f(t, y))
    }
    
    # Solve ODE
    times <- seq(x_range[1], x_range[2], length.out = 100)
    sol <- ode(y = y0, times = times, func = ode_func, parms = NULL)
    
    # Add to data frame
    temp_df <- data.frame(
      x = sol[, 1],
      y = sol[, 2],
      curve = as.factor(i)
    )
    solution_data <- rbind(solution_data, temp_df)
  }
  
  # Add solution curves to plot
  p <- p + 
    geom_line(data = solution_data, 
              aes(x = x, y = y, group = curve, color = curve),
              size = 1.2, alpha = 0.8) +
    geom_point(data = data.frame(x = x_range[1], 
                                y = initial_conditions),
               aes(x = x, y = y), 
               color = "red", size = 3) +
    labs(title = "Direction Field with Solution Curves") +
    guides(color = guide_legend(title = "Initial Condition"))
  
  return(p)
}

# Example with solution curves
initial_conditions <- c(-2, -1, 0, 1, 2)
p2 <- plot_with_solutions(f_example, c(-2, 2), c(-3, 3), initial_conditions)
print(p2)</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="theorem-box p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold mb-3 text-green-600">Reading Direction Fields</h3>
                <ul class="list-disc pl-6 space-y-2">
                    <li><strong>Arrows point in the direction of increasing solutions</strong></li>
                    <li><strong>Steep arrows indicate rapid changes in y</strong></li>
                    <li><strong>Horizontal arrows show where dy/dx = 0 (critical points)</strong></li>
                    <li><strong>Solution curves are always tangent to the arrows</strong></li>
                    <li><strong>Parallel arrows indicate regions with similar behavior</strong></li>
                </ul>
            </div>

            <div class="example-box p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">Isoclines: Another Visualization Tool</h3>
                <p class="mb-4">
                    <strong>Isoclines</strong> are curves along which the slope $\frac{dy}{dx}$ is constant. 
                    For the equation $\frac{dy}{dx} = f(x,y)$, the isocline for slope $m$ is the curve $f(x,y) = m$.
                </p>
                <div class="bg-white p-4 rounded border-l-4 border-yellow-300">
                    <p class="mb-2">
                        <strong>Example:</strong> For $\frac{dy}{dx} = x + y$
                    </p>
                    <p class="mb-2">
                        The isocline for slope $m$ is: $x + y = m$, or $y = m - x$
                    </p>
                    <p>
                        These are straight lines with slope -1, each corresponding to a different value of $m$.
                    </p>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.8: Practical Examples -->
        <section id="practical-examples" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-cogs mr-3 text-blue-600"></i>
                6.8 Practical Examples
            </h2>

            <p class="text-lg mb-6">
                Let's explore how differential equations naturally arise in various fields. Understanding these 
                applications helps motivate the mathematical techniques we'll learn.
            </p>

            <!-- Physics Examples -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">
                    <i class="fas fa-atom mr-2 text-blue-600"></i>
                    Physics Applications
                </h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="example-box p-6 rounded-lg">
                        <h4 class="font-semibold mb-3 text-orange-600">Newton's Second Law</h4>
                        <p class="mb-3 text-sm">
                            A mass $m$ subject to force $F(t)$ follows Newton's second law:
                        </p>
                        <div class="math-display">
                            $$m\frac{d^2x}{dt^2} = F(t)$$
                        </div>
                        <p class="text-sm mt-3">
                            <strong>Special case:</strong> Free fall with air resistance proportional to velocity:
                        </p>
                        <div class="math-display">
                            $$m\frac{dv}{dt} = mg - kv$$
                        </div>
                        <p class="text-sm">
                            This is a first-order linear ODE in velocity $v(t)$.
                        </p>
                    </div>
                    
                    <div class="example-box p-6 rounded-lg">
                        <h4 class="font-semibold mb-3 text-orange-600">Simple Harmonic Motion</h4>
                        <p class="mb-3 text-sm">
                            A mass on a spring with spring constant $k$:
                        </p>
                        <div class="math-display">
                            $$m\frac{d^2x}{dt^2} + kx = 0$$
                        </div>
                        <p class="text-sm mt-3">
                            <strong>With damping:</strong> Adding friction proportional to velocity:
                        </p>
                        <div class="math-display">
                            $$m\frac{d^2x}{dt^2} + c\frac{dx}{dt} + kx = 0$$
                        </div>
                        <p class="text-sm">
                            This second-order linear ODE describes oscillatory motion.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Biology Examples -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">
                    <i class="fas fa-dna mr-2 text-green-600"></i>
                    Biology Applications
                </h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="example-box p-6 rounded-lg">
                        <h4 class="font-semibold mb-3 text-green-600">Population Growth</h4>
                        <p class="mb-3 text-sm">
                            <strong>Exponential Growth:</strong> Rate proportional to population
                        </p>
                        <div class="math-display">
                            $$\frac{dP}{dt} = rP$$
                        </div>
                        <p class="mb-3 text-sm">
                            <strong>Logistic Growth:</strong> Growth limited by carrying capacity
                        </p>
                        <div class="math-display">
                            $$\frac{dP}{dt} = rP\left(1 - \frac{P}{K}\right)$$
                        </div>
                        <p class="text-sm">
                            Where $K$ is the carrying capacity and $r$ is the intrinsic growth rate.
                        </p>
                    </div>
                    
                    <div class="example-box p-6 rounded-lg">
                        <h4 class="font-semibold mb-3 text-green-600">Predator-Prey Model</h4>
                        <p class="mb-3 text-sm">
                            The Lotka-Volterra equations describe interacting populations:
                        </p>
                        <div class="math-display">
                            $$\begin{align}
                            \frac{dx}{dt} &= ax - bxy \\
                            \frac{dy}{dt} &= -cy + dxy
                            \end{align}$$
                        </div>
                        <p class="text-sm">
                            Where $x$ = prey population, $y$ = predator population, and $a,b,c,d > 0$ are parameters.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Engineering Examples -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">
                    <i class="fas fa-microchip mr-2 text-purple-600"></i>
                    Engineering Applications
                </h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="example-box p-6 rounded-lg">
                        <h4 class="font-semibold mb-3 text-purple-600">RC Circuit</h4>
                        <p class="mb-3 text-sm">
                            For a resistor-capacitor circuit with applied voltage $V(t)$:
                       
                        </p>
                        <div class="math-display">
                            $$RC\frac{dV_C}{dt} + V_C = V(t)$$
                        </div>
                        <p class="text-sm mt-3">
                            <strong>Charging with constant voltage:</strong> $V(t) = V_0$
                        </p>
                        <div class="math-display">
                            $$RC\frac{dV_C}{dt} + V_C = V_0$$
                        </div>
                        <p class="text-sm">
                            This first-order linear ODE describes capacitor voltage over time.
                        </p>
                    </div>
                    
                    <div class="example-box p-6 rounded-lg">
                        <h4 class="font-semibold mb-3 text-purple-600">Heat Transfer</h4>
                        <p class="mb-3 text-sm">
                            Newton's law of cooling for an object cooling in ambient temperature $T_a$:
                        </p>
                        <div class="math-display">
                            $$\frac{dT}{dt} = -k(T - T_a)$$
                        </div>
                        <p class="text-sm mt-3">
                            <strong>More general:</strong> With internal heat generation $q(t)$:
                        </p>
                        <div class="math-display">
                            $$mc\frac{dT}{dt} = q(t) - hA(T - T_a)$$
                        </div>
                        <p class="text-sm">
                            Where $m$ = mass, $c$ = specific heat, $h$ = heat transfer coefficient, $A$ = surface area.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Economics Example -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">
                    <i class="fas fa-chart-line mr-2 text-indigo-600"></i>
                    Economics Applications
                </h3>
                
                <div class="example-box p-6 rounded-lg">
                    <h4 class="font-semibold mb-3 text-indigo-600">Investment Growth with Continuous Contributions</h4>
                    <p class="mb-3 text-sm">
                        An investment account with continuous contributions at rate $D$ and compound interest rate $r$:
                    </p>
                    <div class="math-display">
                        $$\frac{dA}{dt} = rA + D$$
                    </div>
                    <p class="text-sm mt-3">
                        <strong>Interpretation:</strong>
                    </p>
                    <ul class="text-sm list-disc pl-6 space-y-1">
                        <li>$rA$ represents interest earned on current balance</li>
                        <li>$D$ represents constant deposit rate</li>
                        <li>$A(t)$ is the account balance at time $t$</li>
                    </ul>
                </div>
            </div>

            <!-- Interactive Example -->
            <div class="interactive-demo">
                <h4 class="font-semibold mb-4">Interactive: Comparing Growth Models</h4>
                <p class="mb-4">
                    Compare exponential growth $\frac{dP}{dt} = rP$ vs logistic growth $\frac{dP}{dt} = rP(1 - P/K)$:
                </p>
                
                <div class="grid md:grid-cols-2 gap-4 mb-4">
                    <div class="slider-container">
                        <label class="block text-sm font-medium mb-2">Growth rate $r$: <span id="r-value">0.1</span></label>
                        <input type="range" id="r-slider" class="slider" min="0.01" max="0.5" step="0.01" value="0.1">
                    </div>
                    <div class="slider-container">
                        <label class="block text-sm font-medium mb-2">Carrying capacity $K$: <span id="k-value">100</span></label>
                        <input type="range" id="k-slider" class="slider" min="50" max="200" step="10" value="100">
                    </div>
                </div>
                
                <div class="chart-container">
                    <canvas id="growthComparisonChart"></canvas>
                </div>
                
                <p class="text-sm mt-4 text-gray-600">
                    <strong>Observe:</strong> Exponential growth increases without bound, while logistic growth 
                    approaches the carrying capacity. Real populations often follow logistic growth.
                </p>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.9: Verification of Solutions -->
        <section id="verification" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-check-double mr-3 text-blue-600"></i>
                6.9 Verification of Solutions
            </h2>

            <p class="text-lg mb-6">
                Before we learn to solve differential equations, it's important to understand how to verify that 
                a proposed function is indeed a solution. This skill is crucial for checking our work.
            </p>

            <div class="definition-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-semibold mb-3">Verification Process</h3>
                <p class="mb-4">
                    To verify that $y = \phi(x)$ is a solution to a differential equation:
                </p>
                <ol class="list-decimal pl-6 space-y-2">
                    <li>Compute the required derivatives of $\phi(x)$</li>
                    <li>Substitute $y = \phi(x)$ and its derivatives into the differential equation</li>
                    <li>Simplify to check if the equation is satisfied identically</li>
                    <li>Check that any initial or boundary conditions are satisfied</li>
                </ol>
            </div>

            <div class="example-box p-6 rounded-lg mb-8">
                <h3 class="text-lg font-semibold mb-4">Example 1: First-Order Verification</h3>
                <div class="bg-white p-4 rounded border-l-4 border-yellow-300">
                    <p class="font-semibold mb-2">Given:</p>
                    <p class="mb-2">Differential equation: $\frac{dy}{dx} = 2x$</p>
                    <p class="mb-2">Proposed solution: $y = x^2 + 5$</p>
                    <p class="mb-2">Initial condition: $y(0) = 5$</p>
                </div>
                
                <div class="mt-4 space-y-4">
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <p class="font-semibold">Step 1: Compute derivative</p>
                        <p class="font-mono">$\frac{dy}{dx} = \frac{d}{dx}(x^2 + 5) = 2x$</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <p class="font-semibold">Step 2: Substitute into DE</p>
                        <p>Left side: $\frac{dy}{dx} = 2x$</p>
                        <p>Right side: $2x$</p>
                        <p>Since $2x = 2x$, the DE is satisfied âœ“</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <p class="font-semibold">Step 3: Check initial condition</p>
                        <p>$y(0) = 0^2 + 5 = 5$ âœ“</p>
                    </div>
                    
                    <p class="font-semibold text-green-600">
                        Conclusion: $y = x^2 + 5$ is indeed a solution to the IVP.
                    </p>
                </div>
            </div>

            <div class="example-box p-6 rounded-lg mb-8">
                <h3 class="text-lg font-semibold mb-4">Example 2: Second-Order Verification</h3>
                <div class="bg-white p-4 rounded border-l-4 border-yellow-300">
                    <p class="font-semibold mb-2">Given:</p>
                    <p class="mb-2">Differential equation: $y'' + 4y = 0$</p>
                    <p class="mb-2">Proposed solution: $y = 3\cos(2x) + 2\sin(2x)$</p>
                </div>
                
                <div class="mt-4 space-y-4">
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <p class="font-semibold">Step 1: Compute derivatives</p>
                        <p class="font-mono">$y = 3\cos(2x) + 2\sin(2x)$</p>
                        <p class="font-mono">$y' = -6\sin(2x) + 4\cos(2x)$</p>
                        <p class="font-mono">$y'' = -12\cos(2x) - 8\sin(2x)$</p>
                    </div>
                    
                    <div class="bg-white p-4 rounded border-l-4 border-green-300">
                        <p class="font-semibold">Step 2: Substitute into DE</p>
                        <p>$y'' + 4y = [-12\cos(2x) - 8\sin(2x)] + 4[3\cos(2x) + 2\sin(2x)]$</p>
                        <p>$= -12\cos(2x) - 8\sin(2x) + 12\cos(2x) + 8\sin(2x)$</p>
                        <p>$= 0$ âœ“</p>
                    </div>
                    
                    <p class="font-semibold text-green-600">
                        Conclusion: The proposed function is a solution.
                    </p>
                </div>
            </div>

            <!-- Code for Verification -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Computational Verification</h3>
                
                <div class="code-block">
                    <div class="code-header">
                        <span class="text-sm">Solution Verification Tools</span>
                        <div class="language-tabs">
                            <span class="language-tab active" data-lang="python">Python</span>
                            <span class="language-tab" data-lang="r">R</span>
                        </div>
                    </div>
                    <div class="language-content">
                        <div class="language-panel active" data-lang="python">
<pre><code class="language-python">import sympy as sp
import numpy as np
import matplotlib.pyplot as plt

def verify_solution(de_expr, solution_expr, var, func):
    """
    Verify if a function is a solution to a differential equation
    
    Parameters:
    de_expr: differential equation expression (should equal 0)
    solution_expr: proposed solution
    var: independent variable (usually x or t)
    func: dependent variable function (usually y)
    """
    
    # Substitute the solution into the DE
    # First, get the derivatives needed
    derivatives = {}
    
    # Find the highest derivative order in the DE
    max_order = 0
    for atom in de_expr.atoms():
        if atom.has(sp.Derivative):
            order = len(atom.args[1:])  # Number of derivatives
            max_order = max(max_order, order)
    
    # Compute derivatives of the solution
    current_expr = solution_expr
    derivatives[func] = solution_expr
    
    for i in range(1, max_order + 1):
        current_expr = sp.diff(current_expr, var)
        derivatives[sp.Derivative(func, var, i)] = current_expr
    
    # Substitute into the differential equation
    result = de_expr.subs(derivatives)
    
    # Simplify
    simplified = sp.simplify(result)
    
    print(f"Original DE: {de_expr} = 0")
    print(f"Proposed solution: {func} = {solution_expr}")
    print(f"After substitution: {result}")
    print(f"Simplified: {simplified}")
    
    if simplified == 0:
        print("âœ“ VERIFIED: The function is a solution!")
        return True
    else:
        print("âœ— NOT VERIFIED: The function is not a solution.")
        return False

# Example 1: First-order ODE
x = sp.Symbol('x')
y = sp.Function('y')

# DE: dy/dx = 2x
de1 = sp.Derivative(y(x), x) - 2*x

# Proposed solution: y = x^2 + C
solution1 = x**2 + 5

print("Example 1:")
verify_solution(de1, solution1, x, y(x))
print("\n" + "="*50 + "\n")

# Example 2: Second-order ODE
# DE: y'' + 4y = 0
de2 = sp.Derivative(y(x), x, 2) + 4*y(x)

# Proposed solution: y = 3*cos(2x) + 2*sin(2x)
solution2 = 3*sp.cos(2*x) + 2*sp.sin(2*x);

print("Example 2:")
verify_solution(de2, solution2, x, y(x))

# Verify initial conditions
def verify_initial_conditions(solution_expr, var, conditions):
    """
    Verify initial conditions for a solution
    
    Parameters:
    solution_expr: the solution function
    var: independent variable
    conditions: list of tuples (derivative_order, point, value)
    """
    print("\nVerifying initial conditions:")
    
    current_expr = solution_expr
    all_satisfied = True
    
    for order, point, expected_value in conditions:
        if order == 0:
            # Function value
            actual_value = current_expr.subs(var, point)
            condition_expr = f"y({point}) = {expected_value}"
        else:
            # Derivative value
            deriv_expr = sp.diff(solution_expr, var, order)
            actual_value = deriv_expr.subs(var, point)
            condition_expr = f"y{'′' * order}({point}) = {expected_value}"
        
        print(f"{condition_expr}: {actual_value} = {expected_value}")
        
        if sp.simplify(actual_value - expected_value) != 0:
            print("âœ— Condition not satisfied")
            all_satisfied = False
        else:
            print("âœ“ Condition satisfied")
    
    return all_satisfied

# Example: Verify y = cos(x) satisfies y'' + y = 0 with y(0) = 1, y'(0) = 0
solution3 = sp.cos(x)
de3 = sp.Derivative(y(x), x, 2) + y(x)

print("\n" + "="*50)
print("Example 3: Complete verification")
verify_solution(de3, solution3, x, y(x))

# Check initial conditions
conditions = [(0, 0, 1), (1, 0, 0)]  # y(0) = 1, y'(0) = 0
verify_initial_conditions(solution3, x, conditions)

# Plotting verification
plot_verification <- function(solution_expr, x_range = c(-2*pi, 2*pi)) {
  library(ggplot2)
  
  # Create x values
  x_vals <- seq(x_range[1], x_range[2], length.out = 1000)
  
  # Evaluate solution
  solution_func <- function(x) eval(parse(text = solution_expr))
  y_vals <- sapply(x_vals, solution_func)
  
  # Create data frame
  df <- data.frame(x = x_vals, y = y_vals)
  
  # Plot
  p <- ggplot(df, aes(x = x, y = y)) +
    geom_line(color = "blue", size = 1.2) +
    labs(title = paste("Solution:", solution_expr),
         x = "x", y = "y") +
    theme_minimal() +
    geom_hline(yintercept = 0, color = "gray", linetype = "dashed") +
    geom_vline(xintercept = 0, color = "gray", linetype = "dashed")
  
  return(p)
}

# Plot the solution
p <- plot_verification("cos(x)")
print(p)</code></pre>
                        </div>
                        <div class="language-panel" data-lang="r">
<pre><code class="language-r">library(Ryacas)
library(pracma)

# Function to verify solution symbolically
verify_solution <- function(de_expr, solution_expr, var = "x") {
  # This is a simplified version for R
  # For more complex verification, numerical methods are often used
  
  cat("Differential Equation:", de_expr, "\n")
  cat("Proposed Solution:", solution_expr, "\n")
  
  # For demonstration, we'll use numerical verification
  # Create a function from the solution expression
  solution_func <- function(x) eval(parse(text = solution_expr))
  
  # Numerical derivatives
  first_deriv <- function(x) pracma::fderiv(solution_func, x, n = 1)
  second_deriv <- function(x) pracma::fderiv(solution_func, x, n = 2)
  
  # Test at several points
  test_points <- seq(-2, 2, by = 0.5)
  
  cat("\nNumerical verification at test points:\n")
  for (x_val in test_points) {
    y_val <- solution_func(x_val)
    y_prime <- first_deriv(x_val)
    y_double_prime <- second_deriv(x_val)
    
    # Example: For y'' + 4y = 0
    lhs <- y_double_prime + 4 * y_val
    
    cat(sprintf("x = %.1f: y'' + 4y = %.6f\n", x_val, lhs))
  }
}

# Example 1: Verify y = cos(2x) for y'' + 4y = 0
cat("Example 1: Second-order ODE\n")
cat("=============================\n")
verify_solution("y'' + 4*y = 0", "cos(2*x)")

# Function to verify initial conditions
verify_initial_conditions <- function(solution_expr, conditions) {
  solution_func <- function(x) eval(parse(text = solution_expr))
  
  cat("\nVerifying initial conditions:\n")
  
  for (i in 1:nrow(conditions)) {
    order <- conditions[i, "order"]
    point <- conditions[i, "point"]
    expected <- conditions[i, "expected"]
    
    if (order == 0) {
      actual <- solution_func(point)
      cat(sprintf("y(%.1f) = %.6f, expected = %.1f\n", 
                  point, actual, expected))
    } else if (order == 1) {
      actual <- pracma::fderiv(solution_func, point, n = 1)
      cat(sprintf("y'(%.1f) = %.6f, expected = %.1f\n", 
                  point, actual, expected))
    } else if (order == 2) {
      actual <- pracma::fderiv(solution_func, point, n = 2)
      cat(sprintf("y''(%.1f) = %.6f, expected = %.1f\n", 
                  point, actual, expected))
    }
    
    if (abs(actual - expected) < 1e-10) {
      cat("âœ“ Condition satisfied\n")
    } else {
      cat("âœ— Condition NOT satisfied\n")
    }
  }
}

# Example: Verify y = cos(x) with initial conditions
cat("\n\nExample 2: With initial conditions\n")
cat("===================================\n")

# Define initial conditions
conditions <- data.frame(
  order = c(0, 1),
  point = c(0, 0),
  expected = c(1, 0)
)

verify_initial_conditions("cos(x)", conditions)

# Plotting verification
plot_verification <- function(solution_expr, x_range = c(-2*pi, 2*pi)) {
  library(ggplot2)
  
  # Create x values
  x_vals <- seq(x_range[1], x_range[2], length.out = 1000)
  
  # Evaluate solution
  solution_func <- function(x) eval(parse(text = solution_expr))
  y_vals <- sapply(x_vals, solution_func)
  
  # Create data frame
  df <- data.frame(x = x_vals, y = y_vals)
  
  # Plot
  p <- ggplot(df, aes(x = x, y = y)) +
    geom_line(color = "blue", size = 1.2) +
    labs(title = paste("Solution:", solution_expr),
         x = "x", y = "y") +
    theme_minimal() +
    geom_hline(yintercept = 0, color = "gray", linetype = "dashed") +
    geom_vline(xintercept = 0, color = "gray", linetype = "dashed")
  
  return(p)
}

# Plot the solution
p <- plot_verification("cos(x)")
print(p)</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="theorem-box p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-3 text-green-600">Verification Checklist</h3>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-medium mb-2">For the Differential Equation:</h4>
                        <ul class="list-disc pl-4 text-sm space-y-1">
                            <li>âœ“ Compute all required derivatives</li>
                            <li>âœ“ Substitute into the equation</li>
                            <li>âœ“ Simplify the result</li>
                            <li>âœ“ Check if the result equals zero (or the right-hand side)</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium mb-2">For Initial/Boundary Conditions:</h4>
                        <ul class="list-disc pl-4 text-sm space-y-1">
                            <li>âœ“ Evaluate the solution at specified points</li>
                            <li>âœ“ Evaluate derivatives at specified points</li>
                            <li>âœ“ Compare with given condition values</li>
                            <li>âœ“ Ensure all conditions are satisfied</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <div class="section-divider"></div>

        <!-- Section 6.10: Exercises and Projects -->
        <section id="exercises" class="mb-12">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-tasks mr-3 text-blue-600"></i>
                6.10 Exercises and Projects
            </h2>

            <div class="grid md:grid-cols-1 gap-6 mb-8">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-blue-700">Practice Problems</h3>
                    
                    <div class="space-y-6">
                        <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                            <h4 class="font-semibold mb-2">Problem 1: Classification</h4>
                            <p class="mb-2">Classify each differential equation by order and linearity:</p>
                            <div class="font-mono text-sm space-y-1 ml-4">
                                <p>a) $\frac{dy}{dx} + 3y = x^2$</p>
                                <p>b) $y'' + y'y = 0$</p>
                                <p>c) $\frac{d^3y}{dx^3} + x\frac{dy}{dx} + y = \sin(x)$</p>
                                <p>d) $\left(\frac{dy}{dx}\right)^2 + y^2 = 1$</p>
                            </div>
                        </div>
                        
                        <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                            <h4 class="font-semibold mb-2">Problem 2: Solution Verification</h4>
                            <p class="mb-2">Verify that the given functions are solutions to the differential equations:</p>
                            <div class="font-mono text-sm space-y-1 ml-4">
                                <p>a) $y = Ce^{-2x}$ solves $\frac{dy}{dx} + 2y = 0$</p>
                                <p>b) $y = x^2 + 2x + 3$ solves $\frac{dy}{dx} = 2x + 2$</p>
                                <p>c) $y = A\sin(3x) + B\cos(3x)$ solves $y'' + 9y = 0$</p>
                            </div>
                        </div>
                        
                        <div class="bg-white p-4 rounded border-l-4 border-blue-300">
                            <h4 class="font-semibold mb-2">Problem 3: Initial Value Problems</h4>
                            <p class="mb-2">For the general solution $y = C_1e^x + C_2e^{-x}$ of $y'' - y = 0$, find the particular solution satisfying:</p>
                            <div class="font-mono text-sm space-y-1 ml-4">
                                <p>a) $y(0) = 1$, $y'(0) = 0$</p>
                                <p>b) $y(0) = 0$, $y'(0) = 1$</p>
                                <p>c) $y(0) = 2$, $y'(0) = -1$</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-1 gap-6 mb-8">
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-green-700">Computational Exercises</h3>
                    
                    <div class="space-y-6">
                        <div class="bg-white p-4 rounded border-l-4 border-green-300">
                            <h4 class="font-semibold mb-2">Exercise 1: Direction Fields</h4>
                            <p class="mb-2">Create direction fields for the following differential equations:</p>
                            <div class="font-mono text-sm space-y-1 ml-4">
                                <p>a) $\frac{dy}{dx} = x - y$</p>
                                <p>b) $\frac{dy}{dx} = xy$</p>
                                <p>c) $\frac{dy}{dx} = \sin(x) + \cos(y)$</p>
                            </div>
                            <p class="text-sm mt-2">
                                Use both Python and R to create the visualizations. Compare the direction fields 
                                and describe what they tell you about solution behavior.
                            </p>
                        </div>
                        
                        <div class="bg-white p-4 rounded border-l-4 border-green-300">
                            <h4 class="font-semibold mb-2">Exercise 2: Solution Families</h4>
                            <p class="mb-2">
                                For the differential equation $\frac{dy}{dx} = -\frac{x}{y}$:
                            </p>
                            <ul class="list-disc pl-6 text-sm space-y-1">
                                <li>Show that $x^2 + y^2 = C$ is a family of solutions</li>
                                <li>Plot several members of this family for different values of $C$</li>
                                <li>Create a direction field and overlay the solution curves</li>
                                <li>Describe the geometric meaning of these solutions</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-1 gap-6 mb-8">
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-purple-700">Modeling Projects</h3>
                    
                    <div class="space-y-6">
                        <div class="bg-white p-4 rounded border-l-4 border-purple-300">
                            <h4 class="font-semibold mb-2">Project 1: Population Dynamics</h4>
                            <p class="mb-3 text-sm">
                                Research the population growth of a species of your choice. Create mathematical models using:
                            </p>
                            <ul class="list-disc pl-6 text-sm space-y-1">
                                <li>Exponential growth model: $\frac{dP}{dt} = rP$</li>
                                <li>Logistic growth model: $\frac{dP}{dt} = rP(1 - P/K)$</li>
                            </ul>
                            <p class="text-sm mt-3">
                                <strong>Deliverables:</strong> Direction fields, parameter estimation from data, 
                                comparison of models, and discussion of which model better fits reality.
                            </p>
                        </div>
                        
                        <div class="bg-white p-4 rounded border-l-4 border-purple-300">
                            <h4 class="font-semibold mb-2">Project 2: Cooling/Heating Analysis</h4>
                            <p class="mb-3 text-sm">
                                Design an experiment to test Newton's law of cooling. Measure the temperature 
                                of a hot object cooling in room air.
                            </p>
                            <ul class="list-disc pl-6 text-sm space-y-1">
                                <li>Collect temperature data over time</li>
                                <li>Fit the model $\frac{dT}{dt} = -k(T - T_{\text{room}})$</li>
                                <li>Estimate the cooling constant $k$</li>
                                <li>Validate the model against your data</li>
                            </ul>
                            <p class="text-sm mt-3">
                                <strong>Extensions:</strong> Compare cooling rates for different materials, 
                                sizes, or environmental conditions.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="theorem-box p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold mb-3 text-green-600">Chapter 6 Summary</h3>
                <p class="mb-4">In this chapter, you've learned the fundamental concepts of differential equations:</p>
                <div class="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <h4 class="font-medium mb-2">Key Concepts:</h4>
                        <ul class="list-disc pl-4 space-y-1">
                            <li>Definition and terminology</li>
                            <li>Classification by order and linearity</li>
                            <li>Solution concepts (general, particular, singular)</li>
                            <li>Initial vs boundary value problems</li>
                            <li>Existence and uniqueness theorems</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium mb-2">Skills Developed:</h4>
                        <ul class="list-disc pl-4 space-y-1">
                            <li>Classifying differential equations</li>
                            <li>Verifying solutions</li>
                            <li>Creating direction fields</li>
                            <li>Identifying real-world applications</li>
                            <li>Using computational tools</li>
                        </ul>
                    </div>
                </div>
                <p class="mt-4 text-sm bg-white p-3 rounded border-l-4 border-green-300">
                    <strong>Next:</strong> In the following chapters, we'll learn systematic methods for solving 
                    specific types of differential equations, starting with separable equations.
                </p>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-3 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3">Chapter 6: What is a Differential Equation?</h3>
                    <p class="text-gray-300 text-sm">
                        Foundation concepts for understanding differential equations, their classification, 
                        and solution methods.
                    </p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-3">Learning Resources</h3>
                    <ul class="text-gray-300 text-sm space-y-1">                        <li>• Interactive visualizations</li>
                        <li>• Python and R code examples</li>
                        <li>• Real-world applications</li>
                        <li>• Practice problems</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-3">Coming Next</h3>
                    <ul class="text-gray-300 text-sm space-y-1">                        <li>• Chapter 7: Separable Equations</li>
                        <li>• Chapter 8: Linear First-Order Equations</li>
                        <li>• Chapter 9: Exact Equations</li>
                        <li>• Chapter 10: Modeling Applications</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-6 text-center text-gray-400 text-sm">
                <p>&copy; 2024 ODE Tutorial Series. Educational content for learning differential equations.</p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Language tab switching
            document.querySelectorAll('.language-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const lang = tab.dataset.lang;
                    const container = tab.closest('.code-block');
                    
                    // Update active tab
                    container.querySelectorAll('.language-tab').forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    // Update active panel
                    container.querySelectorAll('.language-panel').forEach(panel => {
                        panel.classList.remove('active');
                        if (panel.dataset.lang === lang) {
                            panel.classList.add('active');
                        }
                    });
                });
            });

            // Initialize charts
            initializeCharts();
            
            // Add smooth scrolling for table of contents
            document.querySelectorAll('.toc a').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });        function initializeCharts() {
            try {
                // General Solution Chart
                const ctx1 = document.getElementById('generalSolutionChart');
                if (!ctx1) {
                    console.warn('generalSolutionChart canvas not found');
                    return;
                }
                const c1Slider = document.getElementById('c1-slider');
                const c2Slider = document.getElementById('c2-slider');
                const c1Value = document.getElementById('c1-value');
                const c2Value = document.getElementById('c2-value');

                if (!c1Slider || !c2Slider || !c1Value || !c2Value) {
                    console.warn('General solution sliders not found');
                    return;
                }

                let generalChart = new Chart(ctx1.getContext('2d'), {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'y = Câ‚cos(x) + Câ‚‚sin(x)',
                        data: [],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: 'x'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'y'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            function updateGeneralSolution() {
                const c1 = parseFloat(c1Slider.value);
                const c2 = parseFloat(c2Slider.value);
                c1Value.textContent = c1.toFixed(1);
                c2Value.textContent = c2.toFixed(1);

                const x = [];
                const y = [];
                for (let i = 0; i <= 100; i++) {
                    const xi = -2 * Math.PI + (4 * Math.PI * i) / 100;
                    const yi = c1 * Math.cos(xi) + c2 * Math.sin(xi);
                    x.push(xi);
                    y.push(yi);
                }

                generalChart.data.labels = x;
                generalChart.data.datasets[0].data = y;
                generalChart.data.datasets[0].label = `y = ${c1.toFixed(1)}cos(x) + ${c2.toFixed(1)}sin(x)`;
                generalChart.update();
            }

            c1Slider.addEventListener('input', updateGeneralSolution);
            c2Slider.addEventListener('input', updateGeneralSolution);
            updateGeneralSolution();

            // Non-unique solutions chart
            const ctx2 = document.getElementById('nonUniqueChart').getContext('2d');
            const aSlider = document.getElementById('a-slider');
            const aValue = document.getElementById('a-value');

            let nonUniqueChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'y = 0',
                            data: [],
                            borderColor: 'rgb(239, 68, 68)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 3,
                            fill: false
                        },
                        {
                            label: 'y = (x-a)²/4 for x > a',
                            data: [],
                            borderColor: 'rgb(16, 185, 129)',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: 'x'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'y'
                            },
                            min: 0,
                            max: 2
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            function updateNonUnique() {
                const a = parseFloat(aSlider.value);
                aValue.textContent = a.toFixed(1);

                const x = [];
                const y1 = [];
                const y2 = [];

                for (let i = 0; i <= 100; i++) {
                    const xi = 5 * i / 100;
                    x.push(xi);
                    y1.push(0);
                    
                    if (xi <= a) {
                        y2.push(0);
                    } else {
                        y2.push(Math.pow(xi - a, 2) / 4);
                    }
                }

                nonUniqueChart.data.labels = x;
                nonUniqueChart.data.datasets[0].data = y1;
                nonUniqueChart.data.datasets[1].data = y2;
                nonUniqueChart.data.datasets[1].label = `y = (x-${a.toFixed(1)})²/4 for x > ${a.toFixed(1)}`;
                nonUniqueChart.update();
            }

            aSlider.addEventListener('input', updateNonUnique);
            updateNonUnique();

            // Direction field chart
            const ctx3 = document.getElementById('directionFieldChart').getContext('2d');
            const equationSelect = document.getElementById('equation-select');

            let directionChart = new Chart(ctx3, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Direction Field',
                        data: [],
                        backgroundColor: 'rgba(59, 130, 246, 0.6)',
                        borderColor: 'rgb(59, 130, 246)',
                        pointRadius: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            min: -3,
                            max: 3,
                            title: {
                                display: true,
                                text: 'x'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'y'
                            },
                            min: -3,
                            max: 3
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            function updateDirectionField() {
                const equation = equationSelect.value;
                const data = [];
                
                // Generate direction field data
                for (let x = -3; x <= 3; x += 0.3) {
                    for (let y = -3; y <= 3; y += 0.3) {
                        let slope;
                        switch(equation) {
                            case 'xy':
                                slope = x + y;
                                break;
                            case 'x':
                                slope = x;
                                break;
                            case 'y':
                                slope = y;
                                break;
                            case 'sinx':
                                slope = Math.sin(x);
                                break;
                            case 'xy2':
                                slope = x - y;
                                break;
                            default:
                                slope = 0;
                        }
                        
                        // Create small line segments to represent direction
                        const length = 0.1;
                        const angle = Math.atan(slope);
                        const dx = length * Math.cos(angle);
                        const dy = length * Math.sin(angle);
                        
                        data.push({x: x - dx, y: y - dy});
                        data.push({x: x + dx, y: y + dy});
                    }
                }
                
                directionChart.data.datasets[0].data = data;
                directionChart.data.datasets[0].label = `Direction Field`;
                directionChart.update();
            }

            equationSelect.addEventListener('change', updateDirectionField);
            updateDirectionField();

            // Growth comparison chart
            const ctx4 = document.getElementById('growthComparisonChart').getContext('2d');
            const rSlider = document.getElementById('r-slider');
            const kSlider = document.getElementById('k-slider');
            const rValue = document.getElementById('r-value');
            const kValue = document.getElementById('k-value');

            let growthChart = new Chart(ctx4, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'Exponential Growth',
                            data: [],
                            borderColor: 'rgb(239, 68, 68)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 3,
                            fill: false
                        },
                        {
                            label: 'Logistic Growth',
                            data: [],
                            borderColor: 'rgb(16, 185, 129)',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: 'Time (t)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Population (P)'
                            },
                            min: 0
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });

            function updateGrowthComparison() {
                const r = parseFloat(rSlider.value);
                const K = parseFloat(kSlider.value);
                rValue.textContent = r.toFixed(2);
                kValue.textContent = K.toFixed(0);

                const t = [];
                const exponential = [];
                const logistic = [];
                const P0 = 10; // Initial population

                for (let i = 0; i <= 50; i++) {
                    const time = i;
                    t.push(time);
                    
                    // Exponential growth: P = P0 * e^(rt)
                    exponential.push(P0 * Math.exp(r * time));
                    
                    // Logistic growth: P = K / (1 + ((K-P0)/P0) * e^(-rt))
                    const logisticValue = K / (1 + ((K - P0) / P0) * Math.exp(-r * time));
                    logistic.push(logisticValue);
                }

                growthChart.data.labels = t;
                growthChart.data.datasets[0].data = exponential;
                growthChart.data.datasets[1].data = logistic;
                growthChart.data.datasets[0].label = `Exponential: P = ${P0}e^(${r.toFixed(2)}t)`;
                growthChart.data.datasets[1].label = `Logistic: K = ${K}, r = ${r.toFixed(2)}`;
                growthChart.update();
            }            rSlider.addEventListener('input', updateGrowthComparison);
            kSlider.addEventListener('input', updateGrowthComparison);
            updateGrowthComparison();
            
            } catch (error) {
                console.error('Error initializing charts:', error);
            }
        }
    </script>

</body>
</html>
