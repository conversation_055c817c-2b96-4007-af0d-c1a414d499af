<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 10: Modeling with First-Order ODEs - ODE Tutorial</title>
    
    <!-- External Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                packages: {'[+]': ['ams', 'newcommand', 'configmacros']}
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>

    <style>
        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #007acc;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        
        .python-code { border-left-color: #3776ab; }
        .r-code { border-left-color: #276dc3; }
        
        .definition-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .example-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .application-box {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .nav-section {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
            border-bottom: 2px solid #e5e7eb;
            padding: 1rem 0;
        }
        
        .chart-container {
            height: 400px;
            margin: 2rem 0;
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .chart-container canvas {
            max-width: 100%;
            height: 100% !important;
        }
        
        .large-chart {
            height: 600px;
            margin: 2rem 0;
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .large-chart canvas {
            max-width: 100%;
            height: 100% !important;
        }
        
        /* Ensure charts are visible */
        canvas {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        /* Loading animation for charts */
        .chart-container:not(.loaded) {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0, #f0f0f0);
            background-size: 400% 400%;
            animation: gradient-loading 2s ease-in-out infinite;
        }
        
        .chart-container:not(.loaded)::before {
            content: "Loading visualization...";
            color: #666;
            font-size: 14px;
        }
        
        @keyframes gradient-loading {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @media print {
            .nav-section { position: static; }
            .chart-container, .large-chart { 
                height: 400px; 
                page-break-inside: avoid; 
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-8">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-2">
                <i class="fas fa-flask mr-3"></i>Chapter 10: Modeling with First-Order ODEs
            </h1>
            <p class="text-xl opacity-90">Part 2: First-Order Differential Equations - Comprehensive Applications</p>
            <div class="mt-4 text-sm opacity-75">
                <span class="mr-4"><i class="fas fa-book mr-1"></i>Advanced Tutorial</span>
                <span class="mr-4"><i class="fas fa-code mr-1"></i>Python & R</span>
                <span><i class="fas fa-chart-line mr-1"></i>Real-World Applications</span>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-section">
        <div class="container mx-auto px-4">
            <nav class="flex flex-wrap gap-4 text-sm">
                <a href="#prerequisites" class="text-blue-600 hover:text-blue-800 font-medium">Prerequisites</a>
                <a href="#intro" class="text-blue-600 hover:text-blue-800 font-medium">Introduction</a>
                <a href="#methodology" class="text-blue-600 hover:text-blue-800 font-medium">Modeling Methodology</a>
                <a href="#mixing" class="text-blue-600 hover:text-blue-800 font-medium">Mixing Problems</a>
                <a href="#population" class="text-blue-600 hover:text-blue-800 font-medium">Population Dynamics</a>
                <a href="#economics" class="text-blue-600 hover:text-blue-800 font-medium">Economic Models</a>
                <a href="#physics" class="text-blue-600 hover:text-blue-800 font-medium">Physics Applications</a>
                <a href="#pharmacology" class="text-blue-600 hover:text-blue-800 font-medium">Clinical Pharmacology</a>
                <a href="#engineering" class="text-blue-600 hover:text-blue-800 font-medium">Engineering Systems</a>
                <a href="#advanced" class="text-blue-600 hover:text-blue-800 font-medium">Advanced Analysis</a>
                <a href="#case-studies" class="text-blue-600 hover:text-blue-800 font-medium">Case Studies</a>
            </nav>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">

        <!-- Prerequisites -->
        <section id="prerequisites" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-graduation-cap text-indigo-600 mr-3"></i>Prerequisites and Mathematical Foundations
            </h2>

            <div class="definition-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-book mr-2"></i>Required Mathematical Background</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-3">Calculus Foundations</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li><strong>Derivatives:</strong> Understanding $\frac{dy}{dx}$ as rate of change</li>
                            <li><strong>Integration:</strong> Antiderivatives and definite integrals</li>
                            <li><strong>Chain Rule:</strong> For composite functions</li>
                            <li><strong>Implicit Differentiation:</strong> For related rates</li>
                            <li><strong>Exponential/Logarithmic Functions:</strong> Properties and derivatives</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-3">Differential Equations Basics</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li><strong>Order and Degree:</strong> Classification of ODEs</li>
                            <li><strong>Initial Value Problems:</strong> Solutions with conditions</li>
                            <li><strong>General vs Particular Solutions:</strong> Family of curves</li>
                            <li><strong>Existence and Uniqueness:</strong> When solutions exist</li>
                            <li><strong>Direction Fields:</strong> Geometric interpretation</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-3 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-calculator text-blue-600 mr-2"></i>Solution Methods Review
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-blue-50 rounded">
                            <strong>Separable ODEs:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dx} = g(x)h(y)$</p>
                            <p class="text-xs text-gray-600">Separate variables and integrate</p>
                        </div>
                        <div class="p-3 bg-green-50 rounded">
                            <strong>Linear ODEs:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dx} + P(x)y = Q(x)$</p>
                            <p class="text-xs text-gray-600">Use integrating factor $\mu(x) = e^{\int P(x)dx}$</p>
                        </div>
                        <div class="p-3 bg-purple-50 rounded">
                            <strong>Exact ODEs:</strong>
                            <p class="text-sm mt-1">$M(x,y)dx + N(x,y)dy = 0$</p>
                            <p class="text-xs text-gray-600">When $\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-chart-line text-green-600 mr-2"></i>Key Mathematical Concepts
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-yellow-50 rounded">
                            <strong>Exponential Growth/Decay:</strong>
                            <p class="text-sm mt-1">$y = Ae^{kt}$</p>
                            <p class="text-xs text-gray-600">k > 0: growth, k < 0: decay</p>
                        </div>
                        <div class="p-3 bg-orange-50 rounded">
                            <strong>Equilibrium Solutions:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dt} = 0$</p>
                            <p class="text-xs text-gray-600">Constant solutions</p>
                        </div>
                        <div class="p-3 bg-red-50 rounded">
                            <strong>Stability Analysis:</strong>
                            <p class="text-sm mt-1">Linearization near equilibria</p>
                            <p class="text-xs text-gray-600">Stable vs unstable behavior</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-laptop-code text-purple-600 mr-2"></i>Computational Tools
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-indigo-50 rounded">
                            <strong>Python Libraries:</strong>
                            <p class="text-sm mt-1">scipy.integrate, numpy, matplotlib</p>
                            <p class="text-xs text-gray-600">For numerical solutions and plotting</p>
                        </div>
                        <div class="p-3 bg-cyan-50 rounded">
                            <strong>R Packages:</strong>
                            <p class="text-sm mt-1">deSolve, ggplot2, plotly</p>
                            <p class="text-xs text-gray-600">Statistical analysis and visualization</p>
                        </div>
                        <div class="p-3 bg-pink-50 rounded">
                            <strong>Symbolic Math:</strong>
                            <p class="text-sm mt-1">SymPy (Python), Mathematica</p>
                            <p class="text-xs text-gray-600">Analytical solutions</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="theorem-box mt-6">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-exclamation-triangle mr-2"></i>Important Theorems</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Existence and Uniqueness Theorem</h4>
                        <p class="text-sm mb-2">For the IVP: $\frac{dy}{dx} = f(x,y)$, $y(x_0) = y_0$</p>
                        <p class="text-sm">If $f$ and $\frac{\partial f}{\partial y}$ are continuous in a rectangle containing $(x_0, y_0)$, then there exists a unique solution in some interval around $x_0$.</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Fundamental Theorem of Calculus</h4>
                        <p class="text-sm mb-2">$\frac{d}{dx}\int_{a}^{x} f(t)dt = f(x)$</p>
                        <p class="text-sm">Essential for solving ODEs by integration and understanding accumulation functions.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Introduction -->
        <section id="intro" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-rocket text-blue-600 mr-3"></i>Introduction to ODE Modeling
            </h2>
            
            <div class="definition-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>Mathematical Modeling with ODEs</h3>
                <p class="mb-3">Mathematical modeling with first-order differential equations involves:</p>
                <ul class="list-disc list-inside space-y-2">
                    <li><strong>Problem Identification:</strong> Recognizing systems that change continuously over time</li>
                    <li><strong>Mathematical Translation:</strong> Converting real-world relationships into ODE form</li>
                    <li><strong>Solution Strategy:</strong> Choosing appropriate solution methods (separable, linear, exact)</li>
                    <li><strong>Parameter Estimation:</strong> Determining model parameters from data</li>
                    <li><strong>Validation:</strong> Testing model predictions against observations</li>
                    <li><strong>Interpretation:</strong> Understanding what solutions mean in context</li>
                </ul>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-8">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-tools text-green-600 mr-2"></i>Solution Methods Review
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-blue-50 rounded">
                            <strong>Separable:</strong> $\frac{dy}{dx} = g(x)h(y)$
                            <br><span class="text-sm text-gray-600">Growth, decay, cooling problems</span>
                        </div>
                        <div class="p-3 bg-green-50 rounded">
                            <strong>Linear:</strong> $\frac{dy}{dx} + P(x)y = Q(x)$
                            <br><span class="text-sm text-gray-600">Mixing, circuits, harvesting</span>
                        </div>
                        <div class="p-3 bg-purple-50 rounded">
                            <strong>Exact:</strong> $M dx + N dy = 0$
                            <br><span class="text-sm text-gray-600">Conservative systems, thermodynamics</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-globe text-blue-600 mr-2"></i>Application Domains
                    </h3>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <i class="fas fa-flask text-green-500 mr-2"></i>
                            <span>Chemistry & Biology</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                            <span>Economics & Finance</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-atom text-purple-500 mr-2"></i>
                            <span>Physics & Engineering</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-users text-orange-500 mr-2"></i>
                            <span>Population Dynamics</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-thermometer-half text-red-500 mr-2"></i>
                            <span>Environmental Science</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Modeling Methodology -->
        <section id="methodology" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-purple-600 mr-3"></i>Systematic Modeling Methodology
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-list-ol mr-2"></i>The Modeling Process</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">1. Problem Formulation</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Identify the system and variables</li>
                            <li>Determine what changes over time</li>
                            <li>Establish the independent variable (usually time)</li>
                            <li>Define the dependent variable(s)</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">2. Mathematical Translation</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Express rate of change: $\frac{dy}{dt}$</li>
                            <li>Identify relationships between variables</li>
                            <li>Apply physical laws or principles</li>
                            <li>Include initial/boundary conditions</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">3. Solution Strategy</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Classify the ODE type</li>
                            <li>Choose appropriate solution method</li>
                            <li>Solve analytically or numerically</li>
                            <li>Handle parameter estimation</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">4. Validation & Analysis</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Compare with experimental data</li>
                            <li>Perform sensitivity analysis</li>
                            <li>Check limiting behavior</li>
                            <li>Interpret results in context</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-decision text-blue-600 mr-2"></i>Equation Classification Decision Tree
                </h3>
                <div class="chart-container">
                    <canvas id="classificationChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Mixing Problems -->
        <section id="mixing" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-tint text-blue-600 mr-3"></i>Mixing Problems (Tank Problems)
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-flask mr-2"></i>General Mixing Problem Setup</h3>
                <p class="mb-4">A tank contains a solution with concentration that changes over time due to inflow and outflow.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Fundamental Principle:</h4>
                    <p class="text-center text-lg">
                        $$\frac{d(\text{Amount})}{dt} = \text{Rate In} - \text{Rate Out}$$
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-bold mb-2">Key Variables:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>$V(t)$ = Volume in tank at time $t$</li>
                            <li>$S(t)$ = Amount of substance at time $t$</li>
                            <li>$C(t) = S(t)/V(t)$ = Concentration</li>
                            <li>$r_{in}, r_{out}$ = Flow rates in/out</li>
                            <li>$C_{in}$ = Input concentration</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Standard Model:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Rate In = $r_{in} \cdot C_{in}$</li>
                            <li>Rate Out = $r_{out} \cdot C(t)$</li>
                            <li>ODE: $\frac{dS}{dt} = r_{in}C_{in} - r_{out}C(t)$</li>
                            <li>If $V$ constant: $\frac{dC}{dt} + \frac{r}{V}C = \frac{r_{in}C_{in}}{V}$</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 1: Salt Water Tank</h3>
                <p class="mb-3">A tank contains 1000 L of pure water. Salt water with concentration 0.5 kg/L flows in at 10 L/min. The well-mixed solution flows out at 10 L/min.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Solution Setup:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>$V = 1000$ L (constant volume)</li>
                        <li>$r_{in} = r_{out} = 10$ L/min</li>
                        <li>$C_{in} = 0.5$ kg/L</li>
                        <li>$C(0) = 0$ kg/L (initially pure water)</li>
                    </ul>
                    
                    <p class="mt-3"><strong>ODE:</strong> $\frac{dC}{dt} + \frac{10}{1000}C = \frac{10 \times 0.5}{1000}$</p>
                    <p class="mt-2">Simplifies to: $\frac{dC}{dt} + 0.01C = 0.005$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Analytical Solution:</h4>
                    <p>This is a first-order linear ODE. Using integrating factor $\mu = e^{0.01t}$:</p>
                    <p class="text-center mt-2">$$C(t) = 0.5(1 - e^{-0.01t})$$</p>
                    <p class="mt-2">Equilibrium concentration: $C_{\infty} = 0.5$ kg/L</p>
                    <p>Time to reach 95% of equilibrium: $t = -\frac{\ln(0.05)}{0.01} \approx 300$ minutes</p>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python Implementation
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

def mixing_tank(t, y, r_in, r_out, V, C_in):
    """
    Tank mixing model
    y[0] = C(t) = concentration
    """
    C = y[0]
    dCdt = (r_in * C_in - r_out * C) / V
    return [dCdt]

# Parameters
V = 1000  # Tank volume (L)
r_in = r_out = 10  # Flow rates (L/min)
C_in = 0.5  # Input concentration (kg/L)
C0 = 0  # Initial concentration

# Time span
t_span = (0, 500)
t_eval = np.linspace(0, 500, 1000)

# Solve ODE
sol = solve_ivp(mixing_tank, t_span, [C0], t_eval=t_eval, 
                args=(r_in, r_out, V, C_in))

# Analytical solution for comparison
t_analytical = t_eval
C_analytical = C_in * (1 - np.exp(-r_out * t_analytical / V))

# Plot results
plt.figure(figsize=(10, 6))
plt.plot(sol.t, sol.y[0], 'b-', linewidth=2, 
         label='Numerical Solution')
plt.plot(t_analytical, C_analytical, 'r--', linewidth=2, 
         label='Analytical Solution')
plt.axhline(y=C_in, color='g', linestyle=':', alpha=0.7, 
            label='Equilibrium')
plt.xlabel('Time (min)')
plt.ylabel('Concentration (kg/L)')
plt.title('Salt Water Tank Mixing Problem')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Parameter estimation from data
def fit_mixing_model(t_data, C_data, V, r_flow):
    """Estimate C_in from experimental data"""
    from scipy.optimize import curve_fit
    
    def model(t, C_in):
        return C_in * (1 - np.exp(-r_flow * t / V))
    
    popt, pcov = curve_fit(model, t_data, C_data)
    return popt[0], np.sqrt(pcov[0,0])

# Example with noisy data
t_data = np.array([0, 50, 100, 150, 200, 300, 400])
C_true = 0.5 * (1 - np.exp(-0.01 * t_data))
C_data = C_true + np.random.normal(0, 0.01, len(t_data))

C_in_est, C_in_err = fit_mixing_model(t_data, C_data, V, r_out)
print(f"Estimated C_in: {C_in_est:.3f} ± {C_in_err:.3f} kg/L")

# Sensitivity analysis
def sensitivity_analysis():
    V_values = np.linspace(800, 1200, 50)
    C_final = []
    
    for V_test in V_values:
        t_final = 300
        C_eq = C_in * (1 - np.exp(-r_out * t_final / V_test))
        C_final.append(C_eq)
    
    plt.figure(figsize=(8, 5))
    plt.plot(V_values, C_final, 'b-', linewidth=2)
    plt.xlabel('Tank Volume (L)')
    plt.ylabel('Concentration at t=300 min (kg/L)')
    plt.title('Sensitivity to Tank Volume')
    plt.grid(True, alpha=0.3)
    plt.show()

sensitivity_analysis()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R Implementation
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)

# Define mixing tank model
mixing_tank <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    C <- state[1]
    dCdt <- (r_in * C_in - r_out * C) / V
    return(list(dCdt))
  })
}

# Parameters
parms <- list(
  V = 1000,      # Tank volume (L)
  r_in = 10,     # Inflow rate (L/min)
  r_out = 10,    # Outflow rate (L/min)
  C_in = 0.5     # Input concentration (kg/L)
)

# Initial conditions and time
initial_state <- c(C = 0)  # Initial concentration
times <- seq(0, 500, by = 1)

# Solve ODE
solution <- ode(y = initial_state, 
                times = times, 
                func = mixing_tank, 
                parms = parms)

# Convert to data frame
sol_df <- as.data.frame(solution)

# Analytical solution
sol_df$C_analytical <- with(parms, 
  C_in * (1 - exp(-r_out * sol_df$time / V)))

# Create visualization
p1 <- ggplot(sol_df, aes(x = time)) +
  geom_line(aes(y = C, color = "Numerical"), size = 1.2) +
  geom_line(aes(y = C_analytical, color = "Analytical"), 
            linetype = "dashed", size = 1.2) +
  geom_hline(yintercept = parms$C_in, 
             color = "green", linetype = "dotted", alpha = 0.7) +
  labs(title = "Salt Water Tank Mixing Problem",
       x = "Time (min)",
       y = "Concentration (kg/L)",
       color = "Solution") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p1)

# Parameter estimation function
estimate_parameters <- function(t_data, C_data, V, r_flow) {
  # Define model function
  model_func <- function(t, C_in) {
    C_in * (1 - exp(-r_flow * t / V))
  }
  
  # Fit model
  fit <- nls(C_data ~ model_func(t_data, C_in),
             start = list(C_in = 0.4))
  
  return(summary(fit))
}

# Generate synthetic data with noise
set.seed(123)
t_data <- c(0, 50, 100, 150, 200, 300, 400)
C_true <- with(parms, C_in * (1 - exp(-r_out * t_data / V)))
C_data <- C_true + rnorm(length(t_data), 0, 0.01)

# Estimate parameters
fit_result <- estimate_parameters(t_data, C_data, 
                                  parms$V, parms$r_out)
print(fit_result)

# Sensitivity analysis
sensitivity_analysis <- function() {
  V_range <- seq(800, 1200, length.out = 50)
  t_final <- 300
  
  C_final <- sapply(V_range, function(V_test) {
    with(parms, C_in * (1 - exp(-r_out * t_final / V_test)))
  })
  
  p2 <- ggplot(data.frame(V = V_range, C_final = C_final), aes(x = V, y = C_final)) +
    geom_line(color = "blue", size = 1.2) +
    labs(title = "Sensitivity to Tank Volume",
         x = "Tank Volume (L)",
         y = "Concentration at t=300 min (kg/L)") +
    theme_minimal()
  
  return(p2)
}

sens_plot <- sensitivity_analysis()
print(sens_plot)

# Multi-tank system
multi_tank_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    C1 <- state[1]  # Tank 1 concentration
    C2 <- state[2]  # Tank 2 concentration
    
    # Tank 1: Input from external source
    dC1dt <- (r_in * C_in - r12 * C1) / V1
    
    # Tank 2: Input from Tank 1, output to environment
    dC2dt <- (r12 * C1 - r_out * C2) / V2
    
    return(list(c(dC1dt, dC2dt)))
  })
}

# Multi-tank parameters
multi_parms <- list(
  V1 = 1000, V2 = 800,
  r_in = 10, r12 = 10, r_out = 10,
  C_in = 0.5
)

multi_initial <- c(C1 = 0, C2 = 0)
multi_solution <- ode(y = multi_initial,
                      times = times,
                      func = multi_tank_model,
                      parms = multi_parms)

multi_df <- as.data.frame(multi_solution)

# Plot multi-tank system
p3 <- ggplot(multi_df, aes(x = time)) +
  geom_line(aes(y = C1, color = "Tank 1"), size = 1.2) +
  geom_line(aes(y = C2, color = "Tank 2"), size = 1.2) +
  labs(title = "Two-Tank Mixing System",
       x = "Time (min)",
       y = "Concentration (kg/L)",
       color = "Tank") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p3)
                        </code></pre>
                    </div>
                </div>
            </div>

            <!-- Interactive Mixing Problem Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>Interactive Mixing Problem Visualization
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows the concentration change over time in a salt water mixing problem. 
                    The solution approaches equilibrium concentration exponentially.
                </p>
                <div class="chart-container">
                    <canvas id="mixingChart"></canvas>
                </div>
                <div class="mt-4 grid md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded">
                        <strong>Initial Conditions:</strong><br>
                        Tank volume: 1000 L<br>
                        Initial concentration: 0 kg/L
                    </div>
                    <div class="bg-green-50 p-3 rounded">
                        <strong>Flow Parameters:</strong><br>
                        Inflow rate: 10 L/min<br>
                        Input concentration: 0.5 kg/L
                    </div>
                    <div class="bg-orange-50 p-3 rounded">
                        <strong>Results:</strong><br>
                        Equilibrium: 0.5 kg/L<br>
                        Time to 95%: ~300 min
                    </div>
                </div>
            </div>
        </section>

        <!-- Mixing Problem Variations -->
        <div class="theorem-box mt-6">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-cogs mr-2"></i>Mixing Problem Variations
            </h3>
            <p class="mb-4">Real-world mixing problems often involve more complex scenarios. Here are common variations and their mathematical formulations:</p>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-chart-line mr-2"></i>Variable Volume Tank</h4>
                    <p class="mb-2">When inflow ≠ outflow, volume changes with time:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dV}{dt} = r_{in} - r_{out}$$</p>
                        <p>$$\frac{dS}{dt} = r_{in}C_{in} - r_{out}C(t)$$</p>
                        <p>$$C(t) = \frac{S(t)}{V(t)}$$</p>
                    </div>
                    <p class="text-sm">More complex as both $S(t)$ and $V(t)$ vary</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-wave-square mr-2"></i>Time-Varying Input</h4>
                    <p class="mb-2">Input concentration changes with time:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$C_{in}(t) = A + B\sin(\omega t)$$</p>
                        <p>$$\frac{dC}{dt} + \frac{r}{V}C = \frac{r \cdot C_{in}(t)}{V}$$</p>
                    </div>
                    <p class="text-sm">Periodic forcing leads to oscillatory solutions</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-sitemap mr-2"></i>Multi-Tank Systems</h4>
                    <p class="mb-2">Connected tanks create system of ODEs:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dC_1}{dt} = \frac{r_{in}C_{in} - r_{12}C_1}{V_1}$$</p>
                        <p>$$\frac{dC_2}{dt} = \frac{r_{12}C_1 - r_{out}C_2}{V_2}$$</p>
                    </div>
                    <p class="text-sm">Cascade effect with time delays</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-exclamation-triangle mr-2"></i>Chemical Reactions</h4>
                    <p class="mb-2">Substance undergoes reaction while mixing:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dC}{dt} = \frac{r_{in}C_{in} - r_{out}C}{V} - kC^n$$</p>
                    </div>
                    <p class="text-sm">$k$ = reaction rate, $n$ = reaction order</p>
                </div>
            </div>
            
            <div class="mt-6 bg-white bg-opacity-20 p-4 rounded">
                <h4 class="font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>Key Solution Strategies</h4>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-semibold mb-2">Analytical Methods:</h5>
                        <ul class="text-sm space-y-1">
                            <li>• Integrating factor for linear cases</li>
                            <li>• Separation of variables when applicable</li>
                            <li>• Laplace transforms for complex inputs</li>
                            <li>• Steady-state analysis for equilibrium</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-semibold mb-2">Numerical Approaches:</h5>
                        <ul class="text-sm space-y-1">
                            <li>• Runge-Kutta methods for systems</li>
                            <li>• Adaptive step-size for stiff problems</li>
                            <li>• Parameter estimation from data</li>
                            <li>• Sensitivity and uncertainty analysis</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 p-4 bg-white bg-opacity-20 rounded">
                <h4 class="font-bold mb-2"><i class="fas fa-industry mr-2"></i>Real-World Applications</h4>
                <div class="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <strong>Water Treatment:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Chlorination systems</li>
                            <li>• pH adjustment tanks</li>
                            <li>• Coagulation processes</li>
                        </ul>
                    </div>
                    <div>
                        <strong>Chemical Processing:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Reactor vessels</li>
                            <li>• Distillation columns</li>
                            <li>• Batch processing</li>
                        </ul>
                    </div>
                    <div>
                        <strong>Environmental:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Lake pollution models</li>
                            <li>• Atmospheric mixing</li>
                            <li>• Groundwater contamination</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Population Dynamics -->
        <section id="population" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-users text-green-600 mr-3"></i>Population Dynamics
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-seedling mr-2"></i>Population Growth Models</h3>
                <p class="mb-4">Population dynamics models describe how populations change over time under various conditions.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Exponential Growth</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP$</p>
                        <p class="text-sm">Solution: $P(t) = P_0 e^{rt}$</p>
                        <p class="text-xs mt-1">Unlimited resources</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Logistic Growth</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP(1 - \frac{P}{K})$</p>
                        <p class="text-sm">S-shaped growth curve</p>
                        <p class="text-xs mt-1">Carrying capacity $K$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">With Harvesting</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP - H$</p>
                        <p class="text-sm">Constant harvest rate</p>
                        <p class="text-xs mt-1">Sustainability analysis</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 2: Logistic Population Model</h3>
                <p class="mb-3">A population grows according to the logistic model with intrinsic growth rate $r = 0.1$ per year and carrying capacity $K = 1000$ individuals.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Logistic Equation:</h4>
                    <p class="text-center">$$\frac{dP}{dt} = rP\left(1 - \frac{P}{K}\right) = 0.1P\left(1 - \frac{P}{1000}\right)$$</p>
                    
                    <p class="mt-3"><strong>This is separable:</strong></p>
                    <p class="text-center">$$\frac{dP}{P(1 - P/K)} = r \, dt$$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution by Partial Fractions:</h4>
                    <p>$$\frac{1}{P(1 - P/K)} = \frac{A}{P} + \frac{B}{1 - P/K}$$</p>
                    <p>After partial fractions and integration:</p>
                    <p class="text-center mt-2">$$P(t) = \frac{K}{1 + \left(\frac{K}{P_0} - 1\right)e^{-rt}}$$</p>
                    <p class="mt-2"><strong>Key Features:</strong></p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>Inflection point at $P = K/2$</li>
                        <li>Maximum growth rate at $P = K/2$: $\frac{dP}{dt}|_{max} = \frac{rK}{4}$</li>
                        <li>Approaches carrying capacity: $\lim_{t \to \infty} P(t) = K$</li>
                    </ul>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Population Dynamics in Python
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import fsolve

# Population growth models
def exponential_growth(t, y, r):
    """Exponential growth: dP/dt = rP"""
    return [r * y[0]]

def logistic_growth(t, y, r, K):
    """Logistic growth: dP/dt = rP(1 - P/K)"""
    P = y[0]
    return [r * P * (1 - P/K)]

def growth_with_harvesting(t, y, r, K, H):
    """Growth with harvesting: dP/dt = rP(1 - P/K) - H"""
    P = y[0]
    return [r * P * (1 - P/K) - H]

# Parameters
r = 0.1  # Growth rate (1/year)
K = 1000  # Carrying capacity
P0 = 50   # Initial population
t_span = (0, 50)
t_eval = np.linspace(0, 50, 500)

# Solve different models
sol_exp = solve_ivp(exponential_growth, t_span, [P0], 
                    t_eval=t_eval, args=(r,))
sol_log = solve_ivp(logistic_growth, t_span, [P0], 
                    t_eval=t_eval, args=(r, K))

# Analytical solutions
t = t_eval
P_exp_analytical = P0 * np.exp(r * t)
P_log_analytical = K / (1 + (K/P0 - 1) * np.exp(-r * t))

# Plot comparison
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Population curves
ax1.plot(t, P_exp_analytical, 'r-', linewidth=2, 
         label='Exponential')
ax1.plot(t, P_log_analytical, 'b-', linewidth=2, 
         label='Logistic')
ax1.axhline(y=K, color='g', linestyle='--', alpha=0.7, 
            label='Carrying Capacity')
ax1.set_xlabel('Time (years)')
ax1.set_ylabel('Population')
ax1.set_title('Population Growth Models')
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.set_ylim(0, 1200)

# Growth rates
dPdt_exp = r * P_exp_analytical
dPdt_log = r * P_log_analytical * (1 - P_log_analytical/K);

ax2.plot(P_exp_analytical, dPdt_exp, 'r-', linewidth=2, 
         label='Exponential')
ax2.plot(P_log_analytical, dPdt_log, 'b-', linewidth=2, 
         label='Logistic')
ax2.axvline(x=K/2, color='purple', linestyle=':', alpha=0.7, 
            label='Max Growth Rate')
ax2.set_xlabel('Population')
ax2.set_ylabel('Growth Rate (dP/dt)')
ax2.set_title('Phase Portrait')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Harvesting analysis
def analyze_harvesting():
    """Analyze sustainable harvesting"""
    # Critical harvesting rate (maximum sustainable)
    H_max = r * K / 4  # At P = K/2
    
    print(f"Maximum sustainable harvest rate: {H_max:.2f}")
    
    # Different harvesting scenarios
    H_values = [0, H_max/2, H_max, H_max*1.2]
    colors = ['blue', 'green', 'orange', 'red']
    labels = ['No harvest', 'Sustainable', 'Critical', 'Overharvest']
    
    plt.figure(figsize=(12, 8))
    
    for i, H in enumerate(H_values):
        if H < H_max * 1.1:  # Avoid extinction scenarios
            sol_h = solve_ivp(growth_with_harvesting, t_span, [P0], 
                             t_eval=t_eval, args=(r, K, H))
            plt.plot(sol_h.t, sol_h.y[0], color=colors[i], 
                    linewidth=2, label=f'{labels[i]} (H={H:.1f})')
    
    plt.axhline(y=K, color='gray', linestyle='--', alpha=0.5, 
                label='Carrying Capacity')
    plt.xlabel('Time (years)')
    plt.ylabel('Population')
    plt.title('Population Dynamics with Harvesting')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    # Equilibrium analysis
    def equilibrium_points(H):
        """Find equilibrium points for harvesting model"""
        def f(P):
            return r * P * (1 - P/K) - H
        
        if H <= H_max:
            roots = fsolve(f, [K/4, 3*K/4])
            return roots[roots > 0]  # Positive roots only
        else:
            return []
    
    H_range = np.linspace(0, H_max*1.5, 100)
    equilibria = []
    
    for H in H_range:
        eq_points = equilibrium_points(H)
        if len(eq_points) > 0:
            equilibria.extend([(H, P) for P in eq_points])
    
    if equilibria:
        H_eq, P_eq = zip(*equilibria)
        plt.figure(figsize=(10, 6))
        plt.plot(H_eq, P_eq, 'b.', markersize=3)
        plt.axvline(x=H_max, color='red', linestyle='--', 
                   label=f'Critical H = {H_max:.2f}')
        plt.xlabel('Harvest Rate')
        plt.ylabel('Equilibrium Population')
        plt.title('Bifurcation Diagram: Harvest Rate vs Population')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

analyze_harvesting()

# Parameter estimation from data
def fit_logistic_model(t_data, P_data):
    """Fit logistic model to population data"""
    from scipy.optimize import curve_fit
    
    def logistic_func(t, r, K, P0):
        return K / (1 + (K/P0 - 1) * np.exp(-r * t))
    
    # Initial guess
    K_guess = max(P_data) * 1.2
    r_guess = 0.1
    P0_guess = P_data[0]
    
    popt, pcov = curve_fit(logistic_func, t_data, P_data, 
                          p0=[r_guess, K_guess, P0_guess],
                          bounds=([0, max(P_data), 0], 
                                 [1, max(P_data)*3, max(P_data)]))
    
    r_fit, K_fit, P0_fit = popt
    r_err, K_err, P0_err = np.sqrt(np.diag(pcov))
    
    return (r_fit, K_fit, P0_fit), (r_err, K_err, P0_err)

# Generate synthetic data
t_data = np.array([0, 2, 5, 10, 15, 20, 25, 30])
P_true = K / (1 + (K/P0 - 1) * np.exp(-r * t_data))
P_data = P_true + np.random.normal(0, P_true * 0.05)  # 5% noise

# Fit model
params, errors = fit_logistic_model(t_data, P_data)
r_fit, K_fit, P0_fit = params
print(f"Fitted parameters:")
print(f"r = {r_fit:.4f} ± {errors[0]:.4f}")
print(f"K = {K_fit:.1f} ± {errors[1]:.1f}")
print(f"P0 = {P0_fit:.1f} ± {errors[2]:.1f}")

# Compare with true values
P_fitted = K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_eval))

plt.figure(figsize=(10, 6))
plt.plot(t_eval, P_log_analytical, 'b-', linewidth=2, label='True Model')
plt.plot(t_eval, P_fitted, 'r--', linewidth=2, label='Fitted Model')
plt.scatter(t_data, P_data, color='black', s=50, 
           label='Data Points', zorder=5)
plt.xlabel('Time (years)')
plt.ylabel('Population')
plt.title('Logistic Model Fitting')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Model diagnostics - residual analysis
residuals = P_data - K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_data))
fitted_values = K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_data))

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Residuals vs fitted values
ax1.scatter(fitted_values, residuals, color='blue', s=50)
ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
ax1.set_xlabel('Fitted Values')
ax1.set_ylabel('Residuals')
ax1.set_title('Residual Analysis')
ax1.grid(True, alpha=0.3)

# Residuals vs time
ax2.scatter(t_data, residuals, color='blue', s=50)
ax2.plot(t_data, residuals, color='blue', alpha=0.5)
ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
ax2.set_xlabel('Time')
ax2.set_ylabel('Residuals')
ax2.set_title('Residuals vs Time')
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
                        </code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-red-600 mr-2"></i>Population Dynamics in R
                    </h3>
                    <div class="code-block r-code">
                        <pre><code># Population growth models in R
library(deSolve)
library(ggplot2)
library(gridExtra)

# Define population growth models
exponential_growth <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P
    list(c(dP))
  })
}

logistic_growth <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P * (1 - P/K)
    list(c(dP))
  })
}

growth_with_harvesting <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P * (1 - P/K) - H
    list(c(dP))
  })
}

# Parameters
parameters <- list(r = 0.1, K = 1000, H = 25)
initial_conditions <- c(P = 50)
times <- seq(0, 50, by = 0.1)

# Solve ODEs
sol_exp <- ode(y = initial_conditions, 
               times = times, 
               func = exponential_growth, 
               parms = list(r = parameters$r))

sol_log <- ode(y = initial_conditions, 
               times = times, 
               func = logistic_growth, 
               parms = list(r = parameters$r, K = parameters$K))

sol_harv <- ode(y = initial_conditions, 
                times = times, 
                func = growth_with_harvesting, 
                parms = parameters)

# Create data frames for plotting
df_exp <- data.frame(time = sol_exp[,1], population = sol_exp[,2], model = "Exponential")
df_log <- data.frame(time = sol_log[,1], population = sol_log[,2], model = "Logistic")
df_harv <- data.frame(time = sol_harv[,1], population = sol_harv[,2], model = "With Harvesting")

combined_df <- rbind(df_exp, df_log, df_harv)

# Analytical solutions for comparison
t_analytical <- times
P_exp_analytical <- initial_conditions[1] * exp(parameters$r * t_analytical)
P_log_analytical <- parameters$K / (1 + (parameters$K/initial_conditions[1] - 1) * 
                                   exp(-parameters$r * t_analytical))

# Plot results
p1 <- ggplot(combined_df, aes(x = time, y = population, color = model)) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = parameters$K, linetype = "dashed", 
             color = "gray", alpha = 0.7) +
  labs(title = "Population Growth Models Comparison",
       x = "Time (years)",
       y = "Population",
       color = "Model") +
  theme_minimal() +
  theme(legend.position = "bottom") +
  scale_color_manual(values = c("red", "blue", "green"))

print(p1)

# Parameter estimation and model fitting
# Generate synthetic data with noise
set.seed(123)
t_data <- c(0, 2, 5, 10, 15, 20, 25, 30)
P_true <- parameters$K / (1 + (parameters$K/initial_conditions[1] - 1) * 
                         exp(-parameters$r * t_data))
P_data <- P_true + rnorm(length(t_data), 0, P_true * 0.05)  # 5% noise

# Fit logistic model using nls
logistic_model <- function(t, r, K, P0) {
  K / (1 + (K/P0 - 1) * exp(-r * t))
}

# Non-linear least squares fitting
fit_result <- nls(P_data ~ logistic_model(t_data, r, K, P0),
                  start = list(r = 0.1, K = 1200, P0 = 50),
                  algorithm = "port",
                  lower = c(0.01, max(P_data), 0),
                  upper = c(1, max(P_data)*3, max(P_data)))

# Extract fitted parameters
fitted_params <- summary(fit_result)
print("Fitted Parameters:")
print(fitted_params$parameters)

# Model predictions
t_pred <- seq(0, 40, length.out = 200)
P_pred <- predict(fit_result, newdata = list(t_data = t_pred))

# Plot fitted model
fitting_df <- data.frame(
  time = c(t_analytical, t_pred, t_data),
  population = c(P_log_analytical, P_pred, P_data),
  type = c(rep("True", length(t_analytical)), 
           rep("Fitted", length(t_pred)),
           rep("Data", length(t_data)))
)

p3 <- ggplot(fitting_df, aes(x = time, y = population)) +
  geom_line(data = subset(fitting_df, type == "True"), 
            aes(color = "True Model"), size = 1.2) +
  geom_line(data = subset(fitting_df, type == "Fitted"), 
            aes(color = "Fitted Model"), size = 1.2, linetype = "dashed") +
  geom_point(data = subset(fitting_df, type == "Data"), 
             aes(color = "Data Points"), size = 3) +
  labs(title = "Logistic Model Parameter Estimation",
       x = "Time (years)",
       y = "Population",
       color = "Legend") +
  theme_minimal() +
  scale_color_manual(values = c("blue", "red", "black"))

print(p3)
                        </code></pre>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="populationChart"></canvas>
            </div>
        </section>

        <!-- Economic Models -->
        <section id="economics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>Economic Models
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-dollar-sign mr-2"></i>Economic Growth and Decay</h3>
                <p class="mb-4">Economic systems often exhibit exponential or logistic behavior in investment, savings, and debt models.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Compound Interest</h4>
                        <p class="text-sm mb-2">$\frac{dA}{dt} = rA$</p>
                        <p class="text-sm">Solution: $A(t) = A_0 e^{rt}$</p>
                        <p class="text-xs mt-1">Continuous compounding</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Savings with Deposits</h4>
                        <p class="text-sm mb-2">$\frac{dS}{dt} = rS + D$</p>
                        <p class="text-sm">Regular contributions</p>
                        <p class="text-xs mt-1">Linear first-order ODE</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Debt Repayment</h4>
                        <p class="text-sm mb-2">$\frac{dD}{dt} = rD - P$</p>
                        <p class="text-sm">Interest vs payments</p>
                        <p class="text-xs mt-1">Amortization analysis</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 3: Loan Amortization Model</h3>
                <p class="mb-3">A loan of $100,000 at 5% annual interest requires monthly payments. Model the debt balance over time.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Debt Balance Equation:</h4>
                    <p class="text-center">$$\frac{dD}{dt} = rD - P$$</p>
                    <p class="mt-2">Where $D$ is debt balance, $r = 0.05$ is annual rate, $P$ is monthly payment rate</p>
                    
                    <p class="mt-3"><strong>This is linear first-order:</strong></p>
                    <p class="text-center">$$\frac{dD}{dt} - rD = -P$$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution:</h4>
                    <p>Using integrating factor $\mu(t) = e^{-rt}$:</p>
                    <p class="text-center mt-2">$$D(t) = \left(D_0 - \frac{P}{r}\right)e^{rt} + \frac{P}{r}$$</p>
                    <p class="mt-2"><strong>Key Insights:</strong></p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>If $P > rD_0$: Debt decreases to zero</li>
                        <li>If $P = rD_0$: Interest-only payments</li>
                        <li>If $P < rD_0$: Debt grows exponentially</li>
                        <li>Time to payoff: $t_{payoff} = \frac{1}{r}\ln\left(\frac{P}{P-rD_0}\right)$</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Physics Applications -->
        <section id="physics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-atom text-purple-600 mr-3"></i>Physics Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-thermometer-half mr-2"></i>Newton's Law of Cooling</h3>
                <p class="mb-4">Temperature changes in objects follow Newton's law of cooling, a fundamental first-order ODE.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Newton's Law</h4>
                        <p class="text-sm mb-2">$\frac{dT}{dt} = -k(T - T_{env})$</p>
                        <p class="text-sm">Rate ∝ temperature difference</p>
                        <p class="text-xs mt-1">Separable equation</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Solution</h4>
                        <p class="text-sm mb-2">$T(t) = T_{env} + (T_0 - T_{env})e^{-kt}$</p>
                        <p class="text-sm">Exponential approach</p>
                        <p class="text-xs mt-1">To environmental temperature</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 4: Coffee Cooling Problem</h3>
                <p class="mb-3">A cup of coffee at 90°C is placed in a 20°C room. After 5 minutes, it cools to 70°C. When will it reach 30°C?</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Setup:</h4>
                    <p>$\frac{dT}{dt} = -k(T - 20)$, with $T(0) = 90$, $T(5) = 70$</p>
                    
                    <p class="mt-3"><strong>Solution:</strong></p>
                    <p>$T(t) = 20 + 70e^{-kt}$</p>
                    <p>From $T(5) = 70$: $70 = 20 + 70e^{-5k}$</p>
                    <p>Solving: $k = \frac{1}{5}\ln\left(\frac{7}{5}\right) \approx 0.0671$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Finding when T = 30°C:</h4>
                    <p>$30 = 20 + 70e^{-0.0671t}$</p>
                    <p>$10 = 70e^{-0.0671t}$</p>
                    <p>$t = \frac{\ln(7)}{0.0671} \approx 29.0$ minutes</p>
                </div>
            </div>
        </section>

        <!-- Engineering Systems -->
        <section id="engineering" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-orange-600 mr-3"></i>Engineering Systems
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-bolt mr-2"></i>RC Circuits</h3>
                <p class="mb-4">Resistor-Capacitor circuits exhibit first-order behavior in charging and discharging.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Kirchhoff's Law</h4>
                        <p class="text-sm mb-2">$RC\frac{dV_C}{dt} + V_C = V_{in}$</p>
                        <p class="text-sm">First-order linear ODE</p>
                        <p class="text-xs mt-1">Time constant τ = RC</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Charging Response</h4>
                        <p class="text-sm mb-2">$V_C(t) = V_{in}(1 - e^{-t/RC})$</p>
                        <p class="text-sm">Exponential approach</p>
                        <p class="text-xs mt-1">To input voltage</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Physics Applications -->
        <section id="physics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-atom text-purple-600 mr-3"></i>Physics Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-thermometer-half mr-2"></i>Heat Transfer & Thermodynamics</h3>
                <p class="mb-4">Many physical systems follow first-order differential equations, particularly in heat transfer, radioactive decay, and damped motion.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Newton's Cooling</h4>
                        <p class="text-sm mb-2">$\frac{dT}{dt} = -k(T - T_{\text{amb}})$</p>
                        <p class="text-xs">Heat transfer rate ∝ temperature difference</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Radioactive Decay</h4>
                        <p class="text-sm mb-2">$\frac{dN}{dt} = -\lambda N$</p>
                        <p class="text-xs">N(t) = N₀e^(-λt), Half-life = ln(2)/λ</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Electrical Circuits</h4>
                        <p class="text-sm mb-2">$L\frac{dI}{dt} + RI = V(t)$</p>
                        <p class="text-xs">RL circuit with time constant τ = L/R</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-wave-square text-purple-600 mr-2"></i>Physics Visualization Dashboard
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="physicsDecayChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="physicsCircuitChart"></canvas>
                    </div>
                </div>
                <div class="chart-container mt-6">
                    <canvas id="physicsCoolingChart"></canvas>
                </div>
            </div>
            
            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python: Physics Simulations
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.constants import physical_constants

class PhysicsModels:
    def __init__(self):
        self.sigma_sb = 5.67e-8  # Stefan-Boltzmann constant
        
    def radioactive_decay(self, t, N, lambda_decay):
        """Radioactive decay model: dN/dt = -λN"""
        return -lambda_decay * N
    
    def newton_cooling(self, t, T, params):
        """Newton's cooling: dT/dt = -k(T - T_ambient)"""
        k, T_ambient = params
        return -k * (T - T_ambient)
    
    def cooling_with_radiation(self, t, T, params):
        """Combined cooling with convection and radiation"""
        k_conv, k_rad, T_ambient = params
        return (-k_conv * (T - T_ambient) - 
                k_rad * (T**4 - T_ambient**4))
    
    def rc_circuit(self, t, V, params):
        """RC circuit: RC(dV/dt) + V = V_in"""
        R, C, V_in = params
        return (V_in - V) / (R * C)
    
    def rl_circuit(self, t, I, params):
        """RL circuit: L(dI/dt) + RI = V_in"""
        L, R, V_in = params
        return (V_in - R * I) / L

# Create physics model instance
physics = PhysicsModels()

# Radioactive decay simulation
def simulate_decay_chain():
    """Simulate decay chain of radioactive isotopes"""
    isotopes = {
        'U-238': {'half_life': 4.468e9, 'initial': 1000},
        'Ra-226': {'half_life': 1600, 'initial': 0},
        'Rn-222': {'half_life': 3.8235, 'initial': 0}
    }
    
    time_span = np.logspace(0, 6, 1000)  # Years
    
    for name, data in isotopes.items():
        lambda_decay = np.log(2) / data['half_life']
        N_t = data['initial'] * np.exp(-lambda_decay * time_span)
        
        plt.loglog(time_span, N_t, label=f'{name} (t₁/₂ = {data["half_life"]:.1e} y)')
    
    plt.xlabel('Time (years)')
    plt.ylabel('Number of nuclei')
    plt.title('Radioactive Decay Chain')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

# Thermal analysis
def thermal_analysis():
    """Analyze object cooling under different conditions"""
    t_span = (0, 300)  # 5 hours
    t_eval = np.linspace(0, 300, 1000)
    
    # Different cooling scenarios
    scenarios = [
        {'k': 0.01, 'T_amb': 20, 'label': 'Air cooling'},
        {'k': 0.05, 'T_amb': 20, 'label': 'Water cooling'},
        {'k': 0.02, 'T_amb': 5, 'label': 'Refrigerated air'}
    ]
    
    T0 = 100  # Initial temperature
    
    for scenario in scenarios:
        params = (scenario['k'], scenario['T_amb'])
        sol = solve_ivp(physics.newton_cooling, t_span, [T0], 
                       t_eval=t_eval, args=(params,))
        
        plt.plot(sol.t, sol.y[0], label=scenario['label'], linewidth=2)
    
    plt.axhline(y=37, color='red', linestyle='--', alpha=0.7, 
                label='Body temperature')
    plt.xlabel('Time (minutes)')
    plt.ylabel('Temperature (°C)')
    plt.title('Cooling Analysis: Newton\'s Law')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

# Circuit analysis
def circuit_analysis():
    """Analyze RC and RL circuit responses"""
    t_span = (0, 0.01)  # 10 ms
    t_eval = np.linspace(0, 0.01, 1000)
    
    # RC Circuit
    R, C = 1000, 1e-6  # 1kΩ, 1μF
    V_in = 5  # 5V step input
    params_rc = (R, C, V_in)
    
    sol_rc = solve_ivp(physics.rc_circuit, t_span, [0], 
                      t_eval=t_eval, args=(params_rc,))
    
    # RL Circuit  
    L, R = 0.001, 100  # 1mH, 100Ω
    I_in = 0.05  # 50mA step
    params_rl = (L, R, I_in * R)
    
    sol_rl = solve_ivp(physics.rl_circuit, t_span, [0], 
                      t_eval=t_eval, args=(params_rl,))
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # RC Circuit plot
    ax1.plot(sol_rc.t * 1000, sol_rc.y[0], 'b-', linewidth=2, label='V_C(t)')
    ax1.axhline(y=V_in, color='r', linestyle='--', alpha=0.7, label='V_in')
    ax1.axhline(y=V_in * (1 - 1/np.e), color='g', linestyle=':', 
                alpha=0.7, label='63.2% of V_in')
    ax1.set_xlabel('Time (ms)')
    ax1.set_ylabel('Voltage (V)')
    ax1.set_title(f'RC Circuit (τ = {R*C*1000:.1f} ms)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # RL Circuit plot
    ax2.plot(sol_rl.t * 1000, sol_rl.y[0] * 1000, 'r-', linewidth=2, label='I(t)')
    ax2.axhline(y=I_in * 1000, color='b', linestyle='--', alpha=0.7, label='I_final')
    ax2.axhline(y=I_in * 1000 * (1 - 1/np.e), color='g', linestyle=':', 
                alpha=0.7, label='63.2% of I_final')
    ax2.set_xlabel('Time (ms)')
    ax2.set_ylabel('Current (mA)')
    ax2.set_title(f'RL Circuit (τ = {L/R*1000:.1f} ms)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Run physics simulations
if __name__ == "__main__":
    simulate_decay_chain()
    thermal_analysis()
    circuit_analysis()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R: Physics Analysis
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(plotly)

# Physics modeling class equivalent in R
PhysicsModels <- list(
  
  # Radioactive decay model
  radioactive_decay = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      N <- state[1]
      dNdt <- -lambda * N
      return(list(dNdt))
    })
  },
  
  # Newton's cooling law
  newton_cooling = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      T <- state[1]
      dTdt <- -k * (T - T_ambient)
      return(list(dTdt))
    })
  },
  
  # RC circuit model
  rc_circuit = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      V <- state[1]
      dVdt <- (V_in - V) / (R * C)
      return(list(dVdt))
    })
  },
  
  # RL circuit model
  rl_circuit = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      I <- state[1]
      dIdt <- (V_in - R * I) / L
      return(list(dIdt))
    })
  }
)

# Comprehensive decay analysis
analyze_radioactive_decay <- function() {
  # Different isotopes with their half-lives
  isotopes <- data.frame(
    name = c("C-14", "U-238", "Ra-226", "I-131"),
    half_life = c(5730, 4.468e9, 1600, 8.02),
    units = c("years", "years", "years", "days"),
    lambda = c(log(2)/5730, log(2)/4.468e9, log(2)/1600, log(2)/8.02)
  )
  
  results <- list()
  
  for (i in 1:nrow(isotopes)) {
    parms <- list(lambda = isotopes$lambda[i])
    times <- seq(0, 5 * isotopes$half_life[i], length.out = 1000)
    
    solution <- ode(y = c(N = 1000), times = times, 
                   func = PhysicsModels$radioactive_decay, parms = parms)
    
    results[[isotopes$name[i]]] <- data.frame(
      time = solution[, "time"],
      N = solution[, "N"],
      isotope = isotopes$name[i],
      half_life = isotopes$half_life[i]
    )
  }
  
  # Combine all results
  decay_data <- do.call(rbind, results)
  
  # Create visualization
  p <- ggplot(decay_data, aes(x = time, y = N, color = isotope)) +
    geom_line(size = 1.2) +
    scale_y_log10() +
    scale_x_log10() +
    labs(
      title = "Radioactive Decay Comparison",
      x = "Time (various units)",
      y = "Number of Nuclei (log scale)",
      color = "Isotope"
    ) +
    theme_minimal() +
    theme(legend.position = "bottom")
  
  return(p)
}

# Thermal dynamics analysis
thermal_dynamics_analysis <- function() {
  # Different cooling scenarios
  scenarios <- list(
    air_cooling = list(k = 0.01, T_ambient = 20, material = "Air cooling"),
    water_cooling = list(k = 0.05, T_ambient = 20, material = "Water cooling"),
    liquid_nitrogen = list(k = 0.15, T_ambient = -196, material = "Liquid nitrogen")
  )
  
  times <- seq(0, 300, by = 1)  # 5 hours in minutes
  initial_temp <- 100
  
  results <- list()
  
  for (scenario_name in names(scenarios)) {
    scenario <- scenarios[[scenario_name]]
    parms <- list(k = scenario$k, T_ambient = scenario$T_ambient)
    
    solution <- ode(y = c(T = initial_temp), times = times,
                   func = PhysicsModels$newton_cooling, parms = parms)
    
    results[[scenario_name]] <- data.frame(
      time = solution[, "time"],
      temperature = solution[, "T"],
      scenario = scenario$material
    )
  }
  
  thermal_data <- do.call(rbind, results)
  
  # Create interactive plot
  p <- ggplot(thermal_data, aes(x = time, y = temperature, color = scenario)) +
    geom_line(size = 1.2) +
    geom_hline(yintercept = 20, linetype = "dashed", alpha = 0.7) +
    labs(
      title = "Newton's Law of Cooling - Different Media",
      x = "Time (minutes)",
      y = "Temperature (°C)",
      color = "Cooling Method"
    ) +
    theme_minimal()
  
  return(p)
}

# Statistical physics: Maxwell-Boltzmann distribution
maxwell_boltzmann_analysis <- function(T_values = c(300, 600, 1200)) {
  # Constants
  k_B <- 1.381e-23  # Boltzmann constant
  m <- 4.65e-26     # Mass of nitrogen molecule (kg)
  
  # Velocity range
  v <- seq(0, 2000, by = 10)  # m/s
  
  results <- list()
  
  for (T in T_values) {
    # Maxwell-Boltzmann distribution
    f_v <- 4 * pi * (m / (2 * pi * k_B * T))^(3/2) * v^2 * exp(-m * v^2 / (2 * k_B * T))
    
    results[[paste0("T_", T)]] <- data.frame(
      velocity = v,
      probability_density = f_v,
      temperature = paste(T, "K")
    )
  }
  
  mb_data <- do.call(rbind, results)
  
  p <- ggplot(mb_data, aes(x = velocity, y = probability_density, color = temperature)) +
    geom_line(size = 1.2) +
    labs(
      title = "Maxwell-Boltzmann Velocity Distribution",
      x = "Velocity (m/s)",
      y = "Probability Density",
      color = "Temperature"
    ) +
    theme_minimal()
  
  return(p)
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="example-box mt-6">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-radiation mr-2"></i>Multi-Physics Example: Cooling with Radiation</h3>
                <p class="mb-3">A hot object cooling in space follows both Newton's cooling and Stefan-Boltzmann radiation:</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Combined Model:</h4>
                    <p class="text-sm mb-2">$\frac{dT}{dt} = -k_1(T - T_{\text{amb}}) - k_2(T^4 - T_{\text{amb}}^4)$</p>
                    <p class="text-sm">For small temperature differences: $\frac{dT}{dt} \approx -(k_1 + 4k_2T_{\text{amb}}^3)(T - T_{\text{amb}})$</p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Linear Approximation</h4>
                        <p class="text-sm">Valid when |T - T_amb| << T_amb</p>
                        <p class="text-sm">Effective cooling constant: k_eff = k₁ + 4k₂T³_amb</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Physical Insight</h4>
                        <p class="text-sm">Radiation becomes dominant at high temperatures</p>
                        <p class="text-sm">T⁴ law makes cooling faster for hot objects</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Clinical Pharmacology -->
        <section id="pharmacology" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-pills text-green-600 mr-3"></i>Clinical Pharmacological Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-heartbeat mr-2"></i>Pharmacokinetic Modeling with First-Order ODEs</h3>
                <p class="mb-4">Pharmacokinetics describes how drugs move through the body using ADME processes: Absorption, Distribution, Metabolism, and Elimination. These processes often follow first-order kinetics.</p>

                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">First-Order Elimination</h4>
                        <p class="text-sm mb-2">$\frac{dC}{dt} = -k_e C$</p>
                        <p class="text-xs">Rate proportional to concentration</p>
                        <p class="text-xs mt-1">Half-life: $t_{1/2} = \frac{\ln(2)}{k_e}$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">One-Compartment Model</h4>
                        <p class="text-sm mb-2">$\frac{dA}{dt} = -k_e A$</p>
                        <p class="text-xs">A = amount in body</p>
                        <p class="text-xs mt-1">$C(t) = C_0 e^{-k_e t}$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Clearance Concept</h4>
                        <p class="text-sm mb-2">$CL = k_e \cdot V_d$</p>
                        <p class="text-xs">Volume cleared per unit time</p>
                        <p class="text-xs mt-1">$k_e = \frac{CL}{V_d}$</p>
                    </div>
                </div>
            </div>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-flask mr-2"></i>Mathematical Framework for Pharmacokinetics</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-3">Single-Compartment Model</h4>
                        <div class="bg-white bg-opacity-20 p-4 rounded mb-3">
                            <p class="text-sm mb-2"><strong>Mass Balance Equation:</strong></p>
                            <p class="text-center">$$V_d \frac{dC}{dt} = \text{Input Rate} - CL \cdot C$$</p>
                            <p class="text-sm mt-2">Where:</p>
                            <ul class="text-xs space-y-1">
                                <li>$V_d$ = Volume of distribution</li>
                                <li>$C$ = Plasma concentration</li>
                                <li>$CL$ = Total body clearance</li>
                            </ul>
                        </div>

                        <div class="bg-white bg-opacity-20 p-4 rounded">
                            <p class="text-sm mb-2"><strong>IV Bolus Solution:</strong></p>
                            <p class="text-center">$$C(t) = \frac{D}{V_d} e^{-k_e t}$$</p>
                            <p class="text-sm mt-2">Where $k_e = \frac{CL}{V_d}$ and $D$ = dose</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-bold mb-3">First-Order Absorption</h4>
                        <div class="bg-white bg-opacity-20 p-4 rounded mb-3">
                            <p class="text-sm mb-2"><strong>Absorption + Elimination:</strong></p>
                            <p class="text-center">$$\frac{dC}{dt} = \frac{k_a F D}{V_d} e^{-k_a t} - k_e C$$</p>
                            <p class="text-sm mt-2">Where:</p>
                            <ul class="text-xs space-y-1">
                                <li>$k_a$ = Absorption rate constant</li>
                                <li>$F$ = Bioavailability fraction</li>
                                <li>$D$ = Oral dose</li>
                            </ul>
                        </div>

                        <div class="bg-white bg-opacity-20 p-4 rounded">
                            <p class="text-sm mb-2"><strong>Analytical Solution:</strong></p>
                            <p class="text-center">$$C(t) = \frac{k_a F D}{V_d(k_a - k_e)} (e^{-k_e t} - e^{-k_a t})$$</p>
                            <p class="text-sm mt-2">Peak time: $t_{max} = \frac{\ln(k_a/k_e)}{k_a - k_e}$</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Clinical Example: Digoxin Pharmacokinetics</h3>
                <p class="mb-3">Digoxin is a cardiac glycoside with well-characterized first-order elimination kinetics. Model a 0.25 mg IV dose in a 70 kg patient.</p>

                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Patient Parameters:</h4>
                    <div class="grid md:grid-cols-2 gap-4">
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Weight: 70 kg</li>
                            <li>Dose: 0.25 mg IV</li>
                            <li>Volume of distribution: 7 L/kg</li>
                            <li>Clearance: 1.2 mL/min/kg</li>
                        </ul>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>$V_d = 7 \times 70 = 490$ L</li>
                            <li>$CL = 1.2 \times 70 = 84$ mL/min = 5.04 L/h</li>
                            <li>$k_e = \frac{5.04}{490} = 0.0103$ h⁻¹</li>
                            <li>$t_{1/2} = \frac{0.693}{0.0103} = 67.3$ hours</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Concentration-Time Profile:</h4>
                    <p class="text-center">$$C(t) = \frac{0.25 \text{ mg}}{490 \text{ L}} e^{-0.0103t} = 0.51 \times e^{-0.0103t} \text{ ng/mL}$$</p>
                    <p class="text-sm mt-2"><strong>Clinical Targets:</strong></p>
                    <ul class="text-sm space-y-1">
                        <li>Therapeutic range: 1.0-2.0 ng/mL</li>
                        <li>Toxic level: >2.5 ng/mL</li>
                        <li>Time to steady state: ~5 × t₁/₂ = 337 hours (14 days)</li>
                    </ul>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-green-600 mr-2"></i>Pharmacokinetic Visualization Dashboard
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="pharmacologyIVChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="pharmacologyOralChart"></canvas>
                    </div>
                </div>
                <div class="chart-container mt-6">
                    <canvas id="pharmacologyMultiDoseChart"></canvas>
                </div>
                <div class="mt-4 grid md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded">
                        <strong>IV Bolus Model:</strong><br>
                        Immediate distribution<br>
                        First-order elimination<br>
                        Exponential decay
                    </div>
                    <div class="bg-green-50 p-3 rounded">
                        <strong>Oral Absorption:</strong><br>
                        Flip-flop kinetics possible<br>
                        Peak concentration time<br>
                        Bioavailability effects
                    </div>
                    <div class="bg-orange-50 p-3 rounded">
                        <strong>Multiple Dosing:</strong><br>
                        Accumulation to steady state<br>
                        Loading dose calculations<br>
                        Dosing interval optimization
                    </div>
                </div>
            </div>
        </section>

        <!-- Engineering Systems -->
        <section id="engineering" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-orange-600 mr-3"></i>Engineering Systems
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-microchip mr-2"></i>Control Systems & Signal Processing</h3>
                <p class="mb-4">Engineering systems frequently use first-order models for analysis, design, and control of dynamic systems.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">First-Order System</h4>
                        <p class="text-sm mb-2">$\tau\frac{dy}{dt} + y = Ku(t)$</p>
                        <p class="text-sm">τ = time constant, K = gain</p>
                        <p class="text-xs mt-1">Transfer function: G(s) = K/(τs + 1)</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Step Response</h4>
                        <p class="text-sm mb-2">$y(t) = K(1 - e^{-t/\tau})$</p>
                        <p class="text-sm">Time to 63.2%: t = τ</p>
                        <p class="text-xs mt-1">Settling time (2%): t ≈ 4τ</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-tools text-orange-600 mr-2"></i>Engineering System Analysis
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="engineeringStepChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="engineeringFrequencyChart"></canvas>
                    </div>
                </div>
                <div class="chart-container mt-6">
                    <canvas id="engineeringControlChart"></canvas>
                </div>
            </div>
            
            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python: Engineering Control Systems
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.integrate import solve_ivp
from scipy.optimize import minimize

class ControlSystems:
    def __init__(self):
        pass
    
    def first_order_system(self, t, y, params):
        """First-order system: τ dy/dt + y = K*u"""
        tau, K, u = params
        return (K * u - y) / tau
    
    def pid_controller(self, error, dt, kp, ki, kd, 
                      integral_sum, last_error):
        """PID controller implementation"""
        integral_sum += error * dt
        derivative = (error - last_error) / dt if dt > 0 else 0
        output = kp * error + ki * integral_sum + kd * derivative
        return output, integral_sum
    
    def transfer_function_analysis(self, num, den, system_name):
        """Analyze transfer function properties"""
        sys = signal.TransferFunction(num, den)
        
        # Step response
        t, y = signal.step(sys)
        
        # Frequency response
        w, mag, phase = signal.bode(sys)
        
        # System properties
        poles = np.roots(den)
        zeros = np.roots(num) if len(num) > 1 else []
        
        print(f"\n{system_name} Analysis:")
        print(f"Poles: {poles}")
        print(f"Zeros: {zeros}")
        print(f"DC Gain: {sys.dcgain}")
        
        return t, y, w, mag, phase

# Create control systems instance
control = ControlSystems()

# System identification example
def system_identification():
    """Identify system parameters from step response data"""
    # Generate synthetic data with noise
    true_K, true_tau = 2.5, 1.2
    t = np.linspace(0, 10, 100)
    
    # True step response
    y_true = true_K * (1 - np.exp(-t / true_tau))
    
    # Add noise
    np.random.seed(42)
    y_noisy = y_true + np.random.normal(0, 0.1, len(y_true))
    
    # Objective function for parameter estimation
    def objective(params):
        K_est, tau_est = params
        y_est = K_est * (1 - np.exp(-t / tau_est))
        return np.sum((y_noisy - y_est)**2)
    
    # Initial guess
    initial_guess = [2.0, 1.0]
    
    # Optimize
    result = minimize(objective, initial_guess, method='BFGS')
    K_est, tau_est = result.x
    
    print(f"True parameters: K = {true_K}, τ = {true_tau}")
    print(f"Estimated parameters: K = {K_est:.3f}, τ = {tau_est:.3f}")
    
    # Plot results
    y_estimated = K_est * (1 - np.exp(-t / tau_est))
    
    plt.figure(figsize=(10, 6))
    plt.plot(t, y_true, 'g-', linewidth=2, label='True System')
    plt.plot(t, y_noisy, 'bo', markersize=4, alpha=0.6, label='Noisy Data')
    plt.plot(t, y_estimated, 'r--', linewidth=2, label='Identified Model')
    plt.xlabel('Time (s)')
    plt.ylabel('Output')
    plt.title('System Identification Example')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

# Control system design
def control_system_design():
    """Design PID controller for a first-order system"""
    # Plant: G(s) = 2 / (3s + 1)
    num_plant = [2]
    den_plant = [3, 1]
    
    # Different controller gains
    controllers = [
        {'kp': 1, 'ki': 0, 'kd': 0, 'name': 'P Controller'},
        {'kp': 1, 'ki': 0.5, 'kd': 0, 'name': 'PI Controller'},
        {'kp': 1, 'ki': 0.5, 'kd': 0.1, 'name': 'PID Controller'}
    ]
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    for i, controller in enumerate(controllers):
        kp, ki, kd = controller['kp'], controller['ki'], controller['kd']
        
        # Closed-loop transfer function
        # Controller: C(s) = kp + ki/s + kd*s
        num_controller = [kd, kp, ki]
        den_controller = [1, 0]
        
        # Series connection: C(s) * G(s)
        num_open = np.convolve(num_controller, num_plant)
        den_open = np.convolve(den_controller, den_plant)
        
        # Closed-loop: T(s) = C(s)*G(s) / (1 + C(s)*G(s))
        num_closed = num_open
        den_closed = np.polyadd(den_open, num_open)
        
        # Step response
        sys_closed = signal.TransferFunction(num_closed, den_closed)
        t, y = signal.step(sys_closed)
        
        # Plot step response
        ax = axes[0, 0] if i < 2 else axes[1, 0]
        if i == 0:
            ax.clear()
        ax.plot(t, y, linewidth=2, label=controller['name'])
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Output')
        ax.set_title('Step Response Comparison')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Root locus (simplified)
        poles_cl = np.roots(den_closed)
        ax = axes[0, 1] if i < 2 else axes[1, 1]
        if i == 0:
            ax.clear()
        ax.plot(np.real(poles_cl), np.imag(poles_cl), 'o', 
                markersize=8, label=controller['name'])
        ax.set_xlabel('Real Part')
        ax.set_ylabel('Imaginary Part')
        ax.set_title('Closed-Loop Poles')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    # Frequency response comparison
    w = np.logspace(-2, 2, 1000)
    for i, controller in enumerate(controllers):
        kp, ki, kd = controller['kp'], controller['ki'], controller['kd']
        
        # Open-loop frequency response
        s = 1j * w
        C_s = kp + ki / s + kd * s
        G_s = 2 / (3 * s + 1)
        L_s = C_s * G_s
        
        magnitude_db = 20 * np.log10(np.abs(L_s))
        phase_deg = np.angle(L_s) * 180 / np.pi
        
        axes[0, 1].semilogx(w, magnitude_db, linewidth=2, 
                           label=controller['name'])
        axes[1, 1].semilogx(w, phase_deg, linewidth=2)
    
    axes[0, 1].set_xlabel('Frequency (rad/s)')
    axes[0, 1].set_ylabel('Magnitude (dB)')
    axes[0, 1].set_title('Bode Plot - Magnitude')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    axes[1, 1].set_xlabel('Frequency (rad/s)')
    axes[1, 1].set_ylabel('Phase (degrees)')
    axes[1, 1].set_title('Bode Plot - Phase')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Process control simulation
def process_control_simulation():
    """Simulate a tank level control system"""
    def tank_model(t, state, params):
        h, q_in = state
        A, K_v, K_p, h_ref = params
        
        # Tank dynamics
        q_out = K_v * np.sqrt(max(h, 0))  # Avoid negative sqrt
        
        # Controller (PI controller for level)
        error = h_ref - h
        q_in_new = K_p * error  # Simplified P controller
        
        dhdt = (q_in - q_out) / A
        dq_in_dt = 0  # Assuming instantaneous valve response
        
        return [dhdt, dq_in_dt]
    
    # Parameters
    A = 10      # Tank cross-sectional area (m²)
    K_v = 2     # Valve coefficient
    K_p = 5     # Controller gain
    h_ref = 2   # Reference level (m)
    
    params = (A, K_v, K_p, h_ref)
    
    # Initial conditions
    h0 = 0.5    # Initial level
    q_in0 = 0   # Initial inflow
    
    # Time span
    t_span = (0, 50)
    t_eval = np.linspace(0, 50, 1000)
    
    # Solve
    sol = solve_ivp(tank_model, t_span, [h0, q_in0], 
                   t_eval=t_eval, args=(params,))
    
    # Plot results
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    ax1.plot(sol.t, sol.y[0], 'b-', linewidth=2, label='Tank Level')
    ax1.axhline(y=h_ref, color='r', linestyle='--', 
                alpha=0.7, label='Reference Level')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Level (m)')
    ax1.set_title('Tank Level Control')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    ax2.plot(sol.t, sol.y[1], 'g-', linewidth=2, label='Inflow Rate')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Flow Rate (m³/s)')
    ax2.set_title('Controller Output')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Run engineering examples
if __name__ == "__main__":
    system_identification()
    control_system_design()
    process_control_simulation()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R: Engineering Systems Analysis
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(signal)
library(pracma)

# Engineering Systems Analysis in R
EngineeringSystems <- list(
  
  # First-order system model
  first_order_system = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      y <- state[1]
      dydt <- (K * u - y) / tau
      return(list(dydt))
    })
  },
  
  # Tank level control model
  tank_level_control = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      h <- state[1]
      
      # Outflow (nonlinear)
      q_out <- K_v * sqrt(max(h, 0))
      
      # Controller (PI)
      error <- h_ref - h
      q_in <- K_p * error + K_i * integral_error
      
      # Tank dynamics
      dhdt <- (q_in - q_out) / A
      
      return(list(dhdt))
    })
  },
  
  # Heat exchanger model
  heat_exchanger = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      T_out <- state[1]
      
      # Energy balance
      # m*Cp*(dT/dt) = U*A*(T_in - T_out) - m*Cp*F*(T_out - T_ambient)
      dTdt <- (U * A * (T_in - T_out) - m * Cp * F * (T_out - T_ambient)) / (m * Cp)
      
      return(list(dTdt))
    })
  }
)

# System identification using step response
system_identification_R <- function() {
  # Generate synthetic step response data
  time <- seq(0, 10, by = 0.1)
  true_K <- 2.5
  true_tau <- 1.5
  
  # True response
  y_true <- true_K * (1 - exp(-time / true_tau))
  
  # Add noise
  set.seed(42)
  noise <- rnorm(length(time), 0, 0.1)
  y_measured <- y_true + noise
  
  # Parameter estimation using optimization
  objective_function <- function(params) {
    K_est <- params[1]
    tau_est <- params[2]
    y_est <- K_est * (1 - exp(-time / tau_est))
    return(sum((y_measured - y_est)^2))
  }
  
  # Optimize
  result <- optim(c(2, 1), objective_function, method = "BFGS")
  K_est <- result$par[1]
  tau_est <- result$par[2]
  
  # Generate estimated response
  y_estimated <- K_est * (1 - exp(-time / tau_est))
  
  # Create comparison plot
  identification_data <- data.frame(
    time = rep(time, 3),
    response = c(y_true, y_measured, y_estimated),
    type = rep(c("True System", "Measured Data", "Identified Model"), 
               each = length(time))
  )
  
  p <- ggplot(identification_data, aes(x = time, y = response, color = type)) +
    geom_line(data = subset(identification_data, type != "Measured Data"), 
              size = 1.2) +
    geom_point(data = subset(identification_data, type == "Measured Data"), 
               alpha = 0.6) +
    labs(
      title = paste("System Identification: K =", round(K_est, 3), 
                   ", τ =", round(tau_est, 3)),
      x = "Time (s)",
      y = "Response",
      color = "Data Type"
    ) +
    theme_minimal()
  
  return(p)
}

# Control system design and analysis
control_system_design_R <- function() {
  # Plant: G(s) = 2/(3s + 1)
  # Different controller types
  
  controllers <- list(
    P_controller = list(Kp = 1, Ki = 0, Kd = 0),
    PI_controller = list(Kp = 1, Ki = 0.5, Kd = 0),
    PID_controller = list(Kp = 1, Ki = 0.5, Kd = 0.2)
  )
  
  time <- seq(0, 15, by = 0.1)
  results <- list()
  
  for (controller_name in names(controllers)) {
    controller <- controllers[[controller_name]]
    
    # Simulate closed-loop step response
    # For simplification, use approximate closed-loop response
    Kp <- controller$Kp
    Ki <- controller$Ki
    Kd <- controller$Kd
    
    # Plant parameters
    K_plant <- 2
    tau_plant <- 3
    
    # Approximate closed-loop parameters (simplified)
    K_cl <- (K_plant * Kp) / (1 + K_plant * Kp)
    tau_cl <- tau_plant / (1 + K_plant * Kp)
    
    # Step response
    if (Ki == 0) {
      # P or PD controller
      response <- K_cl * (1 - exp(-time / tau_cl))
    } else {
      # PI or PID controller (simplified)
      response <- 1 - exp(-time / tau_cl)  # Unit step response
    }
    
    results[[controller_name]] <- data.frame(
      time = time,
      response = response,
      controller = controller_name
    )
  }
  
  # Combine results
  step_response_data <- do.call(rbind, results)
  
  # Create step response comparison
  p1 <- ggplot(step_response_data, aes(x = time, y = response, color = controller)) +
    geom_line(size = 1.2) +
    geom_hline(yintercept = 1, linetype = "dashed", alpha = 0.7) +
    labs(
      title = "Closed-Loop Step Response Comparison",
      x = "Time (s)",
      y = "Output",
      color = "Controller Type"
    ) +
    theme_minimal()
  
  return(p1)
}

# Process dynamics simulation
process_dynamics_simulation <- function() {
  # CSTR (Continuous Stirred Tank Reactor) model
  # Simplified temperature control
  
  cstr_model <- function(t, state, parms) {
    with(as.list(c(state, parms)), {
      T <- state[1]  # Temperature
      
      # Heat balance
      # V*rho*Cp*(dT/dt) = F*rho*Cp*(T_in - T) + Q_reaction - Q_cooling
      
      # Cooling control (proportional)
      T_setpoint <- 350  # K
      error <- T_setpoint - T
      Q_cooling <- Kc * error
      
      # Reaction heat (temperature dependent)
      Q_reaction <- Q0 * exp(-Ea / (R * T))
      
      # Energy balance
      dTdt <- (F * rho * Cp * (T_in - T) + Q_reaction - Q_cooling) / (V * rho * Cp)
      
      return(list(dTdt))
    })
  }
  
  # Parameters
  parms <- list(
    V = 1.0,          # Volume (m³)
    F = 0.1,          # Flow rate (m³/s)
    rho = 1000,       # Density (kg/m³)
    Cp = 4180,        # Heat capacity (J/kg/K)
    T_in = 300,       # Inlet temperature (K)
    Q0 = 1e6,         # Pre-exponential factor for reaction
    Ea = 5e4,         # Activation energy (J/mol)
    R = 8.314,        # Gas constant (J/mol/K)
    Kc = 1e4          # Controller gain
  )
  
  # Initial conditions
  T0 <- 320  # Initial temperature (K)
  
  # Time span
  times <- seq(0, 1000, by = 1)
  
  # Solve ODE
  solution <- ode(y = c(T = T0), times = times, 
                 func = cstr_model, parms = parms)
  
  # Create visualization
  process_data <- data.frame(
    time = solution[, "time"],
    temperature = solution[, "T"],
    setpoint = 350
  )
  
  p <- ggplot(process_data, aes(x = time)) +
    geom_line(aes(y = temperature, color = "Process Temperature"), size = 1.2) +
    geom_line(aes(y = setpoint, color = "Setpoint"), 
              linetype = "dashed", size = 1) +
    labs(
      title = "CSTR Temperature Control",
      x = "Time (s)",
      y = "Temperature (K)",
      color = "Variable"
    ) +
    theme_minimal()
  
  return(p)
}

# Frequency response analysis
frequency_response_analysis <- function() {
  # Analyze frequency response of different first-order systems
  
  # Frequency range
  omega <- 10^seq(-2, 2, length.out = 1000)
  
  # Different time constants
  systems <- list(
    fast = list(tau = 0.1, K = 1, name = "Fast System (τ = 0.1s)"),
    medium = list(tau = 1.0, K = 1, name = "Medium System (τ = 1.0s)"),
    slow = list(tau = 10.0, K = 1, name = "Slow System (τ = 10.0s)")
  )
  
  results <- list()
  
  for (system_name in names(systems)) {
    system <- systems[[system_name]]
    
    # Transfer function: G(jω) = K / (τjω + 1)
    s <- 1i * omega
    G_jw <- system$K / (system$tau * s + 1)
    
    magnitude_db <- 20 * log10(abs(G_jw))
    phase_deg <- Arg(G_jw) * 180 / pi
    
    results[[system_name]] <- data.frame(
      frequency = omega,
      magnitude_db = magnitude_db,
      phase_deg = phase_deg,
      system = system$name
    )
  }
  
  # Combine results
  freq_response_data <- do.call(rbind, results)
  
  # Create Bode plots
  p1 <- ggplot(freq_response_data, aes(x = frequency, y = magnitude_db, color = system)) +
    geom_line(size = 1.2) +
    scale_x_log10() +
    labs(
      title = "Bode Plot - Magnitude",
      x = "Frequency (rad/s)",
      y = "Magnitude (dB)",
      color = "System"
    ) +
    theme_minimal()
  
  p2 <- ggplot(freq_response_data, aes(x = frequency, y = phase_deg, color = system)) +
    geom_line(size = 1.2) +
    scale_x_log10() +
    labs(
      title = "Bode Plot - Phase",
      x = "Frequency (rad/s)",
      y = "Phase (degrees)",
      color = "System"
    ) +
    theme_minimal()
  
  return(list(magnitude = p1, phase = p2))
}

# Execute all engineering analyses
identification_plot <- system_identification_R()
control_plot <- control_system_design_R()
process_plot <- process_dynamics_simulation()
freq_plots <- frequency_response_analysis()

# Display plots
print(identification_plot)
print(control_plot)
print(process_plot)
print(freq_plots$magnitude)
print(freq_plots$phase)</code></pre>
                    </div>
                </div>
            </div>

            <div class="example-box mt-6">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-industry mr-2"></i>Process Control Example: Tank Level Control</h3>
                <p class="mb-3">A liquid storage tank with proportional level control demonstrates first-order system behavior:</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">System Model:</h4>
                    <p class="text-sm mb-2">$A\frac{dh}{dt} = q_{in} - q_{out}$</p>
                    <p class="text-sm mb-2">$q_{out} = K_v\sqrt{h}$ (valve flow)</p>
                    <p class="text-sm">Linearized: $\frac{dh}{dt} + \frac{K_v}{2A\sqrt{h_0}}h = \frac{q_{in}}{A}$</p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Control Strategy</h4>
                        <p class="text-sm">Proportional control: q_in = K_p(h_ref - h)</p>
                        <p class="text-sm">Closed-loop time constant depends on K_p</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Design Considerations</h4>
                        <p class="text-sm">Faster response vs. stability trade-off</p>
                        <p class="text-sm">Tank size affects natural time constant</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Advanced Analysis -->
        <section id="advanced" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-microscope text-red-600 mr-3"></i>Advanced Analysis Techniques
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-brain mr-2"></i>Qualitative Analysis</h3>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-bold mb-2">Equilibrium Points</h4>
                        <p class="text-sm mb-2">Solutions where $\frac{dy}{dt} = 0$</p>
                        <ul class="text-sm space-y-1">
                            <li>• Stable: Solutions approach equilibrium</li>
                            <li>• Unstable: Solutions move away</li>
                            <li>• Semi-stable: One-sided stability</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Phase Line Analysis</h4>
                        <p class="text-sm mb-2">Graphical representation of solution behavior</p>
                        <ul class="text-sm space-y-1">
                            <li>• Direction field arrows</li>
                            <li>• Equilibrium classification</li>
                            <li>• Long-term behavior prediction</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-project-diagram text-red-600 mr-2"></i>Advanced Analysis Visualizations
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="advancedPhaseChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="advancedStabilityChart"></canvas>
                    </div>
                </div>
                <div class="large-chart mt-6">
                    <canvas id="advancedDirectionChart"></canvas>
                </div>
            </div>

            <div class="example-box mt-6">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-search mr-2"></i>Bifurcation Analysis</h3>
                <p class="mb-3">Study how equilibrium behavior changes with parameter variation:</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Example: Harvesting Model</h4>
                    <p class="text-sm mb-2">$\frac{dP}{dt} = rP(1 - \frac{P}{K}) - H$</p>
                    <p class="text-sm">P = population, r = growth rate, K = carrying capacity, H = harvest rate</p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Critical Harvest Rate</h4>
                        <p class="text-sm">$H_{max} = \frac{rK}{4}$ (maximum sustainable harvest)</p>
                        <p class="text-sm">Two equilibria exist for H < H_max</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Saddle-Node Bifurcation</h4>
                        <p class="text-sm">At H = H_max, equilibria collide and disappear</p>
                        <p class="text-sm">Population crashes for H > H_max</p>
                    </div>
                </div>
            </div>

            <div class="chart-container mt-6">
                <canvas id="advancedBifurcationChart"></canvas>
            </div>
            
            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python: Advanced Analysis
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import fsolve, brentq
from matplotlib.patches import Circle
import sympy as sp

class AdvancedODEAnalysis:
    def __init__(self):
        pass
    
    def phase_line_analysis(self, f, y_range, title="Phase Line Analysis"):
        """Analyze phase line for autonomous ODE dy/dt = f(y)"""
        y = np.linspace(y_range[0], y_range[1], 1000)
        dydt = f(y)
        
        # Find equilibria (zeros of f(y))
        equilibria = []
        sign_changes = np.where(np.diff(np.sign(dydt)))[0]
        
        for i in sign_changes:
            try:
                eq = brentq(f, y[i], y[i+1])
                equilibria.append(eq)
            except:
                pass
        
        # Classify equilibria stability
        stable_eq = []
        unstable_eq = []
        
        for eq in equilibria:
            # Check derivative at equilibrium point
            h = 1e-6
            fprime = (f(eq + h) - f(eq - h)) / (2 * h)
            if fprime < 0:
                stable_eq.append(eq)
            else:
                unstable_eq.append(eq)
        
        # Plot phase line
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Plot dy/dt vs y
        ax1.plot(y, dydt, 'b-', linewidth=2, label='dy/dt = f(y)')
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)
        
        # Mark equilibria
        for eq in stable_eq:
            ax1.plot(eq, 0, 'go', markersize=10, label=f'Stable: y = {eq:.2f}')
        for eq in unstable_eq:
            ax1.plot(eq, 0, 'ro', markersize=10, label=f'Unstable: y = {eq:.2f}')
        
        ax1.set_xlabel('y')
        ax1.set_ylabel('dy/dt')
        ax1.set_title(title)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Phase line with direction arrows
        y_line = np.linspace(y_range[0], y_range[1], 20)
        dydt_line = f(y_line)
        arrows = np.sign(dydt_line)
        
        ax2.scatter(np.zeros_like(y_line), y_line, c=arrows, 
                   cmap='RdYlBu', s=100, alpha=0.7)
        
        # Mark equilibria on phase line
        for eq in stable_eq:
            ax2.plot(0, eq, 'go', markersize=15, label=f'Stable')
        for eq in unstable_eq:
            ax2.plot(0, eq, 'rs', markersize=15, label=f'Unstable')
        
        ax2.set_xlim(-0.5, 0.5)
        ax2.set_ylim(y_range)
        ax2.set_ylabel('y')
        ax2.set_title('Phase Line with Flow Direction')
        ax2.legend()
        
        plt.tight_layout()
        plt.show()
        
        return equilibria, stable_eq, unstable_eq
    
    def bifurcation_analysis(self, f, param_range, param_name='r'):
        """Analyze bifurcations as parameter varies"""
        params = np.linspace(param_range[0], param_range[1], 200)
        equilibria = []
        stability = []
        
        for p in params:
            # Find equilibria for this parameter value
            eq_for_p = []
            stab_for_p = []
            
            # Search for equilibria in reasonable range
            y_search = np.linspace(-10, 10, 1000)
            try:
                f_vals = f(y_search, p)
                sign_changes = np.where(np.diff(np.sign(f_vals)))[0]
                
                for i in sign_changes:
                    try:
                        eq = brentq(lambda y: f(y, p), y_search[i], y_search[i+1])
                        eq_for_p.append(eq)
                        
                        # Check stability
                        h = 1e-6
                        fprime = (f(eq + h, p) - f(eq - h, p)) / (2 * h)
                        stab_for_p.append(fprime < 0)  # True if stable
                    except:
                        pass
            except:
                pass
            
            equilibria.append(eq_for_p)
            stability.append(stab_for_p)
        
        # Plot bifurcation diagram
        plt.figure(figsize=(12, 8))
        
        for i, (eqs, stabs) in enumerate(zip(equilibria, stability)):
            p = params[i]
            for eq, stable in zip(eqs, stabs):
                if stable:
                    plt.plot(p, eq, 'b.', markersize=2, alpha=0.7)
                else:
                    plt.plot(p, eq, 'r.', markersize=2, alpha=0.7)
        
        plt.xlabel(f'Parameter {param_name}')
        plt.ylabel('Equilibrium y')
        plt.title('Bifurcation Diagram')
        plt.grid(True, alpha=0.3)
        
        # Add legend
        plt.plot([], [], 'b.', markersize=10, label='Stable')
        plt.plot([], [], 'r.', markersize=10, label='Unstable')
        plt.legend()
        
        plt.show()
        
        return params, equilibria, stability
    
    def direction_field(self, f, x_range, y_range, title="Direction Field"):
        """Plot direction field for dy/dx = f(x, y)"""
        x = np.linspace(x_range[0], x_range[1], 20)
        y = np.linspace(y_range[0], y_range[1], 20)
        X, Y = np.meshgrid(x, y)
        
        # Calculate slopes
        DY = f(X, Y)
        DX = np.ones_like(DY)
        
        # Normalize arrows
        M = np.sqrt(DX**2 + DY**2)
        M[M == 0] = 1  # Avoid division by zero
        DX_norm = DX / M
        DY_norm = DY / M
        
        plt.figure(figsize=(10, 8))
        plt.quiver(X, Y, DX_norm, DY_norm, M, alpha=0.7, cmap='viridis')
        plt.colorbar(label='Slope magnitude')
        plt.xlabel('x')
        plt.ylabel('y')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        plt.show()
    
    def solution_trajectories(self, f, x_range, y_range, initial_conditions):
        """Plot solution trajectories"""
        def ode_system(x, y):
            return f(x, y)
        
        plt.figure(figsize=(12, 8))
        
        # Plot direction field
        x = np.linspace(x_range[0], x_range[1], 15)
        y = np.linspace(y_range[0], y_range[1], 15)
        X, Y = np.meshgrid(x, y)
        DY = f(X, Y)
        DX = np.ones_like(DY)
        M = np.sqrt(DX**2 + DY**2)
        M[M == 0] = 1
        plt.quiver(X, Y, DX/M, DY/M, alpha=0.3, color='gray')
        
        # Plot trajectories
        x_eval = np.linspace(x_range[0], x_range[1], 1000)
        
        for i, y0 in enumerate(initial_conditions):
            try:
                sol = solve_ivp(lambda x, y: [f(x, y[0])], 
                              x_range, [y0], t_eval=x_eval, 
                              dense_output=True)
                
                plt.plot(sol.t, sol.y[0], linewidth=2, 
                        label=f'y(0) = {y0}')
            except:
                print(f"Could not solve for initial condition y0 = {y0}")
        
        plt.xlabel('x')
        plt.ylabel('y')
        plt.title('Solution Trajectories')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

# Example usage and analysis
analyzer = AdvancedODEAnalysis()

# Example 1: Logistic equation with harvesting
def logistic_harvesting(y, r=1, K=10, H=2):
    """dy/dt = ry(1 - y/K) - H"""
    return r * y * (1 - y/K) - H

# Phase line analysis
print("Analyzing logistic equation with harvesting...")
y_range = (-2, 12)
equilibria, stable, unstable = analyzer.phase_line_analysis(
    lambda y: logistic_harvesting(y), y_range, 
    "Logistic Growth with Harvesting"
)

# Bifurcation analysis (varying harvest rate H)
def logistic_H_param(y, H):
    return logistic_harvesting(y, H=H)

H_range = (0, 5)
params, eqs, stabs = analyzer.bifurcation_analysis(
    logistic_H_param, H_range, 'H (Harvest Rate)'
)

# Example 2: Van der Pol oscillator (first-order form)
def van_der_pol_1st(x, y, mu=1):
    """First-order form of van der Pol: dy/dx = mu(1 - y²)"""
    return mu * (1 - y**2)

# Direction field
analyzer.direction_field(
    lambda x, y: van_der_pol_1st(x, y), 
    (-5, 5), (-3, 3), 
    "Van der Pol Equation (First Order)"
)

# Solution trajectories
initial_conditions = [-2, -1, 0, 1, 2]
analyzer.solution_trajectories(
    lambda x, y: van_der_pol_1st(x, y),
    (0, 10), (-3, 3), initial_conditions
)

# Numerical continuation for parameter studies
def parameter_continuation():
    """Study how solutions change with parameter variation"""
    mu_values = np.linspace(0.1, 3, 20)
    final_values = []
    
    for mu in mu_values:
        # Solve ODE for fixed time
        sol = solve_ivp(
            lambda t, y: [van_der_pol_1st(t, y[0], mu)],
            (0, 20), [1], t_eval=[20]
        )
        final_values.append(sol.y[0][-1])
    
    plt.figure(figsize=(10, 6))
    plt.plot(mu_values, final_values, 'bo-', linewidth=2, markersize=8)
    plt.xlabel('Parameter μ')
    plt.ylabel('Final Value y(20)')
    plt.title('Parameter Continuation Study')
    plt.grid(True, alpha=0.3)
    plt.show()

parameter_continuation()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R: Advanced Analysis
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(phaseR)
library(rootSolve)

# Advanced ODE Analysis Tools in R
AdvancedAnalysis <- list(
  
  # Phase line analysis function
  phase_line_analysis = function(f, y_range, n_points = 1000) {
    y_vals <- seq(y_range[1], y_range[2], length.out = n_points)
    dydt_vals <- f(y_vals)
    
    # Find equilibria (sign changes)
    sign_changes <- which(diff(sign(dydt_vals)) != 0)
    equilibria <- c()
    
    for (i in sign_changes) {
      try({
        eq <- uniroot(f, c(y_vals[i], y_vals[i+1]))$root
        equilibria <- c(equilibria, eq)
      }, silent = TRUE)
    }
    
    # Create phase line data
    phase_data <- data.frame(
      y = y_vals,
      dydt = dydt_vals
    )
    
    return(list(data = phase_data, equilibria = equilibria))
  },
  
  # Bifurcation analysis
  bifurcation_analysis = function(f, param_range, n_params = 100) {
    params <- seq(param_range[1], param_range[2], length.out = n_params)
    bifurcation_data <- data.frame()
    
    for (p in params) {
      # Find equilibria for this parameter
      f_param <- function(y) f(y, p)
      
      # Search for equilibria in reasonable range
      y_search <- seq(-10, 10, by = 0.1)
      equilibria <- c()
      
      for (i in 1:(length(y_search)-1)) {
        if (sign(f_param(y_search[i])) != sign(f_param(y_search[i+1]))) {
          try({
            eq <- uniroot(f_param, c(y_search[i], y_search[i+1]))$root
            equilibria <- c(equilibria, eq)
          }, silent = TRUE)
        }
      }
      
      # Check stability of equilibria
      for (eq in equilibria) {
        # Numerical derivative for stability
        h <- 1e-6
        fprime <- (f_param(eq + h) - f_param(eq - h)) / (2 * h)
        stable <- fprime < 0
        
        bifurcation_data <- rbind(bifurcation_data, 
                                data.frame(parameter = p, 
                                         equilibrium = eq, 
                                         stable = stable))
      }
    }
    
    return(bifurcation_data)
  },
  
  # Stability analysis
  stability_analysis = function(f, equilibria) {
    stability_results <- data.frame()
    
    for (eq in equilibria) {
      # Numerical derivative at equilibrium
      h <- 1e-6
      fprime <- (f(eq + h) - f(eq - h)) / (2 * h)
      
      stability_type <- ifelse(fprime < 0, "Stable", 
                             ifelse(fprime > 0, "Unstable", "Marginal"))
      
      stability_results <- rbind(stability_results,
                               data.frame(equilibrium = eq,
                                        derivative = fprime,
                                        stability = stability_type))
    }
    
    return(stability_results)
  }
)

# Example 1: Harvesting model analysis
harvesting_model <- function(P, H = 2) {
  r <- 1
  K <- 10
  return(r * P * (1 - P/K) - H)
}

# Phase line analysis for harvesting model
phase_result <- AdvancedAnalysis$phase_line_analysis(harvesting_model, c(-1, 12))

# Visualize phase line
phase_plot <- ggplot(phase_result$data, aes(x = y, y = dydt)) +
  geom_line(size = 1.2, color = "blue") +
  geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.7) +
  geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.7) +
  labs(
    title = "Phase Line Analysis: Harvesting Model",
    x = "Population (P)",
    y = "dP/dt"
  ) +
  theme_minimal()

# Add equilibrium points
if (length(phase_result$equilibria) > 0) {
  eq_data <- data.frame(
    equilibrium = phase_result$equilibria,
    dydt = 0
  )
  
  phase_plot <- phase_plot +
    geom_point(data = eq_data, aes(x = equilibrium, y = dydt),
               color = "red", size = 4)
}

print(phase_plot)

# Bifurcation analysis varying harvest rate
bifurcation_harvest <- function(P, H) {
  return(harvesting_model(P, H))
}

bifurcation_data <- AdvancedAnalysis$bifurcation_analysis(
  bifurcation_harvest, c(0, 5), 50
)

# Bifurcation plot
bifurcation_plot <- ggplot(bifurcation_data, 
                          aes(x = parameter, y = equilibrium, color = stable)) +
  geom_point(alpha = 0.7, size = 2) +
  scale_color_manual(values = c("TRUE" = "blue", "FALSE" = "red"),
                    labels = c("TRUE" = "Stable", "FALSE" = "Unstable")) +
  labs(
    title = "Bifurcation Diagram: Harvest Rate vs Population",
    x = "Harvest Rate (H)",
    y = "Equilibrium Population",
    color = "Stability"
  ) +
  theme_minimal()

print(bifurcation_plot)

# Example 2: Predator-Prey model (single species analysis)
predator_prey_prey <- function(x, alpha = 1, beta = 0.1, gamma = 1.5) {
  # Prey equation with constant predator: dx/dt = αx - βxy
  # Treating y as parameter
  y_predator <- gamma  # Constant predator level
  return(alpha * x - beta * x * y_predator)
}

# Phase analysis for prey with different predator levels
predator_levels <- c(5, 10, 15, 20)
prey_analysis_data <- data.frame()

for (pred_level in predator_levels) {
  prey_func <- function(x) predator_prey_prey(x, gamma = pred_level)
  phase_result <- AdvancedAnalysis$phase_line_analysis(prey_func, c(0, 30))
  
  temp_data <- phase_result$data
  temp_data$predator_level <- pred_level
  prey_analysis_data <- rbind(prey_analysis_data, temp_data)
}

# Multi-level phase line plot
prey_phase_plot <- ggplot(prey_analysis_data, 
                         aes(x = y, y = dydt, color = as.factor(predator_level))) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.7) +
  facet_wrap(~predator_level, labeller = label_both) +
  labs(
    title = "Prey Dynamics at Different Predator Levels",
    x = "Prey Population (x)",
    y = "dx/dt",
    color = "Predator Level"
  ) +
  theme_minimal()

print(prey_phase_plot)

# Example 3: Allee effect model
allee_model <- function(N, r = 1, K = 10, A = 2) {
  # dN/dt = rN(1 - N/K)(N/A - 1)
  return(r * N * (1 - N/K) * (N/A - 1))
}

# Bifurcation analysis varying Allee threshold A
allee_bifurcation <- function(N, A) {
  return(allee_model(N, A = A))
}

allee_bifurcation_data <- AdvancedAnalysis$bifurcation_analysis(
  allee_bifurcation, c(0.5, 5), 50
)

# Allee effect bifurcation plot
allee_plot <- ggplot(allee_bifurcation_data, 
                    aes(x = parameter, y = equilibrium, color = stable)) +
  geom_point(alpha = 0.7, size = 2) +
  scale_color_manual(values = c("TRUE" = "darkgreen", "FALSE" = "red"),
                    labels = c("TRUE" = "Stable", "FALSE" = "Unstable")) +
  labs(
    title = "Allee Effect Bifurcation: Threshold vs Population",
    x = "Allee Threshold (A)",
    y = "Equilibrium Population",
    color = "Stability"
  ) +
  theme_minimal()

print(allee_plot)

# Example 4: Sensitivity analysis
sensitivity_analysis <- function() {
  # Analyze sensitivity of equilibria to parameter changes
  
  base_params <- list(r = 1, K = 10, H = 2.5)
  param_variations <- seq(0.8, 1.2, by = 0.05)  # ±20% variation
  
  sensitivity_data <- data.frame()
  
  for (param_name in names(base_params)) {
    for (variation in param_variations) {
      params <- base_params
      params[[param_name]] <- params[[param_name]] * variation
      
      # Define model with current parameters
      model_func <- function(P) {
        harvesting_model(P, H = params$H)
      }
      
      # Find equilibria
      phase_result <- AdvancedAnalysis$phase_line_analysis(model_func, c(0, 15))
      
      if (length(phase_result$equilibria) > 0) {
        for (eq in phase_result$equilibria) {
          if (eq > 0 && eq < 15) {  # Valid population range
            sensitivity_data <- rbind(sensitivity_data,
                                    data.frame(
                                      parameter = param_name,
                                      variation_factor = variation,
                                      equilibrium = eq
                                    ))
          }
        }
      }
    }
  }
  
  # Plot sensitivity
  sensitivity_plot <- ggplot(sensitivity_data, 
                           aes(x = variation_factor, y = equilibrium, 
                               color = parameter)) +
    geom_line(size = 1.2) +
    geom_point(size = 2) +
    facet_wrap(~parameter, scales = "free") +
    labs(
      title = "Sensitivity Analysis: Parameter Variations",
      x = "Parameter Variation Factor",
      y = "Equilibrium Population",
      color = "Parameter"
    ) +
    theme_minimal()
  
  return(sensitivity_plot)
}

# Run sensitivity analysis
sensitivity_plot <- sensitivity_analysis()
print(sensitivity_plot)

# Summary statistics
cat("Advanced Analysis Summary:\n")
cat("========================\n")
cat("Harvesting model equilibria:", phase_result$equilibria, "\n")
cat("Number of bifurcation points found:", nrow(bifurcation_data), "\n")
cat("Stability analysis complete for", length(unique(bifurcation_data$parameter)), "parameter values\n")</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Case Studies -->
        <section id="case-studies" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-project-diagram text-indigo-600 mr-3"></i>Case Studies
            </h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="definition-box">
                    <h3 class="text-xl font-bold mb-3"><i class="fas fa-virus mr-2"></i>Epidemic Modeling</h3>
                    <p class="mb-3">The SIR model uses a system of ODEs to model disease spread:</p>
                    <div class="text-sm space-y-2">
                        <p>$\frac{dS}{dt} = -\beta SI/N$ (Susceptible)</p>
                        <p>$\frac{dI}{dt} = \beta SI/N - \gamma I$ (Infected)</p>
                        <p>$\frac{dR}{dt} = \gamma I$ (Recovered)</p>
                    </div>
                    <p class="text-sm mt-3">For early stages, infected population follows: $\frac{dI}{dt} \approx (\beta - \gamma)I$</p>
                </div>

                <div class="application-box">
                    <h3 class="text-xl font-bold mb-3"><i class="fas fa-industry mr-2"></i>Chemical Reactor</h3>
                    <p class="mb-3">A continuous stirred tank reactor (CSTR) follows:</p>
                    <div class="text-sm space-y-2">
                        <p>$\frac{dC}{dt} = \frac{F}{V}(C_{in} - C) - kC^n$</p>
                        <p>Where: F = flow rate, V = volume, k = reaction rate constant</p>
                    </div>
                    <p class="text-sm mt-3">For first-order reactions (n=1), this becomes linear first-order ODE.</p>
                </div>
            </div>
        </section>

        <!-- Summary -->
        <section class="mb-12">
            <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white p-8 rounded-lg">
                <h2 class="text-2xl font-bold mb-4"><i class="fas fa-graduation-cap mr-3"></i>Chapter Summary</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-bold mb-3">Key Modeling Steps</h3>
                        <ul class="space-y-2 text-sm">
                            <li>1. <strong>Identify variables:</strong> What changes over time?</li>
                            <li>2. <strong>Find relationships:</strong> How do variables relate?</li>
                            <li>3. <strong>Formulate ODE:</strong> Express rate of change</li>
                            <li>4. <strong>Solve equation:</strong> Use appropriate method</li>
                            <li>5. <strong>Validate model:</strong> Check against data</li>
                            <li>6. <strong>Interpret results:</strong> What do solutions mean?</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold mb-3">Common Applications</h3>
                        <ul class="space-y-2 text-sm">
                            <li>• <strong>Population dynamics:</strong> Growth and harvesting</li>
                            <li>• <strong>Economics:</strong> Interest, loans, investments</li>
                            <li>• <strong>Physics:</strong> Cooling, radioactive decay</li>
                            <li>• <strong>Engineering:</strong> RC circuits, control systems</li>
                            <li>• <strong>Biology:</strong> Drug concentration, enzyme kinetics</li>
                            <li>• <strong>Chemistry:</strong> Reaction rates, mixing problems</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Classification Chart - Decision Tree for ODE Types
        const classificationCtx = document.getElementById('classificationChart').getContext('2d');
        const classificationChart = new Chart(classificationCtx, {
            type: 'doughnut',
            data: {
                labels: ['Separable', 'Linear First-Order', 'Exact', 'Other Methods'],
                datasets: [{
                    data: [35, 40, 15, 10],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'ODE Classification Decision Tree',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Mixing Chart - Salt Water Tank Problem
        const mixingCtx = document.getElementById('mixingChart').getContext('2d');
        
        // Generate mixing problem data
        const timePoints = Array.from({length: 501}, (_, i) => i);
        const concentration = timePoints.map(t => 0.5 * (1 - Math.exp(-0.01 * t)));
        const equilibrium = timePoints.map(t => 0.5);
        
        const mixingChart = new Chart(mixingCtx, {
            type: 'line',
            data: {
                labels: timePoints,
                datasets: [{
                    label: 'Concentration C(t)',
                    data: concentration,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: true,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Equilibrium (0.5 kg/L)',
                    data: equilibrium,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderDash: [5, 5],
                    fill: false,
                    tension: 0,
                    borderWidth: 2
                }, {
                    label: '95% of Equilibrium',
                    data: timePoints.map(t => 0.475),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderDash: [10, 5],
                    fill: false,
                    tension: 0,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (minutes)',
                            font: { size: 14 }
                        },
                        grid: {
                            alpha: 0.3
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Concentration (kg/L)',
                            font: { size: 14 }
                        },
                        min: 0,
                        max: 0.6,
                        grid: {
                            alpha: 0.3
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Salt Water Tank Mixing Problem',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                if (context.datasetIndex === 0) {
                                    const t = context.parsed.x;
                                    const percentage = ((context.parsed.y / 0.5) * 100).toFixed(1);
                                    return `${percentage}% of equilibrium at t=${t} min`;
                                }
                                return null;
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 5
                    }
                }
            }
        });

        // Population Chart - Improved version with multiple scenarios
        const populationCtx = document.getElementById('populationChart').getContext('2d');
        
        // Generate population data
        const timeRange = Array.from({length: 51}, (_, i) => i);
        const P0 = 50, r = 0.1, K = 1000;
        
        // Exponential growth
        const exponentialData = timeRange.map(t => P0 * Math.exp(r * t));
        
        // Logistic growth
        const logisticData = timeRange.map(t => K / (1 + (K/P0 - 1) * Math.exp(-r * t)));
        
        // Growth with harvesting (H = 25)
        const harvestingData = timeRange.map(t => {
            // Approximate solution for harvesting model
            const H = 25;
            const equilibrium = K * (1 - Math.sqrt(1 - 4*H/(r*K)));
            if (equilibrium > 0) {
                return equilibrium + (P0 - equilibrium) * Math.exp(-r * (1 - 2*equilibrium/K) * t);
            }
            return 0;
        });
        
        const populationChart = new Chart(populationCtx, {
            type: 'line',
            data: {
                labels: timeRange,
                datasets: [{
                    label: 'Exponential Growth',
                    data: exponentialData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Logistic Growth',
                    data: logisticData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'With Harvesting (H=25)',
                    data: harvestingData,
                    borderColor: 'rgb(255, 206, 86)',
                    backgroundColor: 'rgba(255, 206, 86, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Carrying Capacity',
                    data: timeRange.map(t => K),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderDash: [10, 5],
                    fill: false,
                    tension: 0,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (years)',
                            font: { size: 14 }
                        },
                        grid: {
                            alpha: 0.3
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Population',
                            font: { size: 14 }
                        },
                        min: 0,
                        max: 1200,
                        grid: {
                            alpha: 0.3
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Population Growth Models Comparison',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                if (context.datasetIndex === 1) { // Logistic model
                                    const t = context.parsed.x;
                                    const P = context.parsed.y;
                                    const growthRate = (r * P * (1 - P/K)).toFixed(2);
                                    return `Growth rate: ${growthRate} individuals/year`;
                                }
                                return null;
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 5
                    }
                }
            }
        });

        // Economic Models Visualization
        const economicsCtx = document.getElementById('economicsChart').getContext('2d');
        
        // Generate economic data
        const loanAmortization = timeRange.map(t => {
            const r_annual = 0.05;
            const P = 600;
            const D_0 = 100000;
            const k = r_annual / 12;
            return (D_0 - P/k) * Math.exp(-k * t) + P/k;
        });
        
        const savingsWithDeposits = timeRange.map(t => {
            const r_annual = 0.03;
            const D = 500;
            const S_0 = 10000;
            const r = r_annual / 12;
            return S_0 * Math.exp(r * t) + (D * (Math.exp(r * t) - 1) / r);
        });
        
        const compoundInterest = timeRange.map(t => {
            const A_0 = 10000;
            const r_annual = 0.07;
            return A_0 * Math.exp(r_annual * t);
        });
        
        const economicsChart = new Chart(economicsCtx, {
            type: 'line',
            data: {
                labels: timeRange,
                datasets: [{
                    label: 'Loan Amortization',
                    data: loanAmortization,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Savings Account',
                    data: savingsWithDeposits,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Compound Interest',
                    data: compoundInterest,
                    borderColor: 'rgb(255, 206, 86)',
                    backgroundColor: 'rgba(255, 206, 86, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (years)',
                            font: { size: 14 }
                        },
                        grid: {
                            alpha: 0.3
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Amount ($)',
                            font: { size: 14 }
                        },
                        min: 0,
                        grid: {
                            alpha: 0.3
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Economic Models Visualization',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (context.parsed.y !== null) {
                                    label += `: $${context.parsed.y.toFixed(2)}`;
                                }
                                return label;
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 5
                    }
                }
            }
        });

        // Economics Chart - Financial Models
        const economicsCanvas = document.getElementById('economicsChart');
        if (economicsCanvas) {
            const economicsChart = new Chart(economicsCanvas.getContext('2d'), {
                type: 'line',
                data: {
                    labels: Array.from({length: 121}, (_, i) => i), // 0 to 120 months
                    datasets: [{
                        label: 'Loan Balance ($)',
                        data: Array.from({length: 121}, (_, i) => {
                            // Loan amortization: D(t) = (D0 - P/r)e^(rt) + P/r
                            const D0 = 100000, r = 0.05/12, P = 600; // Monthly
                            const t = i;
                            if (t === 0) return D0;
                            const balance = (D0 - P/r) * Math.exp(r * t) + P/r;
                            return Math.max(0, balance); // Can't go negative
                        }),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3,
                        yAxisID: 'y'
                    }, {
                        label: 'Savings Balance ($)',
                        data: Array.from({length: 121}, (_, i) => {
                            // Savings with deposits: S(t) = (S0 + D/r)e^(rt) - D/r
                            const S0 = 10000, r = 0.03/12, D = 500; // Monthly
                            const t = i;
                            return (S0 + D/r) * Math.exp(r * t) - D/r;
                        }),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3,
                        yAxisID: 'y'
                    }, {
                        label: 'Compound Interest ($)',
                        data: Array.from({length: 121}, (_, i) => {
                            // Compound interest: A(t) = A0 * e^(rt)
                            const A0 = 10000, r = 0.07/12; // Monthly compounding
                            const t = i;
                            return A0 * Math.exp(r * t);
                        }),
                        borderColor: 'rgb(255, 206, 86)',
                        backgroundColor: 'rgba(255, 206, 86, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3,
                        yAxisID: 'y'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Time (months)',
                                font: { size: 14 }
                            },
                            grid: {
                                alpha: 0.3
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Amount ($)',
                                font: { size: 14 }
                            },
                            grid: {
                                alpha: 0.3
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Economic Models: Loans, Savings, and Investments',
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed.y;
                                    return context.dataset.label + ': $' + value.toLocaleString();
                                },
                                afterLabel: function(context) {
                                    if (context.datasetIndex === 0) { // Loan
                                        const months = context.parsed.x;
                                        const years = (months / 12).toFixed(1);
                                        return `After ${years} years`;
                                    }
                                    return null;
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 0,
                            hoverRadius: 5
                        }
                    }
                }
            });
        }

        // Cooling Chart - Newton's Law of Cooling
        const coolingCanvas = document.getElementById('coolingChart');
        if (coolingCanvas) {
            const timePointsCooling = Array.from({length: 61}, (_, i) => i); // 0 to 60 minutes
            
            const coolingChart = new Chart(coolingCanvas.getContext('2d'), {
                type: 'line',
                data: {
                    labels: timePointsCooling,
                    datasets: [{
                        label: 'Hot Coffee (90°C → 20°C)',
                        data: timePointsCooling.map(t => 20 + (90 - 20) * Math.exp(-0.067 * t)),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Cold Drink (5°C → 20°C)',
                        data: timePointsCooling.map(t => 20 + (5 - 20) * Math.exp(-0.050 * t)),
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Lukewarm Water (35°C → 20°C)',
                        data: timePointsCooling.map(t => 20 + (35 - 20) * Math.exp(-0.040 * t)),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Room Temperature',
                        data: timePointsCooling.map(t => 20),
                        borderColor: 'rgb(255, 206, 86)',
                        backgroundColor: 'rgba(255, 206, 86, 0.1)',
                        borderDash: [10, 5],
                        fill: false,
                        tension: 0,
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Time (minutes)',
                                font: { size: 14 }
                            },
                            grid: {
                                alpha: 0.3
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Temperature (°C)',
                                font: { size: 14 }
                            },
                            grid: {
                                alpha: 0.3
                            },
                            min: 0,
                            max: 100
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Newton\'s Law of Cooling - Temperature vs Time',
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const temp = context.parsed.y.toFixed(1);
                                    return context.dataset.label + ': ' + temp + '°C';
                                },
                                afterLabel: function(context) {
                                    if (context.datasetIndex < 3) { // Not room temperature line
                                        const t = context.parsed.x;
                                        const temp = context.parsed.y;
                                        const roomTemp = 20;
                                        const percentageToEquilibrium = (Math.abs(temp - roomTemp) / Math.abs(context.dataset.data[0] - roomTemp) * 100).toFixed(1);
                                        return `${100 - percentageToEquilibrium}% equilibrated at t=${t} min`;
                                    }
                                    return null;
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 0,
                            hoverRadius: 5
                        }
                    }
                }
            });
        }

        // Enhanced visualization creation functions
        function createEconomicCharts() {
            // Economic Price Dynamics Chart
            const priceCtx = document.getElementById('economicPriceChart');
            if (priceCtx) {
                const timeRange = Array.from({length: 101}, (_, i) => i / 10);
                const equilibriumPrice = 100;
                const initialPrices = [80, 120, 90, 110];
                const adjustmentRate = 0.3;
                
                const datasets = initialPrices.map((P0, index) => {
                    const prices = timeRange.map(t => 
                        equilibriumPrice + (P0 - equilibriumPrice) * Math.exp(-adjustmentRate * t)
                    );
                    return {
                        label: `Initial Price: $${P0}`,
                        data: prices,
                        borderColor: `hsl(${index * 60}, 70%, 50%)`,
                        backgroundColor: `hsl(${index * 60}, 70%, 50%, 0.1)`,
                        fill: false,
                        tension: 0.4
                    };
                });

                datasets.push({
                    label: 'Equilibrium Price',
                    data: timeRange.map(() => equilibriumPrice),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(priceCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Market Price Adjustment Dynamics'
                            },
                            legend: { position: 'top' }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (months)' } },
                            y: { title: { display: true, text: 'Price ($)' } }
                        }
                    }
                });
            }

            // Economic Growth Chart
            const growthCtx = document.getElementById('economicGrowthChart');
            if (growthCtx) {
                const timeRange = Array.from({length: 51}, (_, i) => i);
                const scenarios = [
                    { label: 'Low Growth (2%)', rate: 0.02, color: '#ff6b6b' },
                    { label: 'Moderate Growth (5%)', rate: 0.05, color: '#4ecdc4' },
                    { label: 'High Growth (8%)', rate: 0.08, color: '#45b7d1' }
                ];
                const initialValue = 1000;

                const datasets = scenarios.map(scenario => ({
                    label: scenario.label,
                    data: timeRange.map(t => initialValue * Math.exp(scenario.rate * t)),
                    borderColor: scenario.color,
                    backgroundColor: scenario.color + '20',
                    fill: false,
                    tension: 0.4
                }));

                new Chart(growthCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Economic Growth Scenarios'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (years)' } },
                            y: { 
                                title: { display: true, text: 'Value ($)' },
                                type: 'logarithmic'
                            }
                        }
                    }
                });
            }

            // Investment Chart
            const investmentCtx = document.getElementById('economicInvestmentChart');
            if (investmentCtx) {
                const timeRange = Array.from({length: 101}, (_, i) => i / 2);
                const interestRate = 0.05;
                const initialAmount = 10000;
                const scenarios = [
                    { deposit: 0, label: 'No Deposits' },
                    { deposit: 500, label: 'Monthly $500 Deposits' },
                    { deposit: 1000, label: 'Monthly $1000 Deposits' },
                    { deposit: -300, label: 'Monthly $300 Withdrawals' }
                ];

                const datasets = scenarios.map((scenario, index) => {
                    const D = scenario.deposit * 12; // Annual equivalent
                    const data = timeRange.map(t => {
                        if (D === 0) {
                            return initialAmount * Math.exp(interestRate * t);
                        } else {
                            return D/interestRate + (initialAmount - D/interestRate) * Math.exp(interestRate * t);
                        }
                    });
                    
                    return {
                        label: scenario.label,
                        data: data,
                        borderColor: `hsl(${index * 90}, 70%, 50%)`,
                        backgroundColor: `hsl(${index * 90}, 70%, 50%, 0.1)`,
                        fill: false,
                        tension: 0.4
                    };
                });

                new Chart(investmentCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Investment Growth with Continuous Deposits/Withdrawals'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (years)' } },
                            y: { title: { display: true, text: 'Account Balance ($)' } }
                        }
                    }
                });
            }
        }

        function createPhysicsCharts() {
            // Radioactive Decay Chart
            const decayCtx = document.getElementById('physicsDecayChart');
            if (decayCtx) {
                const timeRange = Array.from({length: 101}, (_, i) => i);
                const isotopes = [
                    { name: 'Carbon-14', halfLife: 5730, color: '#ff6b6b' },
                    { name: 'Uranium-238', halfLife: 4468000, color: '#4ecdc4' },
                    { name: 'Tritium', halfLife: 12.3, color: '#45b7d1' },
                    { name: 'Iodine-131', halfLife: 8.02, color: '#f7dc6f' }
                ];

                const datasets = isotopes.map(isotope => {
                    const lambda = Math.log(2) / isotope.halfLife;
                    const data = timeRange.map(t => Math.exp(-lambda * t));
                    
                    return {
                        label: `${isotope.name} (t₁/₂ = ${isotope.halfLife} years)`,
                        data: data,
                        borderColor: isotope.color,
                        backgroundColor: isotope.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                new Chart(decayCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Radioactive Decay of Different Isotopes'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (years)' } },
                            y: { 
                                title: { display: true, text: 'Fraction Remaining' },
                                type: 'logarithmic'
                            }
                        }
                    }
                });
            }

            // Circuit Response Chart
            const circuitCtx = document.getElementById('physicsCircuitChart');
            if (circuitCtx) {
                const timeRange = Array.from({length: 201}, (_, i) => i * 0.01);
                const circuits = [
                    { R: 100, L: 0.1, label: 'Fast Circuit (τ = 1ms)', color: '#ff6b6b' },
                    { R: 1000, L: 1, label: 'Medium Circuit (τ = 1ms)', color: '#4ecdc4' },
                    { R: 10000, L: 10, label: 'Slow Circuit (τ = 1ms)', color: '#45b7d1' }
                ];
                const inputVoltage = 5;

                const datasets = circuits.map(circuit => {
                    const tau = circuit.L / circuit.R;
                    const data = timeRange.map(t => inputVoltage * (1 - Math.exp(-t / tau)));
                    
                    return {
                        label: circuit.label,
                        data: data,
                        borderColor: circuit.color,
                        backgroundColor: circuit.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                datasets.push({
                    label: 'Input Voltage',
                    data: timeRange.map(() => inputVoltage),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(circuitCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'RL Circuit Step Response'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (s)' } },
                            y: { title: { display: true, text: 'Current (A)' } }
                        }
                    }
                });
            }

            // Enhanced Cooling Chart
            const coolingCtx = document.getElementById('physicsCoolingChart');
            if (coolingCtx) {
                const timeRange = Array.from({length: 201}, (_, i) => i);
                const roomTemp = 20;
                const scenarios = [
                    { T0: 100, k: 0.05, material: 'Metal (fast cooling)', color: '#ff6b6b' },
                    { T0: 100, k: 0.02, material: 'Ceramic (medium cooling)', color: '#4ecdc4' },
                    { T0: 100, k: 0.01, material: 'Insulated (slow cooling)', color: '#45b7d1' },
                    { T0: 150, k: 0.03, material: 'Hot metal', color: '#f7dc6f' }
                ];

                const datasets = scenarios.map(scenario => {
                    const data = timeRange.map(t => 
                        roomTemp + (scenario.T0 - roomTemp) * Math.exp(-scenario.k * t)
                    );
                    
                    return {
                        label: scenario.material,
                        data: data,
                        borderColor: scenario.color,
                        backgroundColor: scenario.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                datasets.push({
                    label: 'Room Temperature',
                    data: timeRange.map(() => roomTemp),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(coolingCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Newton\'s Law of Cooling - Multiple Materials'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (minutes)' } },
                            y: { title: { display: true, text: 'Temperature (°C)' } }
                        }
                    }
                });
            }
        }

        function createEngineeringCharts() {
            // Step Response Chart
            const stepCtx = document.getElementById('engineeringStepChart');
            if (stepCtx) {
                const timeRange = Array.from({length: 201}, (_, i) => i * 0.05);
                const systems = [
                    { tau: 0.5, K: 1, label: 'Fast System (τ = 0.5s)', color: '#ff6b6b' },
                    { tau: 1.0, K: 1, label: 'Medium System (τ = 1.0s)', color: '#4ecdc4' },
                    { tau: 2.0, K: 1, label: 'Slow System (τ = 2.0s)', color: '#45b7d1' },
                    { tau: 1.0, K: 2, label: 'High Gain (K = 2)', color: '#f7dc6f' }
                ];

                const datasets = systems.map(system => {
                    const data = timeRange.map(t => system.K * (1 - Math.exp(-t / system.tau)));
                    
                    return {
                        label: system.label,
                        data: data,
                        borderColor: system.color,
                        backgroundColor: system.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                // Add time constant markers
                systems.forEach((system, index) => {
                    if (index < 3) { // Only for different time constants
                        const tauIndex = Math.round(system.tau / 0.05);
                        if (tauIndex < timeRange.length) {
                            datasets.push({
                                label: `τ = ${system.tau}s marker`,
                                data: timeRange.map((t, i) => i === tauIndex ? system.K * 0.632 : null),
                                backgroundColor: system.color,
                                borderColor: system.color,
                                pointRadius: 8,
                                showLine: false,
                                pointStyle: 'circle'
                            });
                        }
                    }
                });

                new Chart(stepCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'First-Order System Step Response'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (s)' } },
                            y: { title: { display: true, text: 'Output' } }
                        }
                    }
                });
            }

            // Frequency Response Chart
            const freqCtx = document.getElementById('engineeringFrequencyChart');
            if (freqCtx) {
                const frequencies = Array.from({length: 100}, (_, i) => Math.pow(10, (i - 50) / 25));
                const systems = [
                    { tau: 0.1, label: 'τ = 0.1s', color: '#ff6b6b' },
                    { tau: 1.0, label: 'τ = 1.0s', color: '#4ecdc4' },
                    { tau: 10.0, label: 'τ = 10.0s', color: '#45b7d1' }
                ];

                const datasets = systems.map(system => {
                    const magnitude = frequencies.map(w => 20 * Math.log10(1 / Math.sqrt(1 + Math.pow(w * system.tau, 2))));
                    
                    return {
                        label: system.label,
                        data: magnitude,
                        borderColor: system.color,
                        backgroundColor: system.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                new Chart(freqCtx, {
                    type: 'line',
                    data: {
                        labels: frequencies.map(f => f.toFixed(3)),
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Frequency Response (Bode Plot - Magnitude)'
                            }
                        },
                        scales: {
                            x: { 
                                title: { display: true, text: 'Frequency (rad/s)' },
                                type: 'logarithmic'
                            },
                            y: { title: { display: true, text: 'Magnitude (dB)' } }
                        }
                    }
                });
            }

            // Control System Chart
            const controlCtx = document.getElementById('engineeringControlChart');
            if (controlCtx) {
                const timeRange = Array.from({length: 301}, (_, i) => i * 0.1);
                const setpoint = 1;
                
                // Simulate step response with different controllers
                const controllers = [
                    { Kp: 1, label: 'P Controller (Kp=1)', color: '#ff6b6b', type: 'proportional' },
                    { Kp: 5, label: 'P Controller (Kp=5)', color: '#4ecdc4', type: 'proportional' },
                    { Kp: 10, label: 'P Controller (Kp=10)', color: '#45b7d1', type: 'proportional' }
                ];

                const datasets = [];
                
                // Add setpoint reference
                datasets.push({
                    label: 'Setpoint',
                    data: timeRange.map(() => setpoint),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false
                });

                // Add controller responses
                controllers.forEach(controller => {
                    const tau_cl = 1 / (1 + controller.Kp); // Closed-loop time constant
                    const ss_error = 1 / (1 + controller.Kp); // Steady-state error
                    const final_value = setpoint - ss_error;
                    
                    const data = timeRange.map(t => final_value * (1 - Math.exp(-t / tau_cl)));
                    
                    datasets.push({
                        label: controller.label,
                        data: data,
                        borderColor: controller.color,
                        backgroundColor: controller.color + '20',
                        fill: false,
                        tension: 0.4
                    });
                });

                new Chart(controlCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Closed-Loop Control System Response'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (s)' } },
                            y: { title: { display: true, text: 'Output' } }
                        }
                    }
                });
            }
        }

        function createAdvancedCharts() {
            // Phase Line Analysis Chart
            const phaseCtx = document.getElementById('advancedPhaseChart');
            if (phaseCtx) {
                const yRange = Array.from({length: 201}, (_, i) => (i - 100) / 10);
                
                // Example: dy/dt = y(y-2)(y+1)
                const dydt = yRange.map(y => y * (y - 2) * (y + 1));
                
                const datasets = [{
                    label: 'dy/dt = y(y-2)(y+1)',
                    data: dydt,
                    borderColor: '#ff6b6b',
                    backgroundColor: '#ff6b6b20',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 0
                }];

                // Add equilibrium points
                const equilibria = [-1, 0, 2];
                equilibria.forEach((eq, index) => {
                    datasets.push({
                        label: `Equilibrium: y = ${eq}`,
                        data: yRange.map((y, i) => Math.abs(y - eq) < 0.1 ? 0 : null),
                        backgroundColor: index % 2 === 0 ? '#4ecdc4' : '#f7dc6f',
                        borderColor: index % 2 === 0 ? '#4ecdc4' : '#f7dc6f',
                        pointRadius: 8,
                        showLine: false,
                        pointStyle: 'circle'
                    });
                });

                new Chart(phaseCtx, {
                    type: 'line',
                    data: {
                        labels: yRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Phase Line Analysis: dy/dt vs y'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'y' } },
                            y: { 
                                title: { display: true, text: 'dy/dt' },
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // Stability Analysis Chart
            const stabilityCtx = document.getElementById('advancedStabilityChart');
            if (stabilityCtx) {
                const timeRange = Array.from({length: 201}, (_, i) => i * 0.1);
                
                // Different initial conditions for dy/dt = y(y-2)(y+1)
                const initialConditions = [
                    { y0: -2, label: 'y₀ = -2', color: '#ff6b6b' },
                    { y0: -0.5, label: 'y₀ = -0.5', color: '#4ecdc4' },
                    { y0: 0.5, label: 'y₀ = 0.5', color: '#45b7d1' },
                    { y0: 1.5, label: 'y₀ = 1.5', color: '#f7dc6f' },
                    { y0: 3, label: 'y₀ = 3', color: '#bb8fce' }
                ];

                const datasets = initialConditions.map(ic => {
                    // Approximate solution behavior based on equilibria
                    let data;
                    if (ic.y0 < -1) {
                        data = timeRange.map(t => -1 + (ic.y0 + 1) * Math.exp(-t));
                    } else if (ic.y0 > -1 && ic.y0 < 0) {
                        data = timeRange.map(t => -1 + (ic.y0 + 1) * Math.exp(t));
                    } else if (ic.y0 > 0 && ic.y0 < 2) {
                        data = timeRange.map(t => 0 + ic.y0 * Math.exp(-t));
                    } else {
                        data = timeRange.map(t => 2 + (ic.y0 - 2) * Math.exp(t));
                    }
                    
                    return {
                        label: ic.label,
                        data: data,
                        borderColor: ic.color,
                        backgroundColor: ic.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                // Add equilibrium lines
                [-1, 0, 2].forEach((eq, index) => {
                    datasets.push({
                        label: `Equilibrium y = ${eq}`,
                        data: timeRange.map(() => eq),
                        borderColor: '#333',
                        borderDash: [5, 5],
                        pointRadius: 0
                    });
                });

                new Chart(stabilityCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Solution Trajectories and Stability'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time' } },
                            y: { title: { display: true, text: 'y(t)' } }
                        }
                    }
                });
            }

            // Direction Field Chart
            const directionCtx = document.getElementById('advancedDirectionChart');
            if (directionCtx) {
                // Create direction field for dy/dt = -2y + 1
                const xRange = Array.from({length: 21}, (_, i) => i * 0.5);
                const yRange = Array.from({length: 21}, (_, i) => (i - 10) * 0.2);
                
                const datasets = [];
                
                // Solution curves
                const initialValues = [-1.5, -1, -0.5, 0, 0.5, 1, 1.5];
                initialValues.forEach((y0, index) => {
                    const solution = xRange.map(t => 0.5 + (y0 - 0.5) * Math.exp(-2 * t));
                    datasets.push({
                        label: `y₀ = ${y0}`,
                        data: solution,
                        borderColor: `hsl(${index * 50}, 70%, 50%)`,
                        backgroundColor: `hsl(${index * 50}, 70%, 50%, 0.1)`,
                        fill: false,
                        tension: 0.4,
                        pointRadius: 0
                    });
                });

                // Equilibrium line
                datasets.push({
                    label: 'Equilibrium: y = 0.5',
                    data: xRange.map(() => 0.5),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(directionCtx, {
                    type: 'line',
                    data: {
                        labels: xRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Direction Field and Solution Curves: dy/dt = -2y + 1'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (t)' } },
                            y: { title: { display: true, text: 'y(t)' } }
                        }
                    }
                });
            }

            // Bifurcation Chart
            const bifurcationCtx = document.getElementById('advancedBifurcationChart');
            if (bifurcationCtx) {
                // Harvesting model bifurcation: dP/dt = rP(1-P/K) - H
                const HRange = Array.from({length: 101}, (_, i) => i * 0.3);
                const r = 1, K = 10;
                const Hmax = r * K / 4;
                
                const datasets = [];
                
                // Stable and unstable equilibria
                const stableEq = [], unstableEq = [];
                HRange.forEach(H => {
                    if (H <= Hmax) {
                        const discriminant = r * r * K * K / 4 - 4 * r * H;
                        if (discriminant >= 0) {
                            const eq1 = (r * K + Math.sqrt(discriminant)) / (2 * r);
                            const eq2 = (r * K - Math.sqrt(discriminant)) / (2 * r);
                            stableEq.push(eq1);
                            unstableEq.push(eq2);
                        } else {
                            stableEq.push(null);
                            unstableEq.push(null);
                        }
                    } else {
                        stableEq.push(null);
                        unstableEq.push(null);
                    }
                });

                datasets.push({
                    label: 'Stable Equilibrium',
                    data: stableEq,
                    borderColor: '#4ecdc4',
                    backgroundColor: '#4ecdc4',
                    pointRadius: 2,
                    pointHoverRadius: 4,
                    showLine: true,
                    tension: 0.4
                });

                datasets.push({
                    label: 'Unstable Equilibrium',
                    data: unstableEq,
                    borderColor: '#ff6b6b',
                    backgroundColor: '#ff6b6b',
                    pointRadius: 2,
                    pointHoverRadius: 4,
                    showLine: true,
                    borderDash: [5, 5],
                    tension: 0.4
                });

                // Critical point
                datasets.push({
                    label: `Critical Point (H = ${Hmax.toFixed(2)})`,
                    data: HRange.map((H, i) => Math.abs(H - Hmax) < 0.1 ? K/2 : null),
                    backgroundColor: '#f7dc6f',
                    borderColor: '#f7dc6f',
                    pointRadius: 8,
                    showLine: false,
                    pointStyle: 'star'
                });

                new Chart(bifurcationCtx, {
                    type: 'line',
                    data: {
                        labels: HRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Bifurcation Diagram: Harvesting Model'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Harvest Rate (H)' } },
                            y: { title: { display: true, text: 'Population Equilibrium (P)' } }
                        }
                    }
                });
            }
        }

        // Initialize all new charts when page loads
        function initializeAllCharts() {
            createEconomicCharts();
            createPhysicsCharts();
            createEngineeringCharts();
            createAdvancedCharts();
        }

        // Add interactivity to page elements
        function addInteractivity() {
            // Add hover effects to boxes
            const boxes = document.querySelectorAll('.definition-box, .theorem-box, .example-box, .application-box');
            boxes.forEach(box => {
                box.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                box.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add click to expand functionality for code blocks
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.style.cursor = 'pointer';
                block.title = 'Click to toggle expansion';
                
                block.addEventListener('click', function() {
                    if (this.style.maxHeight && this.style.maxHeight !== 'none') {
                        this.style.maxHeight = 'none';
                        this.style.overflow = 'visible';
                    } else {
                        this.style.maxHeight = '300px';
                        this.style.overflow = 'auto';
                    }
                });
                
                // Initial state
                block.style.maxHeight = '300px';
                block.style.overflow = 'auto';
            });
        }

        // Chart update functions for dynamic content
        function updateMixingChart(volumeParam, flowParam, concentrationParam) {
            const V = volumeParam || 1000;
            const r = flowParam || 10;
            const C_in = concentrationParam || 0.5;
            
            const newConcentration = timePoints.map(t => C_in * (1 - Math.exp(-r * t / V)));
            
            mixingChart.data.datasets[0].data = newConcentration;
            mixingChart.data.datasets[1].data = timePoints.map(t => C_in);
            mixingChart.update('active');
        }

        function updatePopulationChart(rParam, KParam, HParam) {
            const newR = rParam || 0.1;
            const newK = KParam || 1000;
            const newH = HParam || 25;
            
            const newLogistic = timeRange.map(t => newK / (1 + (newK/P0 - 1) * Math.exp(-newR * t)));
            const newExponential = timeRange.map(t => P0 * Math.exp(newR * t));
            
            populationChart.data.datasets[0].data = newExponential;
            populationChart.data.datasets[1].data = newLogistic;
            populationChart.data.datasets[3].data = timeRange.map(t => newK);
            populationChart.update('active');
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navigation highlighting
        function highlightActiveSection() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('nav a[href^="#"]');
            
            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('font-bold', 'text-blue-800');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('font-bold', 'text-blue-800');
                    }
                });
            });
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all charts
            initializeAllCharts();
            
            addInteractivity();
            highlightActiveSection();
            
            // Mark chart containers as loaded after charts are created
            setTimeout(() => {
                const chartContainers = document.querySelectorAll('.chart-container');
                chartContainers.forEach(container => {
                    container.classList.add('loaded');
                });
                
                // Add loading animation completion
                document.body.style.opacity = '1';
                document.body.style.transition = 'opacity 0.5s ease-in-out';
            }, 500);
            
            // Error handling for charts
            window.addEventListener('error', function(e) {
                if (e.message.includes('Chart')) {
                    console.warn('Chart rendering issue detected, attempting recovery...');
                    // Simple recovery mechanism
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                }
            });
        });

        // Expose update functions globally for potential external control
        window.chartUpdaters = {
            updateMixingChart,
            updatePopulationChart
        };

        // Additional chart configuration for better mobile responsiveness
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;
        Chart.defaults.devicePixelRatio = window.devicePixelRatio || 1;
        
        // Performance optimization for large datasets
        Chart.defaults.elements.point.radius = 0;
        Chart.defaults.elements.point.hoverRadius = 4;
        Chart.defaults.elements.line.tension = 0.4;
        
        // Accessibility improvements
        Chart.defaults.plugins.legend.labels.usePointStyle = true;
        Chart.defaults.plugins.tooltip.titleFont = { size: 14, weight: 'bold' };
        Chart.defaults.plugins.tooltip.bodyFont = { size: 12 };
        
        // Print-friendly chart settings
        window.addEventListener('beforeprint', function() {
            Chart.helpers.each(Chart.instances, function(chart) {
                chart.resize();
            });
        });
    </script>
</body>
</html>