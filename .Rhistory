rmarkdown::pandoc_convert(
input = "ode_tutorial_part1_mathematical_foundations.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "part1.md",        # Desired output filename
options = c(
"--wrap=none",            # Disable line wrapping
"--atx-headers"           # Use ATX-style headers (# H1, ## H2, ...)
)
)
rmarkdown::pandoc_convert(
input = "ode_tutorial_part1_mathematical_foundations.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "part1.md",        # Desired output filename
options = c(
"--from=html+tex_math_dollars",        # Enable TeX math using $...$
"--to=gfm+tex_math_dollars",           # GitHub-Flavored Markdown with math
"--mathjax",                            # Render math via MathJax
"--wrap=none",                          # Disable line wrapping
"--atx-headers"                         # Use hash-style headers
)
)
rmarkdown::pandoc_convert(
input = "chapter_1_algebra_functions_ode_tutorial.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "chapter_01.md",        # Desired output filename
options = c(
"--from=html+tex_math_dollars",        # Enable TeX math using $...$
"--to=gfm+tex_math_dollars",           # GitHub-Flavored Markdown with math
"--mathjax",                            # Render math via MathJax
"--wrap=none",                          # Disable line wrapping
"--atx-headers"                         # Use hash-style headers
)
)
rmarkdown::pandoc_convert(
input = "chapter_2_trigonometry_ode_tutorial.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "chapter_02.md",        # Desired output filename
options = c(
"--from=html+tex_math_dollars",        # Enable TeX math using $...$
"--to=gfm+tex_math_dollars",           # GitHub-Flavored Markdown with math
"--mathjax",                            # Render math via MathJax
"--wrap=none",                          # Disable line wrapping
"--atx-headers"                         # Use hash-style headers
)
)
rmarkdown::pandoc_convert(
input = "chapter_3_calculus_basics_ode_tutorial.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "chapter_03.md",        # Desired output filename
options = c(
"--from=html+tex_math_dollars",        # Enable TeX math using $...$
"--to=gfm+tex_math_dollars",           # GitHub-Flavored Markdown with math
"--mathjax",                            # Render math via MathJax
"--wrap=none",                          # Disable line wrapping
"--atx-headers"                         # Use hash-style headers
)
)
rmarkdown::pandoc_convert(
input = "chapter_4_mathematical_modeling_ode_tutorial.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "chapter_04.md",        # Desired output filename
options = c(
"--from=html+tex_math_dollars",        # Enable TeX math using $...$
"--to=gfm+tex_math_dollars",           # GitHub-Flavored Markdown with math
"--mathjax",                            # Render math via MathJax
"--wrap=none",                          # Disable line wrapping
"--atx-headers"                         # Use hash-style headers
)
)
rmarkdown::pandoc_convert(
input = "chapter_5_programming_python_r_ode_tutorial.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "chapter_05.md",        # Desired output filename
options = c(
"--from=html+tex_math_dollars",        # Enable TeX math using $...$
"--to=gfm+tex_math_dollars",           # GitHub-Flavored Markdown with math
"--mathjax",                            # Render math via MathJax
"--wrap=none",                          # Disable line wrapping
"--atx-headers"                         # Use hash-style headers
)
)
rmarkdown::pandoc_convert(
input = "chapter_5_programming_python_r_ode_tutorial.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "chapter_05.md",        # Desired output filename
options = c(
"--from=html+tex_math_dollars",        # Enable TeX math using $...$
"--to=gfm+tex_math_dollars",           # GitHub-Flavored Markdown with math
"--mathjax",                            # Render math via MathJax
"--wrap=none",                          # Disable line wrapping
"--atx-headers"                         # Use hash-style headers
)
)
rmarkdown::pandoc_convert(
input = "chapter_6_what_is_ode_tutorial.html",        # Path to your source HTML file
to    = "markdown",          # Target format
output = "chapter_06.md",        # Desired output filename
options = c(
"--from=html+tex_math_dollars",        # Enable TeX math using $...$
"--to=gfm+tex_math_dollars",           # GitHub-Flavored Markdown with math
"--mathjax",                            # Render math via MathJax
"--wrap=none",                          # Disable line wrapping
"--atx-headers"                         # Use hash-style headers
)
)
