<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 2: Review of Trigonometry - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-chtml.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-r.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    
    <style>
        .code-container {
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #374151;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background: #4B5563;
        }
        .theorem-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .definition-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .example-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .chart-container {
            height: 400px !important;
            margin: 20px 0;
        }
         {
            .no-print { display: none !important; }
            body { font-size: 12pt; line-height: 1.4; }
            .chart-container { height: 300px !important; }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 leading-relaxed">
    <div class="max-w-6xl mx-auto p-8 bg-white min-h-screen">
        <!-- Header -->
        <header class="mb-12 text-center border-b-4 border-blue-600 pb-6">
            <h1 class="text-5xl font-bold text-blue-800 mb-4">
                <i class="fas fa-wave-square mr-4"></i>
                Chapter 2: Review of Trigonometry
            </h1>
            <p class="text-xl text-gray-600 italic">Comprehensive ODE Tutorial - Part 1</p>
            <div class="mt-4 flex justify-center space-x-6 text-sm text-gray-500">
                <span><i class="fas fa-book mr-2"></i>Mathematical Foundations</span>
                <span><i class="fas fa-code mr-2"></i>Python & R Integration</span>
                <span><i class="fas fa-chart-line mr-2"></i>Visualization Tools</span>
            </div>
        </header>

        <!-- Table of Contents -->
        <nav class="bg-blue-50 p-6 rounded-lg mb-12 border-l-4 border-blue-500">
            <h2 class="text-2xl font-bold text-blue-800 mb-4">
                <i class="fas fa-list-ol mr-2"></i>Table of Contents
            </h2>
            <div class="grid md:grid-cols-2 gap-4">
                <ul class="space-y-2">
                    <li><a href="#introduction" class="text-blue-600 hover:underline">2.1 Introduction to Trigonometry</a></li>
                    <li><a href="#unit-circle" class="text-blue-600 hover:underline">2.2 The Unit Circle</a></li>
                    <li><a href="#basic-functions" class="text-blue-600 hover:underline">2.3 Basic Trigonometric Functions</a></li>
                    <li><a href="#identities" class="text-blue-600 hover:underline">2.4 Fundamental Identities</a></li>
                </ul>
                <ul class="space-y-2">
                    <li><a href="#graphing" class="text-blue-600 hover:underline">2.5 Graphing Trigonometric Functions</a></li>
                    <li><a href="#applications" class="text-blue-600 hover:underline">2.6 Applications to ODEs</a></li>
                    <li><a href="#programming" class="text-blue-600 hover:underline">2.7 Programming Implementation</a></li>
                    <li><a href="#exercises" class="text-blue-600 hover:underline">2.8 Exercises and Projects</a></li>
                </ul>
            </div>
        </nav>

        <!-- Section 2.1: Introduction -->
        <section id="introduction" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
                <i class="fas fa-compass mr-2"></i>2.1 Introduction to Trigonometry
            </h2>
            
            <div class="bg-green-50 p-6 rounded-lg border-l-4 border-green-500 mb-8">
                <h3 class="text-xl font-bold text-green-800 mb-3">Why Trigonometry in Differential Equations?</h3>
                <p class="text-gray-700 leading-relaxed">
                    Trigonometric functions are fundamental to differential equations because they naturally arise as solutions to many physical systems involving oscillatory behavior. From simple harmonic motion to complex wave phenomena, understanding trigonometry is essential for modeling periodic and oscillatory systems.
                </p>
            </div>

            <div class="definition-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-book-open mr-2"></i>Definition: Trigonometric Functions
                </h3>
                <p class="mb-4">
                    For a right triangle with angle $\theta$, opposite side $o$, adjacent side $a$, and hypotenuse $h$:
                </p>
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <div class="grid md:grid-cols-2 gap-4 text-center">
                        <div>
                            $$\sin \theta = \frac{o}{h}$$
                            $$\cos \theta = \frac{a}{h}$$
                            $$\tan \theta = \frac{o}{a}$$
                        </div>
                        <div>
                            $$\csc \theta = \frac{h}{o} = \frac{1}{\sin \theta}$$
                            $$\sec \theta = \frac{h}{a} = \frac{1}{\cos \theta}$$
                            $$\cot \theta = \frac{a}{o} = \frac{1}{\tan \theta}$$
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-500">
                <h3 class="text-xl font-bold text-yellow-800 mb-3">Historical Context</h3>
                <p class="text-gray-700">
                    Trigonometry originated from astronomical observations and navigation needs. The word "trigonometry" comes from Greek words meaning "triangle measurement." Ancient civilizations used trigonometric concepts to track celestial bodies and navigate across oceans.
                </p>
            </div>
        </section>

        <!-- Section 2.2: Unit Circle -->
        <section id="unit-circle" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
                <i class="fas fa-circle mr-2"></i>2.2 The Unit Circle
            </h2>

            <div class="theorem-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-star mr-2"></i>The Unit Circle Definition
                </h3>
                <p class="mb-4">
                    The unit circle is a circle with radius 1 centered at the origin. For any angle $\theta$ measured from the positive x-axis:
                </p>
                <div class="bg-white bg-opacity-20 p-4 rounded text-center">
                    $$\cos \theta = x\text{-coordinate}$$
                    $$\sin \theta = y\text{-coordinate}$$
                    $$x^2 + y^2 = 1 \quad \text{(Pythagorean Identity)}$$
                </div>
            </div>

            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Unit Circle Visualization</h3>
                    <div class="chart-container">
                        <canvas id="unitCircleChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Key Angles and Values</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-200">
                                <tr>
                                    <th class="p-2">Angle (°)</th>
                                    <th class="p-2">Angle (rad)</th>
                                    <th class="p-2">cos θ</th>
                                    <th class="p-2">sin θ</th>
                                </tr>
                            </thead>
                            <tbody class="text-center">
                                <tr class="border-b"><td class="p-2">0°</td><td class="p-2">0</td><td class="p-2">1</td><td class="p-2">0</td></tr>
                                <tr class="border-b"><td class="p-2">30°</td><td class="p-2">π/6</td><td class="p-2">√3/2</td><td class="p-2">1/2</td></tr>
                                <tr class="border-b"><td class="p-2">45°</td><td class="p-2">π/4</td><td class="p-2">√2/2</td><td class="p-2">√2/2</td></tr>
                                <tr class="border-b"><td class="p-2">60°</td><td class="p-2">π/3</td><td class="p-2">1/2</td><td class="p-2">√3/2</td></tr>
                                <tr><td class="p-2">90°</td><td class="p-2">π/2</td><td class="p-2">0</td><td class="p-2">1</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="example-box p-6 rounded-lg">
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>Example: Finding Coordinates on Unit Circle
                </h3>
                <p class="mb-4">Find the coordinates of the point on the unit circle corresponding to $\theta = 2\pi/3$.</p>
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <p><strong>Solution:</strong></p>
                    <p>$\theta = 2\pi/3 = 120°$ is in the second quadrant.</p>
                    <p>Reference angle: $\pi - 2\pi/3 = \pi/3$</p>
                    <p>$\cos(2\pi/3) = -\cos(\pi/3) = -1/2$</p>
                    <p>$\sin(2\pi/3) = \sin(\pi/3) = \sqrt{3}/2$</p>
                    <p><strong>Coordinates:</strong> $(-1/2, \sqrt{3}/2)$</p>
                </div>
            </div>
        </section>

        <!-- Section 2.3: Basic Functions -->
        <section id="basic-functions" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
                <i class="fas fa-wave-square mr-2"></i>2.3 Basic Trigonometric Functions
            </h2>

            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Sine Function</h3>
                    <div class="bg-blue-50 p-4 rounded-lg mb-4">
                        <ul class="space-y-2 text-sm">
                            <li><strong>Domain:</strong> $(-\infty, \infty)$</li>
                            <li><strong>Range:</strong> $[-1, 1]$</li>
                            <li><strong>Period:</strong> $2\pi$</li>
                            <li><strong>Amplitude:</strong> 1</li>
                            <li><strong>Odd Function:</strong> $\sin(-x) = -\sin(x)$</li>
                        </ul>
                    </div>
                    <div class="chart-container">
                        <canvas id="sineChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Cosine Function</h3>
                    <div class="bg-red-50 p-4 rounded-lg mb-4">
                        <ul class="space-y-2 text-sm">
                            <li><strong>Domain:</strong> $(-\infty, \infty)$</li>
                            <li><strong>Range:</strong> $[-1, 1]$</li>
                            <li><strong>Period:</strong> $2\pi$</li>
                            <li><strong>Amplitude:</strong> 1</li>
                            <li><strong>Even Function:</strong> $\cos(-x) = \cos(x)$</li>
                        </ul>
                    </div>
                    <div class="chart-container">
                        <canvas id="cosineChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="theorem-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-cogs mr-2"></i>General Form of Trigonometric Functions
                </h3>
                <p class="mb-4">The general forms of sine and cosine functions are:</p>
                <div class="bg-white bg-opacity-20 p-4 rounded text-center">
                    $$y = A \sin(Bx + C) + D$$
                    $$y = A \cos(Bx + C) + D$$
                </div>
                <div class="mt-4 grid md:grid-cols-2 gap-4">
                    <ul class="space-y-2">
                        <li><strong>A:</strong> Amplitude (vertical stretch)</li>
                        <li><strong>B:</strong> Frequency factor (horizontal compression)</li>
                    </ul>
                    <ul class="space-y-2">
                        <li><strong>C:</strong> Phase shift (horizontal translation)</li>
                        <li><strong>D:</strong> Vertical shift</li>
                    </ul>
                </div>
                <p class="mt-4"><strong>Period:</strong> $\frac{2\pi}{|B|}$</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Transformations Visualization</h3>
                <div class="chart-container">
                    <canvas id="transformationsChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Section 2.4: Identities -->
        <section id="identities" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
                <i class="fas fa-equals mr-2"></i>2.4 Fundamental Trigonometric Identities
            </h2>

            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <div class="theorem-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4">Pythagorean Identities</h3>
                    <div class="bg-white bg-opacity-20 p-4 rounded space-y-3 text-center">
                        <div>$$\sin^2 \theta + \cos^2 \theta = 1$$</div>
                        <div>$$1 + \tan^2 \theta = \sec^2 \theta$$</div>
                        <div>$$1 + \cot^2 \theta = \csc^2 \theta$$</div>
                    </div>
                </div>
                <div class="definition-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4">Reciprocal Identities</h3>
                    <div class="bg-white bg-opacity-20 p-4 rounded space-y-3 text-center">
                        <div>$$\csc \theta = \frac{1}{\sin \theta}$$</div>
                        <div>$$\sec \theta = \frac{1}{\cos \theta}$$</div>
                        <div>$$\cot \theta = \frac{1}{\tan \theta}$$</div>
                    </div>
                </div>
            </div>

            <div class="example-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-bold mb-4">Sum and Difference Formulas</h3>
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="text-center space-y-3">
                            <h4 class="font-bold">Sine Formulas</h4>
                            <div>$$\sin(A \pm B) = \sin A \cos B \pm \cos A \sin B$$</div>
                        </div>
                        <div class="text-center space-y-3">
                            <h4 class="font-bold">Cosine Formulas</h4>
                            <div>$$\cos(A \pm B) = \cos A \cos B \mp \sin A \sin B$$</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="theorem-box p-6 rounded-lg">
                <h3 class="text-xl font-bold mb-4">Double Angle Formulas</h3>
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <div class="grid md:grid-cols-3 gap-4 text-center space-y-2">
                        <div>
                            <h4 class="font-bold mb-2">Sine</h4>
                            $$\sin(2\theta) = 2\sin \theta \cos \theta$$
                        </div>
                        <div>
                            <h4 class="font-bold mb-2">Cosine</h4>
                            $$\cos(2\theta) = \cos^2 \theta - \sin^2 \theta$$
                            $$= 2\cos^2 \theta - 1$$
                            $$= 1 - 2\sin^2 \theta$$
                        </div>
                        <div>
                            <h4 class="font-bold mb-2">Tangent</h4>
                            $$\tan(2\theta) = \frac{2\tan \theta}{1 - \tan^2 \theta}$$
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2.5: Graphing -->
        <section id="graphing" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
                <i class="fas fa-chart-line mr-2"></i>2.5 Graphing Trigonometric Functions
            </h2>

            <div class="bg-green-50 p-6 rounded-lg border-l-4 border-green-500 mb-8">
                <h3 class="text-xl font-bold text-green-800 mb-3">Key Graphing Concepts</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <ul class="space-y-2">
                        <li><strong>Amplitude:</strong> Maximum displacement from equilibrium</li>
                        <li><strong>Period:</strong> Length of one complete cycle</li>
                        <li><strong>Frequency:</strong> Number of cycles per unit interval</li>
                    </ul>
                    <ul class="space-y-2">
                        <li><strong>Phase Shift:</strong> Horizontal displacement</li>
                        <li><strong>Vertical Shift:</strong> Up or down translation</li>
                        <li><strong>Reflection:</strong> About x-axis or y-axis</li>
                    </ul>
                </div>
            </div>

            <div class="mb-8">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Interactive Sine and Cosine Comparison</h3>
                <div class="chart-container">
                    <canvas id="sineCosineChart"></canvas>
                </div>
            </div>

            <div class="example-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-calculator mr-2"></i>Example: Analyzing $y = 3\sin(2x - \pi/3) + 1$
                </h3>
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <p><strong>Given:</strong> $y = 3\sin(2x - \pi/3) + 1$</p>
                            <p><strong>Amplitude:</strong> $|A| = 3$</p>
                            <p><strong>Period:</strong> $\frac{2\pi}{B} = \frac{2\pi}{2} = \pi$</p>
                            <p><strong>Phase Shift:</strong> $\frac{C}{B} = \frac{\pi/3}{2} = \frac{\pi}{6}$ (right)</p>
                            <p><strong>Vertical Shift:</strong> $D = 1$ (up)</p>
                        </div>
                        <div>
                            <p><strong>Range:</strong> $[1-3, 1+3] = [-2, 4]$</p>
                            <p><strong>Key Points:</strong></p>
                            <ul class="text-sm space-y-1">
                                <li>Start: $x = \pi/6$</li>
                                <li>Maximum: $x = \pi/6 + \pi/4 = 5\pi/12$</li>
                                <li>Zero: $x = \pi/6 + \pi/2 = 2\pi/3$</li>
                                <li>Minimum: $x = \pi/6 + 3\pi/4 = 11\pi/12$</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Function Transformation Visualization</h3>
                <div class="chart-container">
                    <canvas id="functionTransformChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Section 2.6: Applications -->
        <section id="applications" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
                <i class="fas fa-cogs mr-2"></i>2.6 Applications to Differential Equations
            </h2>

            <div class="theorem-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-atom mr-2"></i>Simple Harmonic Motion
                </h3>
                <p class="mb-4">The differential equation for simple harmonic motion is:</p>
                <div class="bg-white bg-opacity-20 p-4 rounded text-center">
                    $$\frac{d^2x}{dt^2} + \omega^2 x = 0$$
                </div>
                <p class="mt-4">The general solution involves trigonometric functions:</p>
                <div class="bg-white bg-opacity-20 p-4 rounded text-center">
                    $$x(t) = A\cos(\omega t + \phi)$$
                </div>
                <p class="mt-4">where $A$ is amplitude, $\omega$ is angular frequency, and $\phi$ is the phase constant.</p>
            </div>

            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <div class="definition-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4">Physical Examples</h3>
                    <ul class="space-y-3">
                        <li><strong>Mass-Spring System:</strong> $m\frac{d^2x}{dt^2} = -kx$</li>
                        <li><strong>Pendulum:</strong> $\frac{d^2\theta}{dt^2} = -\frac{g}{L}\sin\theta$</li>
                        <li><strong>RLC Circuit:</strong> $L\frac{d^2q}{dt^2} + R\frac{dq}{dt} + \frac{q}{C} = 0$</li>
                        <li><strong>Wave Equation:</strong> $\frac{\partial^2 u}{\partial t^2} = c^2\frac{\partial^2 u}{\partial x^2}$</li>
                    </ul>
                </div>
                <div class="example-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4">Damped Oscillations</h3>
                    <p class="mb-4">For a damped harmonic oscillator:</p>
                    <div class="bg-white bg-opacity-20 p-4 rounded text-center">
                        $$\frac{d^2x}{dt^2} + 2\gamma\frac{dx}{dt} + \omega_0^2 x = 0$$
                    </div>
                    <p class="mt-4">Solutions depend on the damping coefficient:</p>
                    <ul class="space-y-2 text-sm">
                        <li><strong>Underdamped:</strong> $x(t) = Ae^{-\gamma t}\cos(\omega_d t + \phi)$</li>
                        <li><strong>Critically damped:</strong> $x(t) = (A + Bt)e^{-\gamma t}$</li>
                        <li><strong>Overdamped:</strong> $x(t) = Ae^{r_1 t} + Be^{r_2 t}$</li>
                    </ul>
                </div>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Oscillation Types Comparison</h3>
                <div class="chart-container">
                    <canvas id="oscillationChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Section 2.7: Programming -->
        <section id="programming" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
                <i class="fas fa-code mr-2"></i>2.7 Programming Implementation
            </h2>

            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <div>
                    <h3 class="text-xl font-bold text-blue-600 mb-4">
                        <i class="fab fa-python mr-2"></i>Python Implementation
                    </h3>
                    <div class="code-container">
                        <button class="copy-btn" onclick="copyCode('python-basic')">Copy</button>
                        <pre class="bg-gray-900 text-gray-100 p-6 rounded-lg overflow-x-auto"><code id="python-basic" class="language-python">import numpy as np
import matplotlib.pyplot as plt
from math import pi, sin, cos, tan

# Basic trigonometric function plotting
def plot_trig_functions():
    """Plot basic trigonometric functions"""
    x = np.linspace(-2*pi, 2*pi, 1000)
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Sine function
    axes[0,0].plot(x, np.sin(x), 'b-', linewidth=2, label='sin(x)')
    axes[0,0].set_title('Sine Function')
    axes[0,0].grid(True, alpha=0.3)
    axes[0,0].set_ylim(-1.5, 1.5)
    axes[0,0].legend()
    
    # Cosine function
    axes[0,1].plot(x, np.cos(x), 'r-', linewidth=2, label='cos(x)')
    axes[0,1].set_title('Cosine Function')
    axes[0,1].grid(True, alpha=0.3)
    axes[0,1].set_ylim(-1.5, 1.5)
    axes[0,1].legend()
    
    # Tangent function
    y_tan = np.tan(x)
    y_tan[np.abs(y_tan) > 10] = np.nan  # Remove discontinuities
    axes[1,0].plot(x, y_tan, 'g-', linewidth=2, label='tan(x)')
    axes[1,0].set_title('Tangent Function')
    axes[1,0].grid(True, alpha=0.3)
    axes[1,0].set_ylim(-5, 5)
    axes[1,0].legend()
    
    # Combined sine and cosine
    axes[1,1].plot(x, np.sin(x), 'b-', linewidth=2, label='sin(x)')
    axes[1,1].plot(x, np.cos(x), 'r-', linewidth=2, label='cos(x)')
    axes[1,1].set_title('Sine and Cosine Comparison')
    axes[1,1].grid(True, alpha=0.3)
    axes[1,1].set_ylim(-1.5, 1.5)
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.show()

# Unit circle visualization
def plot_unit_circle():
    """Create interactive unit circle plot"""
    fig, ax = plt.subplots(figsize=(8, 8))
    
    # Draw unit circle
    theta = np.linspace(0, 2*pi, 100)
    x_circle = np.cos(theta)
    y_circle = np.sin(theta)
    ax.plot(x_circle, y_circle, 'k-', linewidth=2)
    
    # Key angles
    key_angles = [0, pi/6, pi/4, pi/3, pi/2, 2*pi/3, 3*pi/4, 5*pi/6, pi,
                  7*pi/6, 5*pi/4, 4*pi/3, 3*pi/2, 5*pi/3, 7*pi/4, 11*pi/6]
    
    for angle in key_angles:
        x = cos(angle)
        y = sin(angle)
        ax.plot(x, y, 'ro', markersize=8)
        ax.plot([0, x], [0, y], 'b--', alpha=0.7)
        
        # Add angle labels
        label_x = 1.15 * x
        label_y = 1.15 * y
        angle_deg = int(np.degrees(angle))
        ax.text(label_x, label_y, f'{angle_deg}°', ha='center', va='center')
    
    ax.set_xlim(-1.5, 1.5)
    ax.set_ylim(-1.5, 1.5)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.axhline(y=0, color='k', linewidth=0.5)
    ax.axvline(x=0, color='k', linewidth=0.5)
    ax.set_title('Unit Circle with Key Angles')
    plt.show()

# Advanced transformations
def plot_transformations(A=1, B=1, C=0, D=0):
    """Plot y = A*sin(B*x + C) + D with parameters"""
    x = np.linspace(-2*pi, 2*pi, 1000)
    y_original = np.sin(x)
    y_transformed = A * np.sin(B * x + C) + D
    
    plt.figure(figsize=(12, 6))
    plt.plot(x, y_original, 'b--', alpha=0.7, label='y = sin(x)')
    plt.plot(x, y_transformed, 'r-', linewidth=2, 
             label=f'y = {A}sin({B}x + {C}) + {D}')
    
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.title(f'Transformation: A={A}, B={B}, C={C}, D={D}')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.show()

# Execute examples
if __name__ == "__main__":
    plot_trig_functions()
    plot_unit_circle()
    plot_transformations(A=2, B=0.5, C=pi/4, D=1)</code></pre>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-xl font-bold text-red-600 mb-4">
                        <i class="fab fa-r-project mr-2"></i>R Implementation
                    </h3>
                    <div class="code-container">
                        <button class="copy-btn" onclick="copyCode('r-basic')">Copy</button>
                        <pre class="bg-gray-900 text-gray-100 p-6 rounded-lg overflow-x-auto"><code id="r-basic" class="language-r"># Load required libraries
library(ggplot2)
library(gridExtra)
library(dplyr)

# Basic trigonometric function plotting
plot_trig_functions <- function() {
  # Create data
  x <- seq(-2*pi, 2*pi, length.out = 1000)
  data <- data.frame(
    x = rep(x, 3),
    y = c(sin(x), cos(x), tan(pmax(pmin(x, pi/2 - 0.01), -pi/2 + 0.01))),
    func = rep(c("sin(x)", "cos(x)", "tan(x)"), each = length(x))
  )
  
  # Create plots
  p1 <- ggplot(data[data$func == "sin(x)", ], aes(x, y)) +
    geom_line(color = "blue", size = 1) +
    labs(title = "Sine Function", x = "x", y = "sin(x)") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
  
  p2 <- ggplot(data[data$func == "cos(x)", ], aes(x, y)) +
    geom_line(color = "red", size = 1) +
    labs(title = "Cosine Function", x = "x", y = "cos(x)") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
  
  # Combined plot
  combined_data <- data.frame(
    x = rep(x, 2),
    y = c(sin(x), cos(x)),
    func = rep(c("sin(x)", "cos(x)"), each = length(x))
  )
  
  p3 <- ggplot(combined_data, aes(x, y, color = func)) +
    geom_line(size = 1) +
    scale_color_manual(values = c("cos(x)" = "red", "sin(x)" = "blue")) +
    labs(title = "Sine and Cosine Comparison", 
         x = "x", y = "y", color = "Function") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
  
  # Display plots
  grid.arrange(p1, p2, p3, ncol = 2)
}

# Unit circle visualization
plot_unit_circle <- function() {
  # Create unit circle
  theta <- seq(0, 2*pi, length.out = 100)
  circle_data <- data.frame(x = cos(theta), y = sin(theta))
  
  # Key angles
  key_angles <- c(0, pi/6, pi/4, pi/3, pi/2, 2*pi/3, 3*pi/4, 5*pi/6, 
                  pi, 7*pi/6, 5*pi/4, 4*pi/3, 3*pi/2, 5*pi/3, 7*pi/4, 11*pi/6)
  
  key_points <- data.frame(
    x = cos(key_angles),
    y = sin(key_angles),
    angle = key_angles * 180 / pi
  )
  
  # Create plot
  ggplot() +
    geom_path(data = circle_data, aes(x, y), size = 1) +
    geom_point(data = key_points, aes(x, y), color = "red", size = 3) +
    geom_segment(data = key_points, aes(x = 0, y = 0, xend = x, yend = y), 
                 color = "blue", alpha = 0.7, linetype = "dashed") +
    geom_text(data = key_points, aes(x = 1.15*x, y = 1.15*y, 
                                    label = paste0(round(angle), "°")), 
              size = 3, hjust = 0.5, vjust = 0.5) +
    coord_equal() +
    xlim(-1.5, 1.5) +
    ylim(-1.5, 1.5) +
    labs(title = "Unit Circle with Key Angles", x = "cos(θ)", y = "sin(θ)") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
}

# Function transformations
plot_transformations <- function(A = 1, B = 1, C = 0, D = 0) {
  x <- seq(-2*pi, 2*pi, length.out = 1000)
  
  data <- data.frame(
    x = rep(x, 2),
    y = c(sin(x), A * sin(B * x + C) + D),
    func = rep(c("sin(x)", paste0(A, "sin(", B, "x + ", C, ") + ", D)), 
               each = length(x))
  )
  
  ggplot(data, aes(x, y, color = func, linetype = func)) +
    geom_line(size = 1) +
    scale_color_manual(values = c("sin(x)" = "blue", 
                                 paste0(A, "sin(", B, "x + ", C, ") + ", D) = "red")) +
    scale_linetype_manual(values = c("sin(x)" = "dashed", 
                                    paste0(A, "sin(", B, "x + ", C, ") + ", D) = "solid")) +
    labs(title = paste("Transformation: A =", A, ", B =", B, ", C =", C, ", D =", D),
         x = "x", y = "y", color = "Function", linetype = "Function") +
    theme_minimal() +
    geom_hline(yintercept = 0, alpha = 0.5) +
    geom_vline(xintercept = 0, alpha = 0.5)
}

# Execute examples
plot_trig_functions()
plot_unit_circle()
plot_transformations(A = 2, B = 0.5, C = pi/4, D = 1)</code></pre>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-500 mb-8">
                <h3 class="text-xl font-bold text-yellow-800 mb-4">Advanced Programming Examples</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">Python - Numerical Integration</h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode('python-advanced')">Copy</button>
                            <pre class="bg-gray-800 text-gray-100 p-4 rounded text-sm"><code id="python-advanced" class="language-python">from scipy import integrate
import numpy as np

# Integrate sin(x) from 0 to pi
result, error = integrate.quad(np.sin, 0, np.pi)
print(f"∫sin(x)dx from 0 to π = {result:.6f}")

# Verify: should equal 2
print(f"Analytical result: {-np.cos(np.pi) + np.cos(0)}")

# Fourier series approximation
def fourier_series(x, n_terms=5):
    result = np.zeros_like(x)
    for n in range(1, n_terms + 1):
        if n % 2 == 1:  # Odd terms only
            result += (4 / (np.pi * n)) * np.sin(n * x)
    return result</code></pre>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">R - Statistical Analysis</h4>
                        <div class="code-container">
                            <button class="copy-btn" onclick="copyCode('r-advanced')">Copy</button>
                            <pre class="bg-gray-800 text-gray-100 p-4 rounded text-sm"><code id="r-advanced" class="language-r"># Generate sinusoidal data with noise
set.seed(123)
x <- seq(0, 4*pi, length.out = 100)
y_true <- 2 * sin(x + pi/4) + 1
noise <- rnorm(100, 0, 0.3)
y_observed <- y_true + noise

# Fit sinusoidal model
library(nls2)
model <- nls(y_observed ~ A * sin(B * x + C) + D,
             start = list(A = 1, B = 1, C = 0, D = 0))

# Extract parameters
params <- coef(model)
print(params)

# Calculate R-squared
ss_res <- sum(residuals(model)^2)
ss_tot <- sum((y_observed - mean(y_observed))^2)
r_squared <- 1 - (ss_res / ss_tot)
print(paste("R-squared:", r_squared))</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2.8: Exercises -->
        <section id="exercises" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-800 mb-6 border-b-2 border-blue-200 pb-2">
                <i class="fas fa-pencil-alt mr-2"></i>2.8 Exercises and Projects
            </h2>

            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
                    <h3 class="text-xl font-bold text-blue-800 mb-4">
                        <i class="fas fa-tasks mr-2"></i>Practice Problems
                    </h3>
                    <ol class="space-y-3 text-sm">
                        <li><strong>Problem 1:</strong> Find the exact values of $\sin(75°)$ and $\cos(75°)$ using sum formulas.</li>
                        <li><strong>Problem 2:</strong> Solve $2\sin^2(x) - 3\sin(x) + 1 = 0$ for $x \in [0, 2\pi]$.</li>
                        <li><strong>Problem 3:</strong> Graph $y = 3\cos(2x - \pi/3) - 1$ and identify all key features.</li>
                        <li><strong>Problem 4:</strong> Prove the identity: $\frac{1 - \cos(2\theta)}{2} = \sin^2(\theta)$</li>
                        <li><strong>Problem 5:</strong> Find the period and amplitude of $y = 4\sin(3x + \pi/2) + 2$.</li>
                    </ol>
                </div>
                
                <div class="bg-green-50 p-6 rounded-lg border-l-4 border-green-500">
                    <h3 class="text-xl font-bold text-green-800 mb-4">
                        <i class="fas fa-project-diagram mr-2"></i>Programming Projects
                    </h3>
                    <ol class="space-y-3 text-sm">
                        <li><strong>Project 1:</strong> Create an interactive unit circle app that shows coordinates as the user clicks on different angles.</li>
                        <li><strong>Project 2:</strong> Implement a Fourier series approximation to approximate square wave and sawtooth functions.</li>
                        <li><strong>Project 3:</strong> Model and visualize simple harmonic motion with different damping coefficients.</li>
                        <li><strong>Project 4:</strong> Create a trigonometric identity verification tool using symbolic computation.</li>
                        <li><strong>Project 5:</strong> Analyze real-world periodic data (temperature, sales, etc.) using trigonometric regression.</li>
                    </ol>
                </div>
            </div>

            <div class="example-box p-6 rounded-lg mb-8">
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>Sample Solution: Problem 1
                </h3>
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <p><strong>Find sin(75°) and cos(75°):</strong></p>
                    <p>Notice that $75° = 45° + 30°$</p>
                    
                    <p><strong>For sin(75°):</strong></p>
                    <p>$\sin(75°) = \sin(45° + 30°)$</p>
                    <p>$= \sin(45°)\cos(30°) + \cos(45°)\sin(30°)$</p>
                    <p>$= \frac{\sqrt{2}}{2} \cdot \frac{\sqrt{3}}{2} + \frac{\sqrt{2}}{2} \cdot \frac{1}{2}$</p>
                    <p>$= \frac{\sqrt{6}}{4} + \frac{\sqrt{2}}{4} = \frac{\sqrt{6} + \sqrt{2}}{4}$</p>
                    
                    <p><strong>For cos(75°):</strong></p>
                    <p>$\cos(75°) = \cos(45° + 30°)$</p>
                    <p>$= \cos(45°)\cos(30°) - \sin(45°)\sin(30°)$</p>
                    <p>$= \frac{\sqrt{2}}{2} \cdot \frac{\sqrt{3}}{2} - \frac{\sqrt{2}}{2} \cdot \frac{1}{2}$</p>
                    <p>$= \frac{\sqrt{6}}{4} - \frac{\sqrt{2}}{4} = \frac{\sqrt{6} - \sqrt{2}}{4}$</p>
                </div>
            </div>

            <div class="theorem-box p-6 rounded-lg">
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-trophy mr-2"></i>Chapter Summary
                </h3>
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <p class="mb-4">In this chapter, we've covered the essential trigonometric concepts needed for differential equations:</p>
                    <ul class="space-y-2">
                        <li>✓ Unit circle and trigonometric function definitions</li>
                        <li>✓ Fundamental trigonometric identities</li>
                        <li>✓ Graphing and transformations of trigonometric functions</li>
                        <li>✓ Applications to oscillatory systems and differential equations</li>
                        <li>✓ Programming implementations in Python and R</li>
                        <li>✓ Real-world modeling applications</li>
                    </ul>
                    <p class="mt-4"><strong>Next Chapter Preview:</strong> We'll build on these trigonometric foundations to explore calculus concepts, including limits, derivatives, and integrals, which are crucial for understanding differential equations.</p>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="mt-16 pt-8 border-t-2 border-gray-200 text-center text-gray-600">
            <div class="flex justify-center space-x-8 mb-4">
                <span><i class="fas fa-book mr-2"></i>Comprehensive ODE Tutorial</span>
                <span><i class="fas fa-graduation-cap mr-2"></i>Academic Excellence</span>
                <span><i class="fas fa-code mr-2"></i>Practical Implementation</span>
            </div>
            <p class="text-sm">
                &copy; 2024 ODE Tutorial Series. Designed for mathematical rigor and practical application.
            </p>
        </footer>
    </div>

    <script>
        // Chart.js configurations and implementations
        
        // Unit Circle Chart
        const unitCircleCtx = document.getElementById('unitCircleChart').getContext('2d');
        new Chart(unitCircleCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Unit Circle',
                    data: Array.from({length: 100}, (_, i) => {
                        const theta = i * 2 * Math.PI / 99;
                        return {x: Math.cos(theta), y: Math.sin(theta)};
                    }),
                    borderColor: 'blue',
                    backgroundColor: 'transparent',
                    showLine: true,
                    pointRadius: 0,
                    borderWidth: 2
                }, {
                    label: 'Key Points',
                    data: [0, Math.PI/6, Math.PI/4, Math.PI/3, Math.PI/2, 2*Math.PI/3, 3*Math.PI/4, 5*Math.PI/6, Math.PI].map(theta => ({
                        x: Math.cos(theta),
                        y: Math.sin(theta)
                    })),
                    backgroundColor: 'red',
                    borderColor: 'red',
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { min: -1.2, max: 1.2, title: { display: true, text: 'cos θ' } },
                    y: { min: -1.2, max: 1.2, title: { display: true, text: 'sin θ' } }
                },
                plugins: {
                    title: { display: true, text: 'Unit Circle' }
                }
            }
        });

        // Sine Chart
        const sineCtx = document.getElementById('sineChart').getContext('2d');
        const xValues = Array.from({length: 200}, (_, i) => -2*Math.PI + i * 4*Math.PI / 199);
        new Chart(sineCtx, {
            type: 'line',
            data: {
                labels: xValues.map(x => x.toFixed(2)),
                datasets: [{
                    label: 'sin(x)',
                    data: xValues.map(x => Math.sin(x)),
                    borderColor: 'blue',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'x' } },
                    y: { min: -1.5, max: 1.5, title: { display: true, text: 'sin(x)' } }
                },
                plugins: {
                    title: { display: true, text: 'Sine Function' }
                }
            }
        });

        // Cosine Chart
        const cosineCtx = document.getElementById('cosineChart').getContext('2d');
        new Chart(cosineCtx, {
            type: 'line',
            data: {
                labels: xValues.map(x => x.toFixed(2)),
                datasets: [{
                    label: 'cos(x)',
                    data: xValues.map(x => Math.cos(x)),
                    borderColor: 'red',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'x' } },
                    y: { min: -1.5, max: 1.5, title: { display: true, text: 'cos(x)' } }
                },
                plugins: {
                    title: { display: true, text: 'Cosine Function' }
                }
            }
        });

        // Transformations Chart
        const transformationsCtx = document.getElementById('transformationsChart').getContext('2d');
        new Chart(transformationsCtx, {
            type: 'line',
            data: {
                labels: xValues.map(x => x.toFixed(2)),
                datasets: [{
                    label: 'sin(x)',
                    data: xValues.map(x => Math.sin(x)),
                    borderColor: 'blue',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 1,
                    borderDash: [5, 5]
                }, {
                    label: '2sin(0.5x + π/4) + 1',
                    data: xValues.map(x => 2 * Math.sin(0.5 * x + Math.PI/4) + 1),
                    borderColor: 'red',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'x' } },
                    y: { title: { display: true, text: 'y' } }
                },
                plugins: {
                    title: { display: true, text: 'Function Transformations' }
                }
            }
        });

        // Sine and Cosine Comparison
        const sineCosineCtx = document.getElementById('sineCosineChart').getContext('2d');
        new Chart(sineCosineCtx, {
            type: 'line',
            data: {
                labels: xValues.map(x => x.toFixed(2)),
                datasets: [{
                    label: 'sin(x)',
                    data: xValues.map(x => Math.sin(x)),
                    borderColor: 'blue',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }, {
                    label: 'cos(x)',
                    data: xValues.map(x => Math.cos(x)),
                    borderColor: 'red',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'x' } },
                    y: { min: -1.5, max: 1.5, title: { display: true, text: 'y' } }
                },
                plugins: {
                    title: { display: true, text: 'Sine vs Cosine Functions' }
                }
            }
        });

        // Function Transform Chart
        const functionTransformCtx = document.getElementById('functionTransformChart').getContext('2d');
        new Chart(functionTransformCtx, {
            type: 'line',
            data: {
                labels: xValues.map(x => x.toFixed(2)),
                datasets: [{
                    label: 'y = 3sin(2x - π/3) + 1',
                    data: xValues.map(x => 3 * Math.sin(2 * x - Math.PI/3) + 1),
                    borderColor: 'purple',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'x' } },
                    y: { title: { display: true, text: 'y' } }
                },
                plugins: {
                    title: { display: true, text: 'Example: y = 3sin(2x - π/3) + 1' }
                }
            }
        });

        // Oscillation Chart
        const oscillationCtx = document.getElementById('oscillationChart').getContext('2d');
        const tValues = Array.from({length: 200}, (_, i) => i * 10 / 199);
        new Chart(oscillationCtx, {
            type: 'line',
            data: {
                labels: tValues.map(t => t.toFixed(2)),
                datasets: [{
                    label: 'Undamped: cos(t)',
                    data: tValues.map(t => Math.cos(t)),
                    borderColor: 'blue',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }, {
                    label: 'Underdamped: e^(-0.1t)cos(t)',
                    data: tValues.map(t => Math.exp(-0.1*t) * Math.cos(t)),
                    borderColor: 'green',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }, {
                    label: 'Overdamped: e^(-0.5t)',
                    data: tValues.map(t => Math.exp(-0.5*t)),
                    borderColor: 'red',
                    backgroundColor: 'transparent',
                    pointRadius: 0,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'Time (t)' } },
                    y: { title: { display: true, text: 'Amplitude' } }
                },
                plugins: {
                    title: { display: true, text: 'Types of Oscillatory Motion' }
                }
            }
        });

        // Copy code functionality
        function copyCode(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                const button = element.parentElement.querySelector('.copy-btn');
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDhktqjflwAhyMAIgiegTRErvQOKhLQUthm74T5kdgV40f4vbe8xLG0q1OV%2B%2FOLkAShECDarp4oITskJ0JVxwnAksuplf%2FM8odDPZEEzxcXi6Cl%2Bd04An6rqycCn9vdYEVI7M%2Fw%2Bv7SICFwwt6fE67%2BHM0VS1XqtZvpY33GgAFQQ%2Bfem%2FWonO7Y%2FPSC8Ry0YtACK0pvmIk6DB47B%2Foeci%2FhhVd78Z39GWSklpoMyome241Wu8b4U0Uqp2%2Bnga1MgC63tz83QthPtc6pDGCm2EuZu4sqKf28x1%2FEURLOnG1%2FpXh2YhjRXYNlh7z3AtggoBAQ%2BB9h4QTdf3E6VY58m%2BVTBGq3Bn3wHr%2B3QoQT3ewtXAy8Lv5N1SchjyKJ%2FcdH2kYElUV8kUyI%2F1fHcmT2%2FEJHdejJuizgqHOrqEh8hvRcvAW%2FQuCB7HQ6SMfT2GaeWUxtancvzZBarzlkNyb%2FdP0UhMb8iGfSyWp71IUqqXVTH%2BYrkLA51zuG%2Bz%2Bw4TSHZEZgcgn10NDHGT%2BiPycvMsYEY%3D";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDhktqjflwAhyMAIgiegTRErvQOKhLQUthm74T5kdgV40f4vbe8xLG0q1OV+/OLkAShECDarp4oITskJ0JVxwnAksuplf/M8odDPZEEzxcXi6Cl+d04An6rqycCn9vdYEVI7M/w+v7SICFwwt6fE67+HM0VS1XqtZvpY33GgAFQQ+fem/WonO7Y/PSC8Ry0YtACK0pvmIk6DB47B/oeci/hhVd78Z39GWSklpoMyome241Wu8b4U0Uqp2+nga1MgC63tz83QthPtc6pDGCm2EuZu4sqKf28x1/EURLOnG1/pXh2YhjRXYNlh7z3AtggoBAQ+B9h4QTdf3E6VY58m+VTBGq3Bn3wHr+3QoQT3ewtXAy8Lv5N1SchjyKJ/cdH2kYElUV8kUyI/1fHcmT2/EJHdejJuizgqHOrqEh8hvRcvAW/QuCB7HQ6SMfT2GaeWUxtancvzZBarzlkNyb/dP0UhMb8iGfSyWp71IUqqXVTH+YrkLA51zuG+z+w4TSHZEZgcgn10NDHGT+iPycvMsYEY=";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    