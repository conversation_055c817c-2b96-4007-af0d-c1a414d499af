---
output:
  word_document: default
  html_document: default
---
<div class="max-w-6xl mx-auto px-4 py-8">

<div class="text-center mb-12">

<div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg shadow-lg">

#  Chapter 3: Basics of Calculus

Essential Calculus Foundations for Differential Equations

<div class="mt-4 text-sm opacity-75">

<span class="mr-4">ODE Tutorial Series</span> <span class="mr-4">Part 1: Mathematical Foundations</span> Chapter 3

</div>

</div>

</div>

<div class="bg-white rounded-lg shadow-md p-6 mb-8">

## Table of Contents

<div class="grid md:grid-cols-2 gap-4">

<div>

### 3.1 Limits and Continuity

- • Definition of Limits
- • Limit Laws and Properties
- • Continuity and Discontinuities
- • L'Hôpital's Rule

</div>

<div>

### 3.2 Differentiation

- • Definition of Derivative
- • Basic Differentiation Rules
- • Product and Quotient Rules
- • Chain Rule

</div>

<div>

### 3.3 Integration

- • Antiderivatives and Integrals
- • Substitution Method
- • Integration by Parts
- • Partial Fractions

</div>

<div>

### 3.4 Applications

- • Area Under Curves
- • Rate of Change Problems
- • Optimization
- • Preparation for ODEs

</div>

</div>

</div>

<div class="section mb-12">

<div class="bg-white rounded-lg shadow-md p-8">

## 3.1 Limits and Continuity

<div class="definition-box">

### Definition: Limit

The limit of a function $f(x)$ as $x$ approaches $a$ is $L$, written as:

$$\lim_{x \to a} f(x) = L$$

if for every $\varepsilon > 0$, there exists a $\delta > 0$ such that whenever $0 < |x - a| < \delta$, we have $|f(x) - L| < \varepsilon$.

</div>

### Fundamental Limit Laws

<div class="bg-gray-100 p-4 rounded-lg mb-6">

If $\lim_{x \to a} f(x) = L$ and $\lim_{x \to a} g(x) = M$, then:

- $\lim_{x \to a} [f(x) \pm g(x)] = L \pm M$
- $\lim_{x \to a} [f(x) \cdot g(x)] = L \cdot M$
- $\lim_{x \to a} \frac{f(x)}{g(x)} = \frac{L}{M}$ (if $M \neq 0$)
- $\lim_{x \to a} [f(x)]^n = L^n$

</div>

<div class="example-box">

### Example: Computing Limits

Let's compute $\lim_{x \to 2} \frac{x^2 - 4}{x - 2}$

<div class="code-block python-code">

Python (SymPy):

    import sympy as sp
    import numpy as np
    import matplotlib.pyplot as plt

    # Define the variable and function
    x = sp.Symbol('x')
    f = (x**2 - 4) / (x - 2)

    # Compute the limit
    limit_result = sp.limit(f, x, 2)
    print(f"Limit as x approaches 2: {limit_result}")

    # Simplify the function
    simplified = sp.simplify(f)
    print(f"Simplified form: {simplified}")

    # Create visualization
    x_vals = np.linspace(0.5, 3.5, 1000)
    x_vals = x_vals[x_vals != 2]  # Remove x = 2
    y_vals = (x_vals**2 - 4) / (x_vals - 2)

    plt.figure(figsize=(10, 6))
    plt.plot(x_vals, y_vals, 'b-', linewidth=2, label='f(x) = (x²-4)/(x-2)')
    plt.plot(2, 4, 'ro', markersize=8, fillstyle='none', 
             markeredgewidth=2, label='Removable discontinuity at (2,4)')
    plt.axhline(y=4, color='red', linestyle='--', alpha=0.5)
    plt.axvline(x=2, color='red', linestyle='--', alpha=0.5)
    plt.xlabel('x')
    plt.ylabel('f(x)')
    plt.title('Limit of (x²-4)/(x-2) as x approaches 2')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.ylim(2, 6)
    plt.show()
                            

</div>

<div class="code-block r-code">

R (Base R):

    library(ggplot2)

    # Define the function symbolically
    # Since (x^2 - 4)/(x - 2) = (x+2)(x-2)/(x-2) = x+2 for x ≠ 2

    # Compute limit numerically
    limit_value <- 2 + 2  # Direct substitution after simplification
    cat("Limit as x approaches 2:", limit_value, "\n")

    # Create data for plotting
    x_vals <- seq(0.5, 3.5, length.out = 1000)
    x_vals <- x_vals[x_vals != 2]  # Remove x = 2
    y_vals <- (x_vals^2 - 4) / (x_vals - 2)

    # Create the plot
    df <- data.frame(x = x_vals, y = y_vals)

    ggplot(df, aes(x = x, y = y)) +
      geom_line(color = "blue", size = 1.2) +
      geom_point(aes(x = 2, y = 4), color = "red", size = 4, shape = 1, stroke = 2) +
      geom_hline(yintercept = 4, color = "red", linetype = "dashed", alpha = 0.5) +
      geom_vline(xintercept = 2, color = "red", linetype = "dashed", alpha = 0.5) +
      labs(title = "Limit of (x²-4)/(x-2) as x approaches 2",
           x = "x", y = "f(x)") +
      ylim(2, 6) +
      theme_minimal() +
      theme(plot.title = element_text(hjust = 0.5, size = 14))
                            

</div>

</div>

<div class="theorem-box">

### L'Hôpital's Rule

If $\lim_{x \to a} f(x) = 0$ and $\lim_{x \to a} g(x) = 0$ (or both approach $\pm\infty$), then:

$$\lim_{x \to a} \frac{f(x)}{g(x)} = \lim_{x \to a} \frac{f'(x)}{g'(x)}$$

provided the limit on the right exists.

</div>

<div class="example-box">

### L'Hôpital's Rule Example

Evaluate: $\lim_{x \to 0} \frac{\sin x}{x}$

<div class="code-block python-code">

Python Implementation:

    import sympy as sp
    import numpy as np
    import matplotlib.pyplot as plt

    x = sp.Symbol('x')

    # Original function
    f = sp.sin(x) / x
    print(f"Original function: {f}")

    # Apply L'Hôpital's rule
    numerator = sp.sin(x)
    denominator = x
    num_derivative = sp.diff(numerator, x)
    den_derivative = sp.diff(denominator, x)

    print(f"Numerator derivative: {num_derivative}")
    print(f"Denominator derivative: {den_derivative}")

    # Compute limit using L'Hôpital's rule
    lhopital_limit = sp.limit(num_derivative/den_derivative, x, 0)
    print(f"Limit using L'Hôpital's rule: {lhopital_limit}")

    # Direct computation
    direct_limit = sp.limit(f, x, 0)
    print(f"Direct limit computation: {direct_limit}")

    # Visualization
    x_vals = np.linspace(-2*np.pi, 2*np.pi, 1000)
    x_vals = x_vals[x_vals != 0]  # Remove x = 0
    y_vals = np.sin(x_vals) / x_vals

    plt.figure(figsize=(12, 6))
    plt.plot(x_vals, y_vals, 'b-', linewidth=2, label='f(x) = sin(x)/x')
    plt.plot(0, 1, 'ro', markersize=8, fillstyle='none', 
             markeredgewidth=2, label='Limit point (0,1)')
    plt.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='y = 1')
    plt.xlabel('x')
    plt.ylabel('f(x)')
    plt.title('The Function sin(x)/x and its Limit as x → 0')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.ylim(-0.5, 1.5)
    plt.show()
                            

</div>

</div>

### Continuity

<div class="definition-box">

### Definition: Continuity

A function $f$ is continuous at $x = a$ if:

1.  $f(a)$ is defined
2.  $\lim_{x \to a} f(x)$ exists
3.  $\lim_{x \to a} f(x) = f(a)$

</div>

<div class="chart-container">

</div>

</div>

</div>

<div class="section mb-12">

<div class="bg-white rounded-lg shadow-md p-8">

## 3.2 Differentiation

<div class="definition-box">

### Definition: Derivative

The derivative of a function $f$ at $x = a$ is defined as:

$$f'(a) = \lim_{h \to 0} \frac{f(a+h) - f(a)}{h}$$

The derivative represents the instantaneous rate of change or the slope of the tangent line at that point.

</div>

### Basic Differentiation Rules

<div class="bg-gray-100 p-6 rounded-lg mb-6">

<div class="grid md:grid-cols-2 gap-6">

<div>

#### Power Rule

$\frac{d}{dx}[x^n] = nx^{n-1}$

#### Constant Rule

$\frac{d}{dx}[c] = 0$

#### Sum/Difference Rule

$\frac{d}{dx}[f(x) \pm g(x)] = f'(x) \pm g'(x)$

</div>

<div>

#### Exponential Functions

$\frac{d}{dx}[e^x] = e^x$

$\frac{d}{dx}[a^x] = a^x \ln(a)$

#### Trigonometric Functions

$\frac{d}{dx}[\sin x] = \cos x$

$\frac{d}{dx}[\cos x] = -\sin x$

</div>

</div>

</div>

<div class="theorem-box">

### Product Rule

If $f$ and $g$ are differentiable functions, then:

$$\frac{d}{dx}[f(x) \cdot g(x)] = f'(x) \cdot g(x) + f(x) \cdot g'(x)$$

</div>

<div class="theorem-box">

### Quotient Rule

If $f$ and $g$ are differentiable functions and $g(x) \neq 0$, then:

$$\frac{d}{dx}\left[\frac{f(x)}{g(x)}\right] = \frac{f'(x) \cdot g(x) - f(x) \cdot g'(x)}{[g(x)]^2}$$

</div>

<div class="theorem-box">

### Chain Rule

If $y = f(g(x))$ where both $f$ and $g$ are differentiable, then:

$$\frac{dy}{dx} = f'(g(x)) \cdot g'(x)$$

Or in Leibniz notation: $\frac{dy}{dx} = \frac{dy}{du} \cdot \frac{du}{dx}$

</div>

<div class="example-box">

### Comprehensive Differentiation Examples

<div class="code-block python-code">

Python (SymPy) - Advanced Differentiation:

    import sympy as sp
    import numpy as np
    import matplotlib.pyplot as plt

    x = sp.Symbol('x')

    # Define various functions
    functions = {
        'Power': x**3 + 2*x**2 - 5*x + 1,
        'Product': x**2 * sp.sin(x),
        'Quotient': sp.sin(x) / (x**2 + 1),
        'Chain': sp.sin(x**2 + 1),
        'Exponential': sp.exp(x) * sp.cos(x)
    }

    print("Function Differentiation Examples:")
    print("=" * 50)

    for name, func in functions.items():
        derivative = sp.diff(func, x)
        print(f"\n{name} Rule Example:")
        print(f"f(x) = {func}")
        print(f"f'(x) = {derivative}")
        
        # Simplify if possible
        simplified = sp.simplify(derivative)
        if simplified != derivative:
            print(f"Simplified: {simplified}")

    # Visualization of derivative as slope
    def plot_derivative_visualization():
        # Function: f(x) = x^3 - 3x^2 + 2x + 1
        f_expr = x**3 - 3*x**2 + 2*x + 1
        f_prime = sp.diff(f_expr, x)
        
        # Convert to numpy functions
        f_func = sp.lambdify(x, f_expr, 'numpy')
        f_prime_func = sp.lambdify(x, f_prime, 'numpy')
        
        x_vals = np.linspace(-1, 4, 1000)
        y_vals = f_func(x_vals)
        slope_vals = f_prime_func(x_vals)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Plot function
        ax1.plot(x_vals, y_vals, 'b-', linewidth=2, label='f(x) = x³ - 3x² + 2x + 1')
        
        # Add tangent lines at specific points
        points = [0, 1, 2, 3]
        for point in points:
            y_point = f_func(point)
            slope = f_prime_func(point)
            
            # Tangent line: y - y0 = m(x - x0)
            tangent_x = np.linspace(point - 0.5, point + 0.5, 100)
            tangent_y = y_point + slope * (tangent_x - point)
            
            ax1.plot(tangent_x, tangent_y, 'r--', alpha=0.7, linewidth=1)
            ax1.plot(point, y_point, 'ro', markersize=6)
            ax1.text(point, y_point + 0.5, f'slope = {slope:.2f}', ha='center')
        
        ax1.set_xlabel('x')
        ax1.set_ylabel('f(x)')
        ax1.set_title('Function and its Tangent Lines')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Plot derivative
        ax2.plot(x_vals, slope_vals, 'g-', linewidth=2, label="f'(x) = 3x² - 6x + 2")
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # Mark critical points where f'(x) = 0
        critical_points = sp.solve(f_prime, x)
        for cp in critical_points:
            if cp.is_real:
                cp_float = float(cp)
                ax2.plot(cp_float, 0, 'ro', markersize=8)
                ax2.text(cp_float, 0.5, f'Critical point\nx = {cp_float:.2f}', ha='center')
        
        ax2.set_xlabel('x')
        ax2.set_ylabel("f'(x)")
        ax2.set_title('Derivative Function')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        plt.tight_layout()
        plt.show()

    plot_derivative_visualization()
                            

</div>

<div class="code-block r-code">

R Implementation:

    library(ggplot2)
    library(gridExtra)

    # Define functions symbolically using derivatives
    # For demonstration, we'll work with f(x) = x^3 - 3x^2 + 2x + 1

    f <- function(x) x^3 - 3*x^2 + 2*x + 1
    f_prime <- function(x) 3*x^2 - 6*x + 2

    # Create data
    x_vals <- seq(-1, 4, length.out = 1000)
    y_vals <- f(x_vals)
    slope_vals <- f_prime(x_vals)

    # Plot function with tangent lines
    df1 <- data.frame(x = x_vals, y = y_vals)

    # Points for tangent lines
    points <- c(0, 1, 2, 3)
    tangent_data <- data.frame()

    for (point in points) {
      y_point <- f(point)
      slope <- f_prime(point)
      
      tangent_x <- seq(point - 0.5, point + 0.5, length.out = 100)
      tangent_y <- y_point + slope * (tangent_x - point)
      
      temp_df <- data.frame(
        x = tangent_x, 
        y = tangent_y, 
        group = paste("tangent", point)
      )
      tangent_data <- rbind(tangent_data, temp_df)
    }

    p1 <- ggplot(df1, aes(x = x, y = y)) +
      geom_line(color = "blue", size = 1.2) +
      geom_line(data = tangent_data, aes(group = group), 
                color = "red", linetype = "dashed", alpha = 0.7) +
      geom_point(data = data.frame(x = points, y = f(points)), 
                 color = "red", size = 3) +
      labs(title = "Function and its Tangent Lines",
           x = "x", y = "f(x)") +
      theme_minimal()

    # Plot derivative
    df2 <- data.frame(x = x_vals, y = slope_vals)

    # Find critical points (where derivative = 0)
    # For f'(x) = 3x^2 - 6x + 2 = 0
    # Using quadratic formula: x = (6 ± √(36-24))/6 = (6 ± √12)/6
    critical_points <- c((6 + sqrt(12))/6, (6 - sqrt(12))/6)

    p2 <- ggplot(df2, aes(x = x, y = y)) +
      geom_line(color = "green", size = 1.2) +
      geom_hline(yintercept = 0, color = "black", alpha = 0.3) +
      geom_point(data = data.frame(x = critical_points, y = rep(0, length(critical_points))), 
                 color = "red", size = 4) +
      labs(title = "Derivative Function",
           x = "x", y = "f'(x)") +
      theme_minimal()

    # Display plots
    grid.arrange(p1, p2, nrow = 2)

    # Print differentiation rules examples
    cat("Differentiation Rules Examples:\n")
    cat("==============================\n\n")

    cat("Power Rule: d/dx[x^n] = nx^(n-1)\n")
    cat("Example: d/dx[x^3] = 3x^2\n\n")

    cat("Product Rule: d/dx[f(x)g(x)] = f'(x)g(x) + f(x)g'(x)\n")
    cat("Example: d/dx[x^2 * sin(x)] = 2x*sin(x) + x^2*cos(x)\n\n")

    cat("Chain Rule: d/dx[f(g(x))] = f'(g(x)) * g'(x)\n")
    cat("Example: d/dx[sin(x^2)] = cos(x^2) * 2x\n\n")
                            

</div>

</div>

<div class="chart-container">

</div>

</div>

</div>

<div class="section mb-12">

<div class="bg-white rounded-lg shadow-md p-8">

## 3.3 Integration

<div class="definition-box">

### Definition: Antiderivative and Integral

An antiderivative of $f(x)$ is a function $F(x)$ such that $F'(x) = f(x)$.

The indefinite integral is written as: $\int f(x) \, dx = F(x) + C$

The definite integral represents the signed area under the curve:

$$\int_a^b f(x) \, dx = F(b) - F(a)$$

</div>

### Basic Integration Rules

<div class="bg-gray-100 p-6 rounded-lg mb-6">

<div class="grid md:grid-cols-2 gap-6">

<div>

#### Power Rule

$\int x^n \, dx = \frac{x^{n+1}}{n+1} + C$ (for $n \neq -1$)

#### Exponential Functions

$\int e^x \, dx = e^x + C$

$\int a^x \, dx = \frac{a^x}{\ln a} + C$

#### Logarithmic

$\int \frac{1}{x} \, dx = \ln|x| + C$

</div>

<div>

#### Trigonometric Functions

$\int \sin x \, dx = -\cos x + C$

$\int \cos x \, dx = \sin x + C$

$\int \sec^2 x \, dx = \tan x + C$

#### Inverse Trigonometric

$\int \frac{1}{\sqrt{1-x^2}} \, dx = \arcsin x + C$

$\int \frac{1}{1+x^2} \, dx = \arctan x + C$

</div>

</div>

</div>

<div class="theorem-box">

### Substitution Method (u-substitution)

For integrals of the form $\int f(g(x)) \cdot g'(x) \, dx$:

1.  Let $u = g(x)$, so $du = g'(x) \, dx$
2.  Substitute: $\int f(g(x)) \cdot g'(x) \, dx = \int f(u) \, du$
3.  Integrate with respect to $u$
4.  Substitute back: replace $u$ with $g(x)$

</div>

<div class="theorem-box">

### Integration by Parts

For products of functions:

$$\int u \, dv = uv - \int v \, du$$

**LIATE Rule** for choosing $u$: Logarithmic, Inverse trig, Algebraic, Trigonometric, Exponential

</div>

<div class="example-box">

### Advanced Integration Examples

<div class="code-block python-code">

Python (SymPy) - Integration Techniques:

    import sympy as sp
    import numpy as np
    import matplotlib.pyplot as plt
    from scipy import integrate

    x = sp.Symbol('x')

    # Integration examples
    integration_examples = {
        'Basic Power': x**3 + 2*x**2 - 5*x + 1,
        'Substitution': sp.sin(x**2) * x,  # u = x^2, du = 2x dx
        'By Parts': x * sp.exp(x),  # u = x, dv = e^x dx
        'Trigonometric': sp.sin(x) * sp.cos(x),
        'Rational': 1/(x**2 + 1),
        'Exponential': sp.exp(x) * sp.sin(x)
    }

    print("Integration Examples:")
    print("=" * 50)

    for name, func in integration_examples.items():
        try:
            indefinite_integral = sp.integrate(func, x)
            print(f"\n{name}:")
            print(f"∫ {func} dx = {indefinite_integral}")
            
            # Show steps for some examples
            if name == 'By Parts':
                print("Using integration by parts: u = x, dv = e^x dx")
                print("Then: du = dx, v = e^x")
                print("∫ x*e^x dx = x*e^x - ∫ e^x dx = x*e^x - e^x = e^x(x-1)")
                
        except Exception as e:
            print(f"\n{name}: Integration failed - {e}")

    # Definite integral example with area visualization
    def visualize_definite_integral():
        # Function: f(x) = x^2
        f_expr = x**2
        f_func = sp.lambdify(x, f_expr, 'numpy')
        
        # Compute definite integral from 0 to 2
        definite_result = sp.integrate(f_expr, (x, 0, 2))
        numerical_result, _ = integrate.quad(f_func, 0, 2)
        
        print(f"\nDefinite Integral Example:")
        print(f"∫₀² x² dx = {definite_result} = {float(definite_result):.4f}")
        print(f"Numerical verification: {numerical_result:.4f}")
        
        # Visualization
        x_vals = np.linspace(-0.5, 2.5, 1000)
        y_vals = f_func(x_vals)
        
        # Area under curve
        x_fill = np.linspace(0, 2, 100)
        y_fill = f_func(x_fill)
        
        plt.figure(figsize=(10, 6))
        plt.plot(x_vals, y_vals, 'b-', linewidth=2, label='f(x) = x²')
        plt.fill_between(x_fill, 0, y_fill, alpha=0.3, color='lightblue', 
                         label=f'Area = {float(definite_result):.3f}')
        plt.axhline(y=0, color='black', linewidth=0.5)
        plt.axvline(x=0, color='black', linewidth=0.5)
        plt.axvline(x=2, color='red', linestyle='--', alpha=0.5)
        
        plt.xlabel('x')
        plt.ylabel('f(x)')
        plt.title('Definite Integral: Area Under f(x) = x² from 0 to 2')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(-0.5, 2.5)
        plt.ylim(-0.5, 4.5)
        plt.show()

    visualize_definite_integral()

    # Fundamental Theorem of Calculus demonstration
    print("\nFundamental Theorem of Calculus:")
    print("If F(x) = ∫ₐˣ f(t) dt, then F'(x) = f(x)")

    # Example: F(x) = ∫₀ˣ t² dt = x³/3
    F_expr = x**3/3
    F_derivative = sp.diff(F_expr, x)
    print(f"F(x) = x³/3")
    print(f"F'(x) = {F_derivative} = x²")
    print("This confirms that the derivative of the antiderivative gives us back the original function!")
                            

</div>

<div class="code-block r-code">

R Implementation:

    library(ggplot2)
    library(pracma)  # For numerical integration

    # Basic integration examples (analytical approach)
    cat("Integration Examples in R:\n")
    cat("==========================\n\n")

    # Define functions and their antiderivatives
    examples <- list(
      "Power Rule" = list(
        func = function(x) x^3 + 2*x^2 - 5*x + 1,
        antiderivative = function(x) x^4/4 + 2*x^3/3 - 5*x^2/2 + x,
        description = "∫(x³ + 2x² - 5x + 1)dx = x⁴/4 + 2x³/3 - 5x²/2 + x + C"
      ),
      "Exponential" = list(
        func = function(x) exp(x),
        antiderivative = function(x) exp(x),
        description = "∫eˣ dx = eˣ + C"
      ),
      "Trigonometric" = list(
        func = function(x) sin(x),
        antiderivative = function(x) -cos(x),
        description = "∫sin(x) dx = -cos(x) + C"
      )
    )

    for (name in names(examples)) {
      cat(paste(name, ":\n"))
      cat(paste(examples[[name]]$description, "\n\n"))
    }

    # Definite integral example with visualization
    f <- function(x) x^2
    a <- 0
    b <- 2

    # Analytical result: ∫₀² x² dx = [x³/3]₀² = 8/3 - 0 = 8/3
    analytical_result <- (b^3/3) - (a^3/3)

    # Numerical integration
    numerical_result <- integrate(f, a, b)$value

    cat("Definite Integral Example:\n")
    cat("Function: f(x) = x²\n")
    cat("Limits: from 0 to 2\n")
    cat(sprintf("Analytical result: %.6f\n", analytical_result))
    cat(sprintf("Numerical result: %.6f\n", numerical_result))
    cat(sprintf("Difference: %.2e\n\n", abs(analytical_result - numerical_result)))

    # Visualization of definite integral
    x_vals <- seq(-0.5, 2.5, length.out = 1000)
    y_vals <- f(x_vals)

    # Area under curve
    x_fill <- seq(a, b, length.out = 100)
    y_fill <- f(x_fill)

    df_curve <- data.frame(x = x_vals, y = y_vals)
    df_area <- data.frame(x = x_fill, y = y_fill)

    p <- ggplot() +
      geom_line(data = df_curve, aes(x = x, y = y), color = "blue", size = 1.2) +
      geom_ribbon(data = df_area, aes(x = x, ymin = 0, ymax = y), 
                  fill = "lightblue", alpha = 0.5) +
      geom_vline(xintercept = c(a, b), color = "red", linetype = "dashed", alpha = 0.7) +
      geom_hline(yintercept = 0, color = "black", size = 0.3) +
      annotate("text", x = 1, y = 2, 
               label = paste("Area =", round(analytical_result, 3)), 
               size = 5, color = "darkblue") +
      labs(title = "Definite Integral: Area Under f(x) = x² from 0 to 2",
           x = "x", y = "f(x)") +
      xlim(-0.5, 2.5) +
      ylim(-0.5, 4.5) +
      theme_minimal() +
      theme(plot.title = element_text(hjust = 0.5, size = 14))

    print(p)

    # Numerical integration methods comparison
    cat("Numerical Integration Methods:\n")
    cat("==============================\n")

    # Trapezoidal rule
    trap_result <- trapz(x_fill, y_fill)
    cat(sprintf("Trapezoidal rule: %.6f\n", trap_result))

    # Simpson's rule (if available)
    if (length(x_fill) %% 2 == 1) {  # Simpson's rule needs odd number of points
      simpson_result <- simps(x_fill, y_fill)
      cat(sprintf("Simpson's rule: %.6f\n", simpson_result))
    }

    # Monte Carlo integration example
    monte_carlo_integration <- function(func, a, b, n = 10000) {
      x_random <- runif(n, a, b)
      y_values <- func(x_random)
      area_estimate <- (b - a) * mean(y_values)
      return(area_estimate)
    }

    mc_result <- monte_carlo_integration(f, a, b)
    cat(sprintf("Monte Carlo (n=10000): %.6f\n", mc_result))
                            

</div>

</div>

<div class="theorem-box">

### Partial Fractions

For rational functions $\frac{P(x)}{Q(x)}$ where degree of \$P \< \$ degree of $Q$:

Decompose $Q(x)$ into linear and quadratic factors, then write:

$$\frac{P(x)}{Q(x)} = \frac{A_1}{x-r_1} + \frac{A_2}{x-r_2} + \cdots + \frac{Bx+C}{ax^2+bx+c} + \cdots$$

</div>

<div class="chart-container">

</div>

</div>

</div>

<div class="section mb-12">

<div class="bg-white rounded-lg shadow-md p-8">

## 3.4 Applications to Area and Rate of Change

### Area Between Curves

<div class="definition-box">

### Area Between Two Curves

The area between curves $y = f(x)$ and $y = g(x)$ from $x = a$ to $x = b$ is:

$$A = \int_a^b |f(x) - g(x)| \, dx$$

If $f(x) \geq g(x)$ on $[a,b]$, then $A = \int_a^b [f(x) - g(x)] \, dx$

</div>

### Rate of Change Applications

<div class="bg-gray-100 p-6 rounded-lg mb-6">

#### Common Rate of Change Problems:

- **Velocity and Acceleration:** If $s(t)$ is position, then $v(t) = s'(t)$ is velocity and $a(t) = v'(t) = s''(t)$ is acceleration
- **Population Growth:** $\frac{dP}{dt} = kP$ leads to exponential growth $P(t) = P_0 e^{kt}$
- **Temperature Problems:** Newton's Law of Cooling: $\frac{dT}{dt} = -k(T - T_{ambient})$
- **Economic Models:** Marginal cost, revenue, and profit functions

</div>

<div class="example-box">

### Complete Application Examples

<div class="code-block python-code">

Python - Real-World Applications:

    import numpy as np
    import matplotlib.pyplot as plt
    from scipy import integrate
    import sympy as sp

    # Example 1: Area Between Curves
    def area_between_curves_example():
        print("Example 1: Area Between Curves")
        print("=" * 40)
        
        # Functions: f(x) = x^2 and g(x) = 2x - x^2
        x = sp.Symbol('x')
        f = x**2
        g = 2*x - x**2
        
        # Find intersection points
        intersection_eq = sp.Eq(f, g)
        intersection_points = sp.solve(intersection_eq, x)
        print(f"Functions: f(x) = {f}, g(x) = {g}")
        print(f"Intersection points: {intersection_points}")
        
        # Determine which function is on top
        test_point = 0.5  # Test point between intersections
        f_test = float(f.subs(x, test_point))
        g_test = float(g.subs(x, test_point))
        
        if g_test > f_test:
            area_integrand = g - f
            print(f"g(x) > f(x) between intersections")
        else:
            area_integrand = f - g
            print(f"f(x) > g(x) between intersections")
        
        # Calculate area
        a, b = float(intersection_points[0]), float(intersection_points[1])
        area = sp.integrate(area_integrand, (x, a, b))
        print(f"Area = ∫_{a}^{b} [{area_integrand}] dx = {area}")
        
        # Visualization
        x_vals = np.linspace(-0.5, 2.5, 1000)
        f_vals = x_vals**2
        g_vals = 2*x_vals - x_vals**2
        
        # Find area between curves
        x_fill = np.linspace(a, b, 100)
        f_fill = x_fill**2
        g_fill = 2*x_fill - x_fill**2
        
        plt.figure(figsize=(10, 8))
        plt.plot(x_vals, f_vals, 'b-', linewidth=2, label='f(x) = x²')
        plt.plot(x_vals, g_vals, 'r-', linewidth=2, label='g(x) = 2x - x²')
        plt.fill_between(x_fill, f_fill, g_fill, alpha=0.3, color='green', 
                         label=f'Area = {float(area):.3f}')
        
        # Mark intersection points
        plt.plot(intersection_points, [float(f.subs(x, pt)) for pt in intersection_points], 
                 'ko', markersize=8, label='Intersection points')
        
        plt.xlabel('x')
        plt.ylabel('y')
        plt.title('Area Between Curves')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(-0.5, 2.5)
        plt.show()

    # Example 2: Motion Problems
    def motion_problems_example():
        print("\nExample 2: Motion Problems")
        print("=" * 40)
        
        # Position function: s(t) = t^3 - 6t^2 + 9t
        t = sp.Symbol('t')
        s = t**3 - 6*t**2 + 9*t
        v = sp.diff(s, t)  # Velocity
        a = sp.diff(v, t)  # Acceleration
        
        print(f"Position: s(t) = {s}")
        print(f"Velocity: v(t) = s'(t) = {v}")
        print(f"Acceleration: a(t) = v'(t) = {a}")
        
        # Find when velocity is zero (turning points)
        v_zero = sp.solve(v, t)
        print(f"Velocity = 0 when t = {v_zero}")
        
        # Find when acceleration is zero
        a_zero = sp.solve(a, t)
        print(f"Acceleration = 0 when t = {a_zero}")
        
        # Calculate total distance traveled (considering direction changes)
        t_vals = [0] + [float(t_val) for t_val in v_zero if t_val >= 0] + [5]
        t_vals.sort()
        
        total_distance = 0
        for i in range(len(t_vals)-1):
            t1, t2 = t_vals[i], t_vals[i+1]
            # Distance is integral of |v(t)|
            v_func = sp.lambdify(t, v, 'numpy')
            distance_segment, _ = integrate.quad(lambda x: abs(v_func(x)), t1, t2)
            total_distance += distance_segment
            print(f"Distance from t={t1} to t={t2}: {distance_segment:.3f}")
        
        print(f"Total distance traveled from t=0 to t=5: {total_distance:.3f}")
        
        # Visualization
        t_plot = np.linspace(0, 5, 1000)
        s_vals = t_plot**3 - 6*t_plot**2 + 9*t_plot
        v_vals = 3*t_plot**2 - 12*t_plot + 9
        a_vals = 6*t_plot - 12
        
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12))
        
        # Position
        ax1.plot(t_plot, s_vals, 'b-', linewidth=2, label='Position s(t)')
        ax1.set_ylabel('Position')
        ax1.set_title('Motion Analysis')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Velocity
        ax2.plot(t_plot, v_vals, 'r-', linewidth=2, label='Velocity v(t)')
        ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax2.plot([float(pt) for pt in v_zero], [0]*len(v_zero), 'ro', 
                 markersize=8, label='v = 0 (turning points)')
        ax2.set_ylabel('Velocity')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Acceleration
        ax3.plot(t_plot, a_vals, 'g-', linewidth=2, label='Acceleration a(t)')
        ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax3.plot([float(pt) for pt in a_zero], [0]*len(a_zero), 'go', 
                 markersize=8, label='a = 0 (inflection)')
        ax3.set_xlabel('Time t')
        ax3.set_ylabel('Acceleration')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        plt.tight_layout()
        plt.show()

    # Example 3: Exponential Growth/Decay
    def exponential_growth_example():
        print("\nExample 3: Exponential Growth/Decay")
        print("=" * 40)
        
        # Population growth: dP/dt = kP, P(0) = P0
        # Solution: P(t) = P0 * e^(kt)
        
        # Parameters
        P0 = 1000  # Initial population
        k = 0.05   # Growth rate (5% per time unit)
        
        print(f"Differential equation: dP/dt = kP")
        print(f"Initial condition: P(0) = {P0}")
        print(f"Growth rate: k = {k}")
        print(f"Solution: P(t) = {P0} * e^({k}t)")
        
        # Calculate population at different times
        times = [0, 5, 10, 15, 20]
        populations = [P0 * np.exp(k * t) for t in times]
        
        print("\nPopulation at different times:")
        for t, p in zip(times, populations):
            print(f"t = {t}: P = {p:.0f}")
        
        # Doubling time
        doubling_time = np.log(2) / k
        print(f"\nDoubling time: {doubling_time:.2f} time units")
        
        # Visualization
        t_plot = np.linspace(0, 25, 1000)
        P_plot = P0 * np.exp(k * t_plot)
        
        plt.figure(figsize=(10, 6))
        plt.plot(t_plot, P_plot, 'b-', linewidth=2, label=f'P(t) = {P0}e^{{{k}t}}')
        plt.plot(times, populations, 'ro', markersize=8, label='Sample points')
        plt.axhline(y=2*P0, color='red', linestyle='--', alpha=0.7, label='Double initial')
        plt.axvline(x=doubling_time, color='red', linestyle='--', alpha=0.7)
        
        plt.xlabel('Time t')
        plt.ylabel('Population P(t)')
        plt.title('Exponential Population Growth')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.show()
        
        # Half-life example (decay)
        print(f"\nFor decay (k < 0), half-life = ln(2)/|k| = {np.log(2)/abs(-k):.2f}")

    # Run all examples
    area_between_curves_example()
    motion_problems_example()
    exponential_growth_example()
                            

</div>

<div class="code-block r-code">

R Applications:

    library(ggplot2)
    library(dplyr)
    library(gridExtra)

    # Example 1: Area Between Curves in R
    area_between_curves_r <- function() {
      cat("Example 1: Area Between Curves in R\n")
      cat("===================================\n")
      
      # Functions: f(x) = x^2 and g(x) = 2x - x^2
      f <- function(x) x^2
      g <- function(x) 2*x - x^2
      
      # Find intersection points by solving f(x) = g(x)
      # x^2 = 2x - x^2  =>  2x^2 - 2x = 0  =>  2x(x-1) = 0
      intersections <- c(0, 1)
      cat("Intersection points:", intersections, "\n")
      
      # Calculate area between curves
      integrand <- function(x) abs(g(x) - f(x))
      area <- integrate(integrand, intersections[1], intersections[2])$value
      cat("Area between curves:", area, "\n")
      
      # Visualization
      x_vals <- seq(-0.5, 2.5, length.out = 1000)
      df <- data.frame(
        x = x_vals,
        f_x = f(x_vals),
        g_x = g(x_vals)
      )
      
      # Area data
      x_fill <- seq(intersections[1], intersections[2], length.out = 100)
      df_fill <- data.frame(
        x = x_fill,
        f_x = f(x_fill),
        g_x = g(x_fill)
      )
      
      p <- ggplot(df, aes(x = x)) +
        geom_line(aes(y = f_x), color = "blue", size = 1.2) +
        geom_line(aes(y = g_x), color = "red", size = 1.2) +
        geom_ribbon(data = df_fill, aes(ymin = f_x, ymax = g_x), 
                    fill = "green", alpha = 0.3) +
        geom_point(data = data.frame(x = intersections, y = f(intersections)), 
                   size = 4, color = "black") +
        labs(title = "Area Between Curves f(x) = x² and g(x) = 2x - x²",
             x = "x", y = "y") +
        xlim(-0.5, 2.5) +
        theme_minimal()
      
      print(p)
      return(area)
    }

    # Example 2: Motion Problems in R
    motion_analysis_r <- function() {
      cat("\nExample 2: Motion Analysis in R\n")
      cat("================================\n")
      
      # Position function: s(t) = t^3 - 6t^2 + 9t
      s <- function(t) t^3 - 6*t^2 + 9*t
      v <- function(t) 3*t^2 - 12*t + 9  # velocity (derivative of position)
      a <- function(t) 6*t - 12           # acceleration (derivative of velocity)
      
      cat("Position: s(t) = t³ - 6t² + 9t\n")
      cat("Velocity: v(t) = 3t² - 12t + 9\n")
      cat("Acceleration: a(t) = 6t - 12\n")
      
      # Find when velocity is zero (solve 3t^2 - 12t + 9 = 0)
      # Using quadratic formula: t = (12 ± √(144-108))/6 = (12 ± 6)/6
      v_zero_times <- c(1, 3)
      cat("Velocity = 0 at t =", v_zero_times, "\n")
      
      # Find when acceleration is zero (6t - 12 = 0 => t = 2)
      a_zero_time <- 2
      cat("Acceleration = 0 at t =", a_zero_time, "\n")
      
      # Create visualization data
      t_vals <- seq(0, 5, length.out = 1000)
      
      motion_data <- data.frame(
        t = rep(t_vals, 3),
        value = c(s(t_vals), v(t_vals), a(t_vals)),
        type = rep(c("Position", "Velocity", "Acceleration"), each = length(t_vals))
      )
      
      # Create plots
      p <- ggplot(motion_data, aes(x = t, y = value)) +
        geom_line(size = 1.2, color = "blue") +
        geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
        facet_wrap(~type, scales = "free_y", ncol = 1) +
        labs(title = "Motion Analysis: Position, Velocity, and Acceleration",
             x = "Time t", y = "Value") +
        theme_minimal()
      
      print(p)
      
      # Calculate total distance
      segments <- c(0, v_zero_times[v_zero_times >= 0 & v_zero_times <= 5], 5)
      segments <- sort(unique(segments))
      
      total_distance <- 0
      for (i in 1:(length(segments)-1)) {
        t1 <- segments[i]
        t2 <- segments[i+1]
        distance_segment <- integrate(function(t) abs(v(t)), t1, t2)$value
        total_distance <- total_distance + distance_segment
        cat(sprintf("Distance from t=%.1f to t=%.1f: %.3f\n", t1, t2, distance_segment))
      }
      
      cat(sprintf("Total distance traveled: %.3f\n", total_distance))
    }

    # Example 3: Exponential Growth Model
    exponential_growth_r <- function() {
      cat("\nExample 3: Exponential Growth in R\n")
      cat("==================================\n")
      
      # Parameters
      P0 <- 1000  # Initial population
      k <- 0.05   # Growth rate
      
      cat("Exponential growth model: dP/dt = kP\n")
      cat("Solution: P(t) = P₀ × e^(kt)\n")
      cat("P₀ =", P0, "\n")
      cat("k =", k, "\n")
      
      # Population function
      P <- function(t) P0 * exp(k * t)
      
      # Calculate doubling time
      doubling_time <- log(2) / k
      cat("Doubling time:", round(doubling_time, 2), "time units\n")
      
      # Sample calculations
      times <- c(0, 5, 10, 15, 20)
      populations <- P(times)
      
      cat("\nPopulation at different times:\n")
      for (i in 1:length(times)) {
        cat(sprintf("t = %d: P = %.0f\n", times[i], populations[i]))
      }
      
      # Visualization
      t_plot <- seq(0, 25, length.out = 1000)
      df_growth <- data.frame(
        t = t_plot,
        P = P(t_plot)
      )
      
      sample_points <- data.frame(
        t = times,
        P = populations
      )
      
      p <- ggplot(df_growth, aes(x = t, y = P)) +
        geom_line(color = "blue", size = 1.2) +
        geom_point(data = sample_points, color = "red", size = 3) +
        geom_hline(yintercept = 2*P0, color = "red", linetype = "dashed", alpha = 0.7) +
        geom_vline(xintercept = doubling_time, color = "red", linetype = "dashed", alpha = 0.7) +
        labs(title = paste("Exponential Growth: P(t) =", P0, "× e^(", k, "t)"),
             x = "Time t", y = "Population P(t)") +
        theme_minimal()
      
      print(p)
      
      return(list(doubling_time = doubling_time, final_population = P(20)))
    }

    # Run all examples
    area_result <- area_between_curves_r()
    motion_analysis_r()
    growth_results <- exponential_growth_r()

    cat("\n" + "="*50 + "\n")
    cat("Summary of Applications:\n")
    cat("- Area between curves:", round(area_result, 4), "\n")
    cat("- Doubling time:", round(growth_results$doubling_time, 2), "\n")
    cat("- Population after 20 units:", round(growth_results$final_population, 0), "\n")
                            

</div>

</div>

<div class="warning-box">

### Connection to Differential Equations

The calculus concepts covered in this chapter are fundamental to understanding differential equations:

- **Derivatives** appear in the definition of differential equations
- **Integration** is used to solve separable differential equations
- **Rate of change problems** naturally lead to differential equation models
- **Exponential functions** are solutions to linear first-order ODEs
- **Area calculations** help in understanding solution curves and phase portraits

</div>

</div>

</div>

<div class="section mb-12">

<div class="bg-white rounded-lg shadow-md p-8">

## Practice Exercises

<div class="grid md:grid-cols-2 gap-6">

<div class="bg-blue-50 p-6 rounded-lg">

### Limits and Continuity

1.  Evaluate: $\lim_{x \to 0} \frac{\sin(3x)}{x}$
2.  Find: $\lim_{x \to \infty} \frac{2x^2 + 3x - 1}{x^2 - 5}$
3.  Determine where $f(x) = \frac{x^2-1}{x-1}$ is discontinuous
4.  Use L'Hôpital's rule: $\lim_{x \to 0} \frac{e^x - 1 - x}{x^2}$

</div>

<div class="bg-red-50 p-6 rounded-lg">

### Differentiation

1.  Find $\frac{d}{dx}[x^3 \sin(2x)]$
2.  Differentiate: $y = \frac{\ln(x)}{x^2 + 1}$
3.  Use chain rule: $\frac{d}{dx}[\cos(x^3 + 2x)]$
4.  Find critical points of $f(x) = x^3 - 3x^2 + 2$

</div>

<div class="bg-green-50 p-6 rounded-lg">

### Integration

1.  Evaluate: $\int (3x^2 - 2x + 5) \, dx$
2.  Use substitution: $\int x \sqrt{x^2 + 1} \, dx$
3.  Integration by parts: $\int x \ln(x) \, dx$
4.  Definite integral: $\int_0^{\pi/2} \sin(x) \cos(x) \, dx$

</div>

<div class="bg-purple-50 p-6 rounded-lg">

### Applications

1.  Find area between $y = x^2$ and $y = 4-x^2$
2.  A particle's position is $s(t) = t^3 - 6t^2 + 9t$. Find when velocity is maximum.
3.  If $\frac{dP}{dt} = 0.03P$ and $P(0) = 500$, find $P(10)$
4.  Optimize: Find dimensions of rectangle with perimeter 20 and maximum area

</div>

</div>

<div class="mt-8 p-6 bg-gray-100 rounded-lg">

### Programming Exercises

Implement the following using both Python and R:

1.  Create a function to numerically approximate derivatives using the difference quotient
2.  Implement the trapezoidal rule for numerical integration
3.  Visualize the relationship between a function and its derivative
4.  Model and plot exponential growth with different growth rates
5.  Find and visualize the area between two intersecting curves

</div>

</div>

</div>

<div class="section mb-12">

<div class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg shadow-lg p-8">

## Chapter Summary

<div class="grid md:grid-cols-2 gap-6">

<div>

### Key Concepts Mastered:

- Limits and continuity analysis
- Differentiation rules and techniques
- Integration methods and applications
- Rate of change modeling
- Area calculations and geometric interpretations

</div>

<div>

### Programming Skills:

- Symbolic computation with SymPy and R
- Numerical methods implementation
- Mathematical visualization techniques
- Real-world problem modeling

</div>

</div>

<div class="mt-8 p-6 bg-white bg-opacity-10 rounded-lg">

### Preparation for Differential Equations

This chapter has equipped you with the essential calculus tools needed for differential equations:

<div class="grid md:grid-cols-3 gap-4 text-sm">

<div class="bg-white bg-opacity-10 p-3 rounded">

**Derivatives**  
Will define differential equations and help us understand rates of change in dynamic systems.

</div>

<div class="bg-white bg-opacity-10 p-3 rounded">

**Integration**  
Essential for solving separable equations and finding antiderivatives in solution methods.

</div>

<div class="bg-white bg-opacity-10 p-3 rounded">

**Applications**  
Real-world modeling problems naturally lead to differential equation formulations.

</div>

</div>

</div>

<div class="text-center mt-6">

Next: Chapter 4 - Introduction to Mathematical Modeling

</div>

</div>

</div>

<div class="border-t border-gray-300 pt-8">

📘 ODE Tutorial Series - Chapter 3 Complete

<span class="mr-4">Foundation Level</span> <span class="mr-4">\~2-3 Hours Study Time</span> Essential Prerequisites

<div class="flex justify-center space-x-6 text-sm">

Python Examples R Implementation Interactive Visualizations Practical Applications

</div>

</div>

</div>
