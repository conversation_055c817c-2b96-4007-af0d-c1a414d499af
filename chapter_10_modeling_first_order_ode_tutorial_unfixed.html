<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 10: Modeling with First-Order ODEs - ODE Tutorial</title>
    
    <!-- External Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                packages: {'[+]': ['ams', 'newcommand', 'configmacros']}
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>

    <style>
        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #007acc;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        
        .python-code { border-left-color: #3776ab; }
        .r-code { border-left-color: #276dc3; }
        
        .definition-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .example-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .application-box {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .nav-section {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
            border-bottom: 2px solid #e5e7eb;
            padding: 1rem 0;
        }
        
        .chart-container {
            height: 400px;
            margin: 2rem 0;
        }
        
        .large-chart {
            height: 600px;
            margin: 2rem 0;
        }
        
         {
            .nav-section { position: static; }
            .chart-container, .large-chart { 
                height: 400px; 
                page-break-inside: avoid; 
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-8">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-2">
                <i class="fas fa-flask mr-3"></i>Chapter 10: Modeling with First-Order ODEs
            </h1>
            <p class="text-xl opacity-90">Part 2: First-Order Differential Equations - Comprehensive Applications</p>
            <div class="mt-4 text-sm opacity-75">
                <span class="mr-4"><i class="fas fa-book mr-1"></i>Advanced Tutorial</span>
                <span class="mr-4"><i class="fas fa-code mr-1"></i>Python & R</span>
                <span><i class="fas fa-chart-line mr-1"></i>Real-World Applications</span>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-section">
        <div class="container mx-auto px-4">
            <nav class="flex flex-wrap gap-4 text-sm">
                <a href="#intro" class="text-blue-600 hover:text-blue-800 font-medium">Introduction</a>
                <a href="#methodology" class="text-blue-600 hover:text-blue-800 font-medium">Modeling Methodology</a>
                <a href="#mixing" class="text-blue-600 hover:text-blue-800 font-medium">Mixing Problems</a>
                <a href="#population" class="text-blue-600 hover:text-blue-800 font-medium">Population Dynamics</a>
                <a href="#economics" class="text-blue-600 hover:text-blue-800 font-medium">Economic Models</a>
                <a href="#physics" class="text-blue-600 hover:text-blue-800 font-medium">Physics Applications</a>
                <a href="#engineering" class="text-blue-600 hover:text-blue-800 font-medium">Engineering Systems</a>
                <a href="#advanced" class="text-blue-600 hover:text-blue-800 font-medium">Advanced Analysis</a>
                <a href="#case-studies" class="text-blue-600 hover:text-blue-800 font-medium">Case Studies</a>
            </nav>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">

        <!-- Introduction -->
        <section id="intro" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-rocket text-blue-600 mr-3"></i>Introduction to ODE Modeling
            </h2>
            
            <div class="definition-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>Mathematical Modeling with ODEs</h3>
                <p class="mb-3">Mathematical modeling with first-order differential equations involves:</p>
                <ul class="list-disc list-inside space-y-2">
                    <li><strong>Problem Identification:</strong> Recognizing systems that change continuously over time</li>
                    <li><strong>Mathematical Translation:</strong> Converting real-world relationships into ODE form</li>
                    <li><strong>Solution Strategy:</strong> Choosing appropriate solution methods (separable, linear, exact)</li>
                    <li><strong>Parameter Estimation:</strong> Determining model parameters from data</li>
                    <li><strong>Validation:</strong> Testing model predictions against observations</li>
                    <li><strong>Interpretation:</strong> Understanding what solutions mean in context</li>
                </ul>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-8">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-tools text-green-600 mr-2"></i>Solution Methods Review
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-blue-50 rounded">
                            <strong>Separable:</strong> $\frac{dy}{dx} = g(x)h(y)$
                            <br><span class="text-sm text-gray-600">Growth, decay, cooling problems</span>
                        </div>
                        <div class="p-3 bg-green-50 rounded">
                            <strong>Linear:</strong> $\frac{dy}{dx} + P(x)y = Q(x)$
                            <br><span class="text-sm text-gray-600">Mixing, circuits, harvesting</span>
                        </div>
                        <div class="p-3 bg-purple-50 rounded">
                            <strong>Exact:</strong> $M dx + N dy = 0$
                            <br><span class="text-sm text-gray-600">Conservative systems, thermodynamics</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-globe text-blue-600 mr-2"></i>Application Domains
                    </h3>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <i class="fas fa-flask text-green-500 mr-2"></i>
                            <span>Chemistry & Biology</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                            <span>Economics & Finance</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-atom text-purple-500 mr-2"></i>
                            <span>Physics & Engineering</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-users text-orange-500 mr-2"></i>
                            <span>Population Dynamics</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-thermometer-half text-red-500 mr-2"></i>
                            <span>Environmental Science</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Modeling Methodology -->
        <section id="methodology" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-purple-600 mr-3"></i>Systematic Modeling Methodology
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-list-ol mr-2"></i>The Modeling Process</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">1. Problem Formulation</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Identify the system and variables</li>
                            <li>Determine what changes over time</li>
                            <li>Establish the independent variable (usually time)</li>
                            <li>Define the dependent variable(s)</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">2. Mathematical Translation</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Express rate of change: $\frac{dy}{dt}$</li>
                            <li>Identify relationships between variables</li>
                            <li>Apply physical laws or principles</li>
                            <li>Include initial/boundary conditions</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">3. Solution Strategy</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Classify the ODE type</li>
                            <li>Choose appropriate solution method</li>
                            <li>Solve analytically or numerically</li>
                            <li>Handle parameter estimation</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">4. Validation & Analysis</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Compare with experimental data</li>
                            <li>Perform sensitivity analysis</li>
                            <li>Check limiting behavior</li>
                            <li>Interpret results in context</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-decision text-blue-600 mr-2"></i>Equation Classification Decision Tree
                </h3>
                <div class="chart-container">
                    <canvas id="classificationChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Mixing Problems -->
        <section id="mixing" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-tint text-blue-600 mr-3"></i>Mixing Problems (Tank Problems)
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-flask mr-2"></i>General Mixing Problem Setup</h3>
                <p class="mb-4">A tank contains a solution with concentration that changes over time due to inflow and outflow.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Fundamental Principle:</h4>
                    <p class="text-center text-lg">
                        $$\frac{d(\text{Amount})}{dt} = \text{Rate In} - \text{Rate Out}$$
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-bold mb-2">Key Variables:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>$V(t)$ = Volume in tank at time $t$</li>
                            <li>$S(t)$ = Amount of substance at time $t$</li>
                            <li>$C(t) = S(t)/V(t)$ = Concentration</li>
                            <li>$r_{in}, r_{out}$ = Flow rates in/out</li>
                            <li>$C_{in}$ = Input concentration</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Standard Model:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Rate In = $r_{in} \cdot C_{in}$</li>
                            <li>Rate Out = $r_{out} \cdot C(t)$</li>
                            <li>ODE: $\frac{dS}{dt} = r_{in}C_{in} - r_{out}C(t)$</li>
                            <li>If $V$ constant: $\frac{dC}{dt} + \frac{r}{V}C = \frac{r_{in}C_{in}}{V}$</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 1: Salt Water Tank</h3>
                <p class="mb-3">A tank contains 1000 L of pure water. Salt water with concentration 0.5 kg/L flows in at 10 L/min. The well-mixed solution flows out at 10 L/min.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Solution Setup:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>$V = 1000$ L (constant volume)</li>
                        <li>$r_{in} = r_{out} = 10$ L/min</li>
                        <li>$C_{in} = 0.5$ kg/L</li>
                        <li>$C(0) = 0$ kg/L (initially pure water)</li>
                    </ul>
                    
                    <p class="mt-3"><strong>ODE:</strong> $\frac{dC}{dt} + \frac{10}{1000}C = \frac{10 \times 0.5}{1000}$</p>
                    <p class="mt-2">Simplifies to: $\frac{dC}{dt} + 0.01C = 0.005$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Analytical Solution:</h4>
                    <p>This is a first-order linear ODE. Using integrating factor $\mu = e^{0.01t}$:</p>
                    <p class="text-center mt-2">$$C(t) = 0.5(1 - e^{-0.01t})$$</p>
                    <p class="mt-2">Equilibrium concentration: $C_{\infty} = 0.5$ kg/L</p>
                    <p>Time to reach 95% of equilibrium: $t = -\frac{\ln(0.05)}{0.01} \approx 300$ minutes</p>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python Implementation
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

def mixing_tank(t, y, r_in, r_out, V, C_in):
    """
    Tank mixing model
    y[0] = C(t) = concentration
    """
    C = y[0]
    dCdt = (r_in * C_in - r_out * C) / V
    return [dCdt]

# Parameters
V = 1000  # Tank volume (L)
r_in = r_out = 10  # Flow rates (L/min)
C_in = 0.5  # Input concentration (kg/L)
C0 = 0  # Initial concentration

# Time span
t_span = (0, 500)
t_eval = np.linspace(0, 500, 1000)

# Solve ODE
sol = solve_ivp(mixing_tank, t_span, [C0], t_eval=t_eval, 
                args=(r_in, r_out, V, C_in))

# Analytical solution for comparison
t_analytical = t_eval
C_analytical = C_in * (1 - np.exp(-r_out * t_analytical / V))

# Plot results
plt.figure(figsize=(10, 6))
plt.plot(sol.t, sol.y[0], 'b-', linewidth=2, 
         label='Numerical Solution')
plt.plot(t_analytical, C_analytical, 'r--', linewidth=2, 
         label='Analytical Solution')
plt.axhline(y=C_in, color='g', linestyle=':', alpha=0.7, 
            label='Equilibrium')
plt.xlabel('Time (min)')
plt.ylabel('Concentration (kg/L)')
plt.title('Salt Water Tank Mixing Problem')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Parameter estimation from data
def fit_mixing_model(t_data, C_data, V, r_flow):
    """Estimate C_in from experimental data"""
    from scipy.optimize import curve_fit
    
    def model(t, C_in):
        return C_in * (1 - np.exp(-r_flow * t / V))
    
    popt, pcov = curve_fit(model, t_data, C_data)
    return popt[0], np.sqrt(pcov[0,0])

# Example with noisy data
t_data = np.array([0, 50, 100, 150, 200, 300, 400])
C_true = 0.5 * (1 - np.exp(-0.01 * t_data))
C_data = C_true + np.random.normal(0, 0.01, len(t_data))

C_in_est, C_in_err = fit_mixing_model(t_data, C_data, V, r_out)
print(f"Estimated C_in: {C_in_est:.3f} ± {C_in_err:.3f} kg/L")

# Sensitivity analysis
def sensitivity_analysis():
    V_values = np.linspace(800, 1200, 50)
    C_final = []
    
    for V_test in V_values:
        t_final = 300
        C_eq = C_in * (1 - np.exp(-r_out * t_final / V_test))
        C_final.append(C_eq)
    
    plt.figure(figsize=(8, 5))
    plt.plot(V_values, C_final, 'b-', linewidth=2)
    plt.xlabel('Tank Volume (L)')
    plt.ylabel('Concentration at t=300 min (kg/L)')
    plt.title('Sensitivity to Tank Volume')
    plt.grid(True, alpha=0.3)
    plt.show()

sensitivity_analysis()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R Implementation
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)

# Define mixing tank model
mixing_tank <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    C <- state[1]
    dCdt <- (r_in * C_in - r_out * C) / V
    return(list(dCdt))
  })
}

# Parameters
parms <- list(
  V = 1000,      # Tank volume (L)
  r_in = 10,     # Inflow rate (L/min)
  r_out = 10,    # Outflow rate (L/min)
  C_in = 0.5     # Input concentration (kg/L)
)

# Initial conditions and time
initial_state <- c(C = 0)  # Initial concentration
times <- seq(0, 500, by = 1)

# Solve ODE
solution <- ode(y = initial_state, 
                times = times, 
                func = mixing_tank, 
                parms = parms)

# Convert to data frame
sol_df <- as.data.frame(solution)

# Analytical solution
sol_df$C_analytical <- with(parms, 
  C_in * (1 - exp(-r_out * sol_df$time / V)))

# Create visualization
p1 <- ggplot(sol_df, aes(x = time)) +
  geom_line(aes(y = C, color = "Numerical"), size = 1.2) +
  geom_line(aes(y = C_analytical, color = "Analytical"), 
            linetype = "dashed", size = 1.2) +
  geom_hline(yintercept = parms$C_in, 
             color = "green", linetype = "dotted", alpha = 0.7) +
  labs(title = "Salt Water Tank Mixing Problem",
       x = "Time (min)",
       y = "Concentration (kg/L)",
       color = "Solution") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p1)

# Parameter estimation function
estimate_parameters <- function(t_data, C_data, V, r_flow) {
  # Define model function
  model_func <- function(t, C_in) {
    C_in * (1 - exp(-r_flow * t / V))
  }
  
  # Fit model
  fit <- nls(C_data ~ model_func(t_data, C_in),
             start = list(C_in = 0.4))
  
  return(summary(fit))
}

# Generate synthetic data with noise
set.seed(123)
t_data <- c(0, 50, 100, 150, 200, 300, 400)
C_true <- with(parms, C_in * (1 - exp(-r_out * t_data / V)))
C_data <- C_true + rnorm(length(t_data), 0, 0.01)

# Estimate parameters
fit_result <- estimate_parameters(t_data, C_data, 
                                  parms$V, parms$r_out)
print(fit_result)

# Sensitivity analysis
sensitivity_analysis <- function() {
  V_range <- seq(800, 1200, length.out = 50)
  t_final <- 300
  
  C_final <- sapply(V_range, function(V_test) {
    with(parms, C_in * (1 - exp(-r_out * t_final / V_test)))
  })
  
  sens_df <- data.frame(V = V_range, C_final = C_final)
  
  p2 <- ggplot(sens_df, aes(x = V, y = C_final)) +
    geom_line(color = "blue", size = 1.2) +
    labs(title = "Sensitivity to Tank Volume",
         x = "Tank Volume (L)",
         y = "Concentration at t=300 min (kg/L)") +
    theme_minimal()
  
  return(p2)
}

sens_plot <- sensitivity_analysis()
print(sens_plot)

# Multi-tank system
multi_tank_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    C1 <- state[1]  # Tank 1 concentration
    C2 <- state[2]  # Tank 2 concentration
    
    # Tank 1: Input from external source
    dC1dt <- (r_in * C_in - r12 * C1) / V1
    
    # Tank 2: Input from Tank 1, output to environment
    dC2dt <- (r12 * C1 - r_out * C2) / V2
    
    return(list(c(dC1dt, dC2dt)))
  })
}

# Multi-tank parameters
multi_parms <- list(
  V1 = 1000, V2 = 800,
  r_in = 10, r12 = 10, r_out = 10,
  C_in = 0.5
)

multi_initial <- c(C1 = 0, C2 = 0)
multi_solution <- ode(y = multi_initial,
                      times = times,
                      func = multi_tank_model,
                      parms = multi_parms)

multi_df <- as.data.frame(multi_solution)

# Plot multi-tank system
p3 <- ggplot(multi_df, aes(x = time)) +
  geom_line(aes(y = C1, color = "Tank 1"), size = 1.2) +
  geom_line(aes(y = C2, color = "Tank 2"), size = 1.2) +
  labs(title = "Two-Tank Mixing System",
       x = "Time (min)",
       y = "Concentration (kg/L)",
       color = "Tank") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p3)</code></pre>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-green-600 mr-2"></i>Mixing Problem Visualization
                </h3>
                <div class="large-chart">
                    <canvas id="mixingChart"></canvas>
                </div>
            </div>

            <div class="bg-gray-100 p-6 rounded-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-list-alt text-purple-600 mr-2"></i>Mixing Problem Variations
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">Variable Volume Cases:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li><strong>Different flow rates:</strong> $r_{in} \neq r_{out}$</li>
                            <li><strong>Volume change:</strong> $\frac{dV}{dt} = r_{in} - r_{out}$</li>
                            <li><strong>Coupled system:</strong> $\frac{d(VC)}{dt} = r_{in}C_{in} - r_{out}C$</li>
                            <li><strong>Evaporation effects:</strong> Additional volume loss</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Advanced Applications:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li><strong>Chemical reactors:</strong> With reaction terms</li>
                            <li><strong>Pollution control:</strong> Environmental cleanup</li>
                            <li><strong>Pharmaceutical mixing:</strong> Drug concentration</li>
                            <li><strong>Food processing:</strong> Ingredient mixing</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Population Dynamics -->
        <section id="population" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-users text-green-600 mr-3"></i>Population Dynamics
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-seedling mr-2"></i>Population Growth Models</h3>
                <p class="mb-4">Population dynamics models describe how populations change over time under various conditions.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Exponential Growth</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP$</p>
                        <p class="text-sm">Solution: $P(t) = P_0 e^{rt}$</p>
                        <p class="text-xs mt-1">Unlimited resources</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Logistic Growth</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP(1 - \frac{P}{K})$</p>
                        <p class="text-sm">S-shaped growth curve</p>
                        <p class="text-xs mt-1">Carrying capacity $K$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">With Harvesting</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP - H$</p>
                        <p class="text-sm">Constant harvest rate</p>
                        <p class="text-xs mt-1">Sustainability analysis</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 2: Logistic Population Model</h3>
                <p class="mb-3">A population grows according to the logistic model with intrinsic growth rate $r = 0.1$ per year and carrying capacity $K = 1000$ individuals.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Logistic Equation:</h4>
                    <p class="text-center">$$\frac{dP}{dt} = rP\left(1 - \frac{P}{K}\right) = 0.1P\left(1 - \frac{P}{1000}\right)$$</p>
                    
                    <p class="mt-3"><strong>This is separable:</strong></p>
                    <p class="text-center">$$\frac{dP}{P(1 - P/K)} = r \, dt$$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution by Partial Fractions:</h4>
                    <p>$$\frac{1}{P(1 - P/K)} = \frac{A}{P} + \frac{B}{1 - P/K}$$</p>
                    <p>After partial fractions and integration:</p>
                    <p class="text-center mt-2">$$P(t) = \frac{K}{1 + \left(\frac{K}{P_0} - 1\right)e^{-rt}}$$</p>
                    <p class="mt-2"><strong>Key Features:</strong></p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>Inflection point at $P = K/2$</li>
                        <li>Maximum growth rate at $P = K/2$: $\frac{dP}{dt}|_{max} = \frac{rK}{4}$</li>
                        <li>Approaches carrying capacity: $\lim_{t \to \infty} P(t) = K$</li>
                    </ul>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Population Dynamics in Python
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import fsolve

# Population growth models
def exponential_growth(t, y, r):
    """Exponential growth: dP/dt = rP"""
    return [r * y[0]]

def logistic_growth(t, y, r, K):
    """Logistic growth: dP/dt = rP(1 - P/K)"""
    P = y[0]
    return [r * P * (1 - P/K)]

def growth_with_harvesting(t, y, r, K, H):
    """Growth with harvesting: dP/dt = rP(1 - P/K) - H"""
    P = y[0]
    return [r * P * (1 - P/K) - H]

# Parameters
r = 0.1  # Growth rate (1/year)
K = 1000  # Carrying capacity
P0 = 50   # Initial population
t_span = (0, 50)
t_eval = np.linspace(0, 50, 500)

# Solve different models
sol_exp = solve_ivp(exponential_growth, t_span, [P0], 
                    t_eval=t_eval, args=(r,))
sol_log = solve_ivp(logistic_growth, t_span, [P0], 
                    t_eval=t_eval, args=(r, K))

# Analytical solutions
t = t_eval
P_exp_analytical = P0 * np.exp(r * t)
P_log_analytical = K / (1 + (K/P0 - 1) * np.exp(-r * t))

# Plot comparison
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Population curves
ax1.plot(t, P_exp_analytical, 'r-', linewidth=2, 
         label='Exponential')
ax1.plot(t, P_log_analytical, 'b-', linewidth=2, 
         label='Logistic')
ax1.axhline(y=K, color='g', linestyle='--', alpha=0.7, 
            label='Carrying Capacity')
ax1.set_xlabel('Time (years)')
ax1.set_ylabel('Population')
ax1.set_title('Population Growth Models')
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.set_ylim(0, 1200)

# Growth rates
dPdt_exp = r * P_exp_analytical
dPdt_log = r * P_log_analytical * (1 - P_log_analytical/K)

ax2.plot(P_exp_analytical, dPdt_exp, 'r-', linewidth=2, 
         label='Exponential')
ax2.plot(P_log_analytical, dPdt_log, 'b-', linewidth=2, 
         label='Logistic')
ax2.axvline(x=K/2, color='purple', linestyle=':', alpha=0.7, 
            label='Max Growth Rate')
ax2.set_xlabel('Population')
ax2.set_ylabel('Growth Rate (dP/dt)')
ax2.set_title('Phase Portrait')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Harvesting analysis
def analyze_harvesting():
    """Analyze sustainable harvesting"""
    # Critical harvesting rate (maximum sustainable)
    H_max = r * K / 4  # At P = K/2
    
    print(f"Maximum sustainable harvest rate: {H_max:.2f}")
    
    # Different harvesting scenarios
    H_values = [0, H_max/2, H_max, H_max*1.2]
    colors = ['blue', 'green', 'orange', 'red']
    labels = ['No harvest', 'Sustainable', 'Critical', 'Overharvest']
    
    plt.figure(figsize=(12, 8))
    
    for i, H in enumerate(H_values):
        if H < H_max * 1.1:  # Avoid extinction scenarios
            sol_h = solve_ivp(growth_with_harvesting, t_span, [P0], 
                             t_eval=t_eval, args=(r, K, H))
            plt.plot(sol_h.t, sol_h.y[0], color=colors[i], 
                    linewidth=2, label=f'{labels[i]} (H={H:.1f})')
    
    plt.axhline(y=K, color='gray', linestyle='--', alpha=0.5, 
                label='Carrying Capacity')
    plt.xlabel('Time (years)')
    plt.ylabel('Population')
    plt.title('Population Dynamics with Harvesting')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    # Equilibrium analysis
    def equilibrium_points(H):
        """Find equilibrium points for harvesting model"""
        def f(P):
            return r * P * (1 - P/K) - H
        
        if H <= H_max:
            roots = fsolve(f, [K/4, 3*K/4])
            return roots[roots > 0]  # Positive roots only
        else:
            return []
    
    H_range = np.linspace(0, H_max*1.5, 100)
    equilibria = []
    
    for H in H_range:
        eq_points = equilibrium_points(H)
        if len(eq_points) > 0:
            equilibria.extend([(H, P) for P in eq_points])
    
    if equilibria:
        H_eq, P_eq = zip(*equilibria)
        plt.figure(figsize=(10, 6))
        plt.plot(H_eq, P_eq, 'b.', markersize=3)
        plt.axvline(x=H_max, color='red', linestyle='--', 
                   label=f'Critical H = {H_max:.2f}')
        plt.xlabel('Harvest Rate')
        plt.ylabel('Equilibrium Population')
        plt.title('Bifurcation Diagram: Harvest Rate vs Population')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

analyze_harvesting()

# Parameter estimation from data
def fit_logistic_model(t_data, P_data):
    """Fit logistic model to population data"""
    from scipy.optimize import curve_fit
    
    def logistic_func(t, r, K, P0):
        return K / (1 + (K/P0 - 1) * np.exp(-r * t))
    
    # Initial guess
    K_guess = max(P_data) * 1.2
    r_guess = 0.1
    P0_guess = P_data[0]
    
    popt, pcov = curve_fit(logistic_func, t_data, P_data, 
                          p0=[r_guess, K_guess, P0_guess],
                          bounds=([0, max(P_data), 0], 
                                 [1, max(P_data)*3, max(P_data)]))
    
    r_fit, K_fit, P0_fit = popt
    r_err, K_err, P0_err = np.sqrt(np.diag(pcov))
    
    return (r_fit, K_fit, P0_fit), (r_err, K_err, P0_err)

# Generate synthetic data
t_data = np.array([0, 2, 5, 10, 15, 20, 25, 30])
P_true = K / (1 + (K/P0 - 1) * np.exp(-r * t_data))
P_data = P_true + np.random.normal(0, P_true * 0.05)  # 5% noise

# Fit model
params, errors = fit_logistic_model(t_data, P_data)
r_fit, K_fit, P0_fit = params
print(f"Fitted parameters:")
print(f"r = {r_fit:.4f} ± {errors[0]:.4f}")
print(f"K = {K_fit:.1f} ± {errors[1]:.1f}")
print(f"P0 = {P0_fit:.1f} ± {errors[2]:.1f}")

# Compare with true values
P_fitted = K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_eval))

plt.figure(figsize=(10, 6))
plt.plot(t_eval, P_log_analytical, 'b-', linewidth=2, label='True Model')
plt.plot(t_eval, P_fitted, 'r--', linewidth=2, label='Fitted Model')
plt.scatter(t_data, P_data, color='black', s=50, 
           label='Data Points', zorder=5)
plt.xlabel('Time (years)')
plt.ylabel('Population')
plt.title('Logistic Model Fitting')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>Population Dynamics in R
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(gridExtra)

# Define population models
exponential_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    P <- state[1]
    dPdt <- r * P
    return(list(dPdt))
  })
}

logistic_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    P <- state[1]
    dPdt <- r * P * (1 - P/K)
    return(list(dPdt))
  })
}

harvesting_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    P <- state[1]
    dPdt <- r * P * (1 - P/K) - H
    return(list(dPdt))
  })
}

# Parameters
parms_exp <- list(r = 0.1)
parms_log <- list(r = 0.1, K = 1000)
parms_harv <- list(r = 0.1, K = 1000, H = 10)

# Initial conditions and time
initial_state <- c(P = 50)
times <- seq(0, 50, by = 0.1)

# Solve models
sol_exp <- ode(y = initial_state, times = times, 
               func = exponential_model, parms = parms_exp)
sol_log <- ode(y = initial_state, times = times, 
               func = logistic_model, parms = parms_log)
sol_harv <- ode(y = initial_state, times = times, 
                func = harvesting_model, parms = parms_harv)

# Convert to data frames and combine
df_exp <- as.data.frame(sol_exp) %>% mutate(Model = "Exponential")
df_log <- as.data.frame(sol_log) %>% mutate(Model = "Logistic")
df_harv <- as.data.frame(sol_harv) %>% mutate(Model = "Harvesting")

combined_df <- rbind(df_exp, df_log, df_harv)

# Plot population dynamics
p1 <- ggplot(combined_df, aes(x = time, y = P, color = Model)) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = parms_log$K, linetype = "dashed", 
             color = "gray", alpha = 0.7) +
  labs(title = "Population Growth Models Comparison",
       x = "Time (years)",
       y = "Population",
       color = "Model") +
  theme_minimal() +
  theme(legend.position = "bottom") +
  ylim(0, 1200)

# Phase portrait
df_log$dPdt <- with(parms_log, r * df_log$P * (1 - df_log$P/K))

p2 <- ggplot(df_log, aes(x = P, y = dPdt)) +
  geom_line(color = "blue", size = 1.2) +
  geom_hline(yintercept = 0, linetype = "dashed", 
             color = "gray", alpha = 0.7) +
  geom_vline(xintercept = parms_log$K/2, linetype = "dotted", 
             color = "purple", alpha = 0.7) +
  labs(title = "Logistic Growth Phase Portrait",
       x = "Population (P)",
       y = "Growth Rate (dP/dt)") +
  theme_minimal()

# Combine plots
grid.arrange(p1, p2, ncol = 2)

# Harvesting analysis
analyze_harvesting <- function() {
  # Calculate maximum sustainable harvest
  H_max <- with(parms_log, r * K / 4)
  cat("Maximum sustainable harvest rate:", H_max, "\n")
  
  # Different harvesting scenarios
  H_values <- c(0, H_max/2, H_max, H_max * 0.9)
  results <- list()
  
  for(i in seq_along(H_values)) {
    H_current <- H_values[i]
    parms_current <- list(r = 0.1, K = 1000, H = H_current)
    
    sol <- ode(y = initial_state, times = times, 
               func = harvesting_model, parms = parms_current)
    
    df <- as.data.frame(sol) %>% 
      mutate(Scenario = paste("H =", round(H_current, 1)))
    
    results[[i]] <- df
  }
  
  harv_df <- do.call(rbind, results)
  
  p3 <- ggplot(harv_df, aes(x = time, y = P, color = Scenario)) +
    geom_line(size = 1.2) +
    geom_hline(yintercept = parms_log$K, linetype = "dashed", 
               color = "gray", alpha = 0.7) +
    labs(title = "Population Dynamics with Different Harvest Rates",
         x = "Time (years)",
         y = "Population",
         color = "Harvest Rate") +
    theme_minimal() +
    theme(legend.position = "bottom")
  
  return(p3)
}

harv_plot <- analyze_harvesting()
print(harv_plot)

# Parameter estimation
fit_logistic <- function(t_data, P_data) {
  # Define model function for nls
  logistic_func <- function(t, r, K, P0) {
    K / (1 + (K/P0 - 1) * exp(-r * t))
  }
  
  # Initial parameter estimates
  K_init <- max(P_data) * 1.2
  r_init <- 0.1
  P0_init <- P_data[1]
  
  # Fit nonlinear model
  fit <- nls(P_data ~ logistic_func(t_data, r, K, P0),
             start = list(r = r_init, K = K_init, P0 = P0_init),
             control = nls.control(maxiter = 100))
  
  return(fit)
}

# Generate synthetic data with noise
set.seed(123)
t_data <- c(0, 2, 5, 10, 15, 20, 25, 30)
P_true <- with(parms_log, K / (1 + (K/50 - 1) * exp(-r * t_data)))
P_data <- P_true + rnorm(length(t_data), 0, P_true * 0.05)

# Fit model
fit_result <- fit_logistic(t_data, P_data)
summary(fit_result)

# Extract fitted parameters
r_fitted <- coef(fit_result)['r']
K_fitted <- coef(fit_result)['K']
P0_fitted <- coef(fit_result)['P0']

# Generate fitted curve
t_fit <- seq(0, 35, length.out = 200)
P_fitted <- K_fitted / (1 + (K_fitted/P0_fitted - 1) * exp(-r_fitted * t_fit))

# Plot fitted model
fit_df <- data.frame(time = t_fit, P_fitted = P_fitted)
data_df <- data.frame(time = t_data, P_observed = P_data)

p4 <- ggplot() +
  geom_line(data = df_log[df_log$time <= 35, ], 
            aes(x = time, y = P), color = "blue", size = 1.2, 
            linetype = "solid") +
  geom_line(data = fit_df, aes(x = time, y = P_fitted), 
            color = "red", size = 1.2, linetype = "dashed") +
  geom_point(data = data_df, aes(x = time, y = P_observed), 
             color = "black", size = 3) +
  labs(title = "Logistic Model Fitting to Data",
       x = "Time (years)",
       y = "Population") +
  theme_minimal() +
  annotate("text", x = 25, y = 200, 
           label = paste("Fitted: r =", round(r_fitted, 3), 
                        ", K =", round(K_fitted, 0)), 
           color = "red") +
  annotate("text", x = 25, y = 150, 
           label = paste("True: r = 0.1, K = 1000"), 
           color = "blue")

print(p4)

# Confidence intervals and model diagnostics
conf_int <- confint(fit_result)
print("95% Confidence Intervals:")
print(conf_int)

# Residual analysis
residuals <- residuals(fit_result)
fitted_values <- fitted(fit_result)

residual_df <- data.frame(
  fitted = fitted_values,
  residuals = residuals,
  time = t_data
)

p5 <- ggplot(residual_df, aes(x = fitted, y = residuals)) +
  geom_point(size = 3) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "red") +
  labs(title = "Residual Analysis",
       x = "Fitted Values",
       y = "Residuals") +
  theme_minimal()

p6 <- ggplot(residual_df, aes(x = time, y = residuals)) +
  geom_point(size = 3) +
  geom_line(alpha = 0.5) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "red") +
  labs(title = "Residuals vs Time",
       x = "Time",
       y = "Residuals") +
  theme_minimal()

grid.arrange(p5, p6, ncol = 2)</code></pre>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-area text-green-600 mr-2"></i>Population Dynamics Comparison
                </h3>
                <div class="large-chart">
                    <canvas id="populationChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Economics Models -->
        <section id="economics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>Economic Models
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-coins mr-2"></i>Economic Dynamics with ODEs</h3>
                <p class="mb-4">Economic systems exhibit continuous change that can be modeled using differential equations.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Investment Growth Models:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li><strong>Simple Interest:</strong> $\frac{dA}{dt} = rP$ (linear)</li>
                            <li><strong>Compound Interest:</strong> $\frac{dA}{dt} = rA$ (exponential)</li>
                            <li><strong>With Deposits:</strong> $\frac{dA}{dt} = rA + D(t)$ (linear ODE)</li>
                            <li><strong>With Withdrawals:</strong> $\frac{dA}{dt} = rA - W(t)$</li>
                        </ul>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Market Dynamics:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li><strong>Price Adjustment:</strong> $\frac{dp}{dt} = k(D(p) - S(p))$</li>
                            <li><strong>Supply-Demand:</strong> Equilibrium seeking behavior</li>
                            <li><strong>Market Saturation:</strong> Logistic-type models</li>
                            <li><strong>Economic Cycles:</strong> Oscillatory behavior</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 3: Investment with Regular Deposits</h3>
                <p class="mb-3">An investment account pays 5% annual interest compounded continuously. Regular deposits of $1000/year are made.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Model Setup:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>$A(t)$ = Account balance at time $t$</li>
                        <li>$r = 0.05$ = Interest rate (5% annual)</li>
                        <li>$D = 1000$ = Annual deposit rate</li>
                        <li>$A(0) = 0$ = Initial balance</li>
                    </ul>
                    
                    <p class="mt-3"><strong>ODE:</strong> $\frac{dA}{dt} = rA + D = 0.05A + 1000$</p>
                    <p class="mt-2">This is a first-order linear ODE: $\frac{dA}{dt} - 0.05A = 1000$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution:</h4>
                    <p>Using integrating factor $\mu = e^{-0.05t}$:</p>
                    <p class="text-center mt-2">$$A(t) = \frac{1000}{0.05}(e^{0.05t} - 1) = 20000(e^{0.05t} - 1)$$</p>
                    <p class="mt-2"><strong>After 20 years:</strong> $A(20) = 20000(e^{1} - 1) \approx \$34,310$</p>
                    <p class="mt-1"><strong>Total deposits:</strong> $20 \times 1000 = \$20,000$</p>
                    <p class="mt-1"><strong>Interest earned:</strong> $\$34,310 - \$20,000 = \$14,310$</p>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-money-bill-wave text-green-600 mr-2"></i>Economic Models Visualization
                </h3>
                <div class="large-chart">
                    <canvas id="economicsChart"></canvas>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Economic Models in Python
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

# Investment models
def simple_interest(t, y, r, P):
    """Simple interest: dA/dt = rP"""
    return [r * P]

def compound_interest(t, y, r):
    """Compound interest: dA/dt = rA"""
    return [r * y[0]]

def investment_with_deposits(t, y, r, D):
    """Investment with deposits: dA/dt = rA + D"""
    return [r * y[0] + D]

def investment_with_withdrawals(t, y, r, W):
    """Investment with withdrawals: dA/dt = rA - W"""
    return [r * y[0] - W]

# Market dynamics
def price_adjustment(t, y, k, D_params, S_params):
    """Price adjustment: dp/dt = k(D(p) - S(p))"""
    p = y[0]
    
    # Linear demand: D(p) = a - bp
    a_d, b_d = D_params
    demand = a_d - b_d * p
    
    # Linear supply: S(p) = c + dp
    c_s, d_s = S_params
    supply = c_s + d_s * p
    
    return [k * (demand - supply)]

# Parameters
r = 0.05  # 5% annual interest rate
D = 1000  # Annual deposit ($1000/year)
W = 500   # Annual withdrawal ($500/year)
A0 = 1000 # Initial amount
t_span = (0, 30)
t_eval = np.linspace(0, 30, 300)

# Solve investment models
sol_simple = solve_ivp(simple_interest, t_span, [A0], 
                      t_eval=t_eval, args=(r, A0))
sol_compound = solve_ivp(compound_interest, t_span, [A0], 
                        t_eval=t_eval, args=(r,))
sol_deposits = solve_ivp(investment_with_deposits, t_span, [0], 
                        t_eval=t_eval, args=(r, D))
sol_withdrawals = solve_ivp(investment_with_withdrawals, t_span, [A0*5], 
                           t_eval=t_eval, args=(r, W))

# Analytical solutions
t = t_eval
A_simple = A0 + r * A0 * t
A_compound = A0 * np.exp(r * t)
A_deposits = (D/r) * (np.exp(r * t) - 1)
A_withdrawals = A0*5 * np.exp(r * t) - (W/r) * (np.exp(r * t) - 1)

# Plot investment models
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

# Simple vs Compound Interest
ax1.plot(t, A_simple, 'r-', linewidth=2, label='Simple Interest')
ax1.plot(t, A_compound, 'b-', linewidth=2, label='Compound Interest')
ax1.set_xlabel('Time (years)')
ax1.set_ylabel('Account Balance ($)')
ax1.set_title('Simple vs Compound Interest')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Investment with Deposits
ax2.plot(t, A_deposits, 'g-', linewidth=2, label='With Deposits')
ax2.plot(t, D * t, 'k--', linewidth=1, alpha=0.7, label='Total Deposits')
ax2.set_xlabel('Time (years)')
ax2.set_ylabel('Account Balance ($)')
ax2.set_title('Investment with Regular Deposits')
ax2.legend()
ax2.grid(True, alpha=0.3)

# Investment with Withdrawals
ax3.plot(t, A_withdrawals, 'purple', linewidth=2, label='With Withdrawals')
ax3.axhline(y=0, color='red', linestyle=':', alpha=0.7, label='Break-even')
ax3.set_xlabel('Time (years)')
ax3.set_ylabel('Account Balance ($)')
ax3.set_title('Investment with Regular Withdrawals')
ax3.legend()
ax3.grid(True, alpha=0.3)

# Comparison of all models
ax4.plot(t, A_compound, 'b-', linewidth=2, label='Compound Only')
ax4.plot(t, A_deposits, 'g-', linewidth=2, label='With Deposits')
ax4.plot(t[:len(A_withdrawals[A_withdrawals > 0])], 
         A_withdrawals[A_withdrawals > 0], 'purple', linewidth=2, 
         label='With Withdrawals')
ax4.set_xlabel('Time (years)')
ax4.set_ylabel('Account Balance ($)')
ax4.set_title('Investment Strategy Comparison')
ax4.legend()
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Market price dynamics
def analyze_market_dynamics():
    """Analyze price adjustment in a market"""
    
    # Market parameters
    k = 0.1  # Adjustment speed
    D_params = (1000, 2)  # Demand: D = 1000 - 2p
    S_params = (200, 1)   # Supply: S = 200 + p
    
    # Equilibrium price: D = S => 1000 - 2p = 200 + p => p_eq = 800/3
    p_equilibrium = (D_params[0] - S_params[0]) / (D_params[1] + S_params[1])
    print(f"Equilibrium price: ${p_equilibrium:.2f}")
    
    # Different initial prices
    initial_prices = [100, 200, 300, 400]
    colors = ['red', 'blue', 'green', 'orange']
    
    plt.figure(figsize=(12, 8))
    
    for i, p0 in enumerate(initial_prices):
        sol_price = solve_ivp(price_adjustment, (0, 20), [p0], 
                             t_eval=np.linspace(0, 20, 200),
                             args=(k, D_params, S_params))
        
        plt.plot(sol_price.t, sol_price.y[0], color=colors[i], 
                linewidth=2, label=f'Initial price: ${p0}')
    
    plt.axhline(y=p_equilibrium, color='black', linestyle='--', 
                alpha=0.7, label=f'Equilibrium: ${p_equilibrium:.2f}')
    plt.xlabel('Time')
    plt.ylabel('Price ($)')
    plt.title('Market Price Adjustment Dynamics')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    # Phase portrait
    p_range = np.linspace(50, 400, 100)
    dpdt = []
    
    for p in p_range:
        demand = D_params[0] - D_params[1] * p
        supply = S_params[0] + S_params[1] * p
        dpdt.append(k * (demand - supply))
    
    plt.figure(figsize=(10, 6))
    plt.plot(p_range, dpdt, 'b-', linewidth=2)
    plt.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    plt.axvline(x=p_equilibrium, color='green', linestyle='--', alpha=0.7)
    plt.xlabel('Price ($)')
    plt.ylabel('Price Change Rate (dp/dt)')
    plt.title('Price Adjustment Phase Portrait')
    plt.grid(True, alpha=0.3)
    plt.show()

analyze_market_dynamics()

# Loan repayment model
def loan_repayment_analysis():
    """Analyze loan repayment dynamics"""
    
    def loan_balance(t, y, r, payment):
        """Loan balance: dL/dt = rL - payment"""
        return [r * y[0] - payment]
    
    # Loan parameters
    L0 = 100000  # Initial loan amount ($100,000)
    r_annual = 0.06  # 6% annual interest rate
    payment_annual = 12000  # $12,000 annual payment
    
    # Check if payment covers interest
    annual_interest = r_annual * L0
    print(f"Annual interest: ${annual_interest}")
    print(f"Annual payment: ${payment_annual}")
    
    if payment_annual <= annual_interest:
        print("Warning: Payment doesn't cover interest!")
        return
    
    # Time to pay off loan analytically
    # L(t) = (L0 - payment/r) * exp(rt) + payment/r
    # L(t) = 0 when t = ln(payment/(payment - rL0)) / r
    
    if payment_annual > r_annual * L0:
        payoff_time = np.log(payment_annual / (payment_annual - r_annual * L0)) / r_annual
        print(f"Loan payoff time: {payoff_time:.2f} years")
    
    # Solve numerically
    t_max = min(20, payoff_time * 1.2) if 'payoff_time' in locals() else 20
    t_span = (0, t_max)
    t_eval = np.linspace(0, t_max, 1000)
    
    sol_loan = solve_ivp(loan_balance, t_span, [L0], 
                        t_eval=t_eval, args=(r_annual, payment_annual))
    
    # Find actual payoff time (when balance reaches 0)
    balance = sol_loan.y[0]
    positive_balance = balance > 0
    if np.any(positive_balance):
        actual_payoff_time = sol_loan.t[positive_balance][-1]
        print(f"Numerical payoff time: {actual_payoff_time:.2f} years")
    
    # Plot loan balance over time
    plt.figure(figsize=(12, 6))
    plt.plot(sol_loan.t, sol_loan.y[0], 'b-', linewidth=2, label='Loan Balance')
    plt.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='Paid Off')
    plt.xlabel('Time (years)')
    plt.ylabel('Loan Balance ($)')
    plt.title('Loan Repayment Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    # Payment sensitivity analysis
    payment_range = np.linspace(8000, 20000, 50)
    payoff_times = []
    
    for payment in payment_range:
        if payment > r_annual * L0:
            t_payoff = np.log(payment / (payment - r_annual * L0)) / r_annual
            payoff_times.append(t_payoff)
        else:
            payoff_times.append(np.inf)
    
    valid_times = np.array(payoff_times)
    valid_times = valid_times[valid_times < 50]  # Reasonable time limit
    valid_payments = payment_range[:len(valid_times)]
    
    plt.figure(figsize=(10, 6))
    plt.plot(valid_payments, valid_times, 'g-', linewidth=2)
    plt.axvline(x=annual_interest, color='red', linestyle='--', 
                alpha=0.7, label='Minimum Payment (Interest Only)')
    plt.xlabel('Annual Payment ($)')
    plt.ylabel('Payoff Time (years)')
    plt.title('Payment Amount vs Payoff Time')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

loan_repayment_analysis()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>Economic Models in R
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(gridExtra)

# Investment models
simple_interest_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    A <- state[1]
    dAdt <- r * P  # Simple interest
    return(list(dAdt))
  })
}

compound_interest_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    A <- state[1]
    dAdt <- r * A  # Compound interest
    return(list(dAdt))
  })
}

investment_deposits_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    A <- state[1]
    dAdt <- r * A + D  # With regular deposits
    return(list(dAdt))
  })
}

investment_withdrawals_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    A <- state[1]
    dAdt <- r * A - W  # With regular withdrawals
    return(list(dAdt))
  })
}

# Parameters
r <- 0.05  # 5% annual interest rate
P <- 1000  # Principal for simple interest
D <- 1000  # Annual deposits
W <- 500   # Annual withdrawals
A0 <- 1000 # Initial amount

times <- seq(0, 30, by = 0.1)

# Solve different investment models
parms_simple <- list(r = r, P = P)
parms_compound <- list(r = r)
parms_deposits <- list(r = r, D = D)
parms_withdrawals <- list(r = r, W = W)

sol_simple <- ode(y = c(A = A0), times = times, 
                  func = simple_interest_model, parms = parms_simple)
sol_compound <- ode(y = c(A = A0), times = times, 
                   func = compound_interest_model, parms = parms_compound)
sol_deposits <- ode(y = c(A = 0), times = times, 
                   func = investment_deposits_model, parms = parms_deposits)
sol_withdrawals <- ode(y = c(A = A0 * 5), times = times, 
                      func = investment_withdrawals_model, parms = parms_withdrawals)

# Convert to data frames
df_simple <- as.data.frame(sol_simple) %>% mutate(Model = "Simple Interest")
df_compound <- as.data.frame(sol_compound) %>% mutate(Model = "Compound Interest")
df_deposits <- as.data.frame(sol_deposits) %>% mutate(Model = "With Deposits")
df_withdrawals <- as.data.frame(sol_withdrawals) %>% mutate(Model = "With Withdrawals")

# Combine for comparison
investment_df <- rbind(
  df_simple %>% select(time, A, Model),
  df_compound %>% select(time, A, Model),
  df_deposits %>% select(time, A, Model),
  df_withdrawals %>% filter(A > 0) %>% select(time, A, Model)
)

# Plot investment comparison
p1 <- ggplot(investment_df, aes(x = time, y = A, color = Model)) +
  geom_line(size = 1.2) +
  labs(title = "Investment Growth Models Comparison",
       x = "Time (years)",
       y = "Account Balance ($)",
       color = "Investment Type") +
  theme_minimal() +
  theme(legend.position = "bottom") +
  scale_y_continuous(labels = scales::dollar_format())

print(p1)

# Analytical solutions for verification
times_analytical <- seq(0, 30, by = 1)
analytical_df <- data.frame(
  time = times_analytical,
  simple = A0 + r * P * times_analytical,
  compound = A0 * exp(r * times_analytical),
  deposits = (D/r) * (exp(r * times_analytical) - 1),
  withdrawals = A0 * 5 * exp(r * times_analytical) - 
                (W/r) * (exp(r * times_analytical) - 1)
) %>%
  filter(withdrawals > 0) %>%
  pivot_longer(cols = -time, names_to = "Model", values_to = "Balance")

# Market price dynamics
price_adjustment_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    p <- state[1]
    
    # Linear demand and supply
    demand <- a_d - b_d * p
    supply <- c_s + d_s * p
    
    dpdt <- k * (demand - supply)
    return(list(dpdt))
  })
}

# Market parameters
market_parms <- list(
  k = 0.1,     # Adjustment speed
  a_d = 1000,  # Demand intercept
  b_d = 2,     # Demand slope
  c_s = 200,   # Supply intercept
  d_s = 1      # Supply slope
)

# Equilibrium price
p_eq <- with(market_parms, (a_d - c_s) / (b_d + d_s))
cat("Equilibrium price: $", round(p_eq, 2), "\n")

# Solve for different initial prices
initial_prices <- c(100, 200, 300, 400)
market_results <- list()

for(i in seq_along(initial_prices)) {
  sol <- ode(y = c(p = initial_prices[i]), 
             times = seq(0, 20, by = 0.1),
             func = price_adjustment_model, 
             parms = market_parms)
  
  df <- as.data.frame(sol) %>%
    mutate(Initial_Price = paste("$", initial_prices[i]))
  
  market_results[[i]] <- df
}

market_df <- do.call(rbind, market_results)

# Plot market dynamics
p2 <- ggplot(market_df, aes(x = time, y = p, color = Initial_Price)) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = p_eq, linetype = "dashed", 
             color = "black", alpha = 0.7) +
  labs(title = "Market Price Adjustment Dynamics",
       x = "Time",
       y = "Price ($)",
       color = "Initial Price") +
  theme_minimal() +
  theme(legend.position = "bottom") +
  annotate("text", x = 15, y = p_eq + 20, 
           label = paste("Equilibrium: $", round(p_eq, 2)))

print(p2)

# Phase portrait for market dynamics
p_range <- seq(50, 400, by = 5)
phase_data <- data.frame(
  price = p_range,
  dpdt = with(market_parms, {
    demand <- a_d - b_d * p_range
    supply <- c_s + d_s * p_range
    k * (demand - supply)
  })
)

p3 <- ggplot(phase_data, aes(x = price, y = dpdt)) +
  geom_line(color = "blue", size = 1.2) +
  geom_hline(yintercept = 0, linetype = "dashed", 
             color = "red", alpha = 0.7) +
  geom_vline(xintercept = p_eq, linetype = "dashed", 
             color = "green", alpha = 0.7) +
  labs(title = "Market Price Phase Portrait",
       x = "Price ($)",
       y = "Price Change Rate (dp/dt)") +
  theme_minimal()

print(p3)

# Loan repayment analysis
loan_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    L <- state[1]
    dLdt <- r * L - payment
    return(list(dLdt))
  })
}

# Loan parameters
L0 <- 100000  # Initial loan ($100,000)
r_loan <- 0.06  # 6% annual interest rate
payment <- 12000  # $12,000 annual payment

loan_parms <- list(r = r_loan, payment = payment)

# Check feasibility
annual_interest <- r_loan * L0
cat("Annual interest: $", annual_interest, "\n")
cat("Annual payment: $", payment, "\n")

if(payment > annual_interest) {
  # Calculate theoretical payoff time
  payoff_time <- log(payment / (payment - annual_interest)) / r_loan
  cat("Theoretical payoff time:", round(payoff_time, 2), "years\n")
  
  # Solve numerically
  times_loan <- seq(0, min(20, payoff_time * 1.2), by = 0.01)
  sol_loan <- ode(y = c(L = L0), times = times_loan, 
                  func = loan_model, parms = loan_parms)
  
  loan_df <- as.data.frame(sol_loan) %>%
    filter(L > 0)  # Only positive balances
  
  # Plot loan balance
  p4 <- ggplot(loan_df, aes(x = time, y = L)) +
    geom_line(color = "blue", size = 1.2) +
    geom_hline(yintercept = 0, linetype = "dashed", 
               color = "red", alpha = 0.7) +
    labs(title = "Loan Repayment Progress",
         x = "Time (years)",
         y = "Loan Balance ($)") +
    theme_minimal() +
    scale_y_continuous(labels = scales::dollar_format())
  
  print(p4)
  
  # Payment sensitivity analysis
  payment_range <- seq(8000, 20000, by = 500)
  payoff_times <- sapply(payment_range, function(p) {
    if(p > annual_interest) {
      log(p / (p - annual_interest)) / r_loan
    } else {
      NA
    }
  })
  
  sensitivity_df <- data.frame(
    payment = payment_range,
    payoff_time = payoff_times
  ) %>%
    filter(!is.na(payoff_time), payoff_time < 50)
  
  p5 <- ggplot(sensitivity_df, aes(x = payment, y = payoff_time)) +
    geom_line(color = "green", size = 1.2) +
    geom_vline(xintercept = annual_interest, linetype = "dashed", 
               color = "red", alpha = 0.7) +
    labs(title = "Payment Amount vs Payoff Time",
         x = "Annual Payment ($)",
         y = "Payoff Time (years)") +
    theme_minimal() +
    scale_x_continuous(labels = scales::dollar_format()) +
    annotate("text", x = annual_interest + 1000, y = 40, 
             label = "Minimum\nPayment", color = "red")
  
  print(p5)
  
} else {
  cat("Payment is insufficient to cover interest!\n")
}

# Economic optimization example
optimize_investment <- function() {
  # Find optimal deposit rate given budget constraint
  total_budget <- 50000  # Total available over 20 years
  time_horizon <- 20
  
  # Objective: maximize final account balance
  # Subject to: total deposits ≤ budget
  
  deposit_rates <- seq(1000, 4000, by = 100)
  final_balances <- sapply(deposit_rates, function(D) {
    if(D * time_horizon <= total_budget) {
      # Final balance with deposit rate D
      (D/r) * (exp(r * time_horizon) - 1)
    } else {
      NA
    }
  })
  
  optimization_df <- data.frame(
    deposit_rate = deposit_rates,
    final_balance = final_balances,
    total_deposits = deposit_rates * time_horizon
  ) %>%
    filter(!is.na(final_balance))
  
  optimal_idx <- which.max(optimization_df$final_balance)
  optimal_deposit <- optimization_df$deposit_rate[optimal_idx]
  optimal_balance <- optimization_df$final_balance[optimal_idx]
  
  cat("Optimal deposit rate: $", optimal_deposit, "per year\n")
  cat("Final balance: $", round(optimal_balance, 2), "\n")
  
  p6 <- ggplot(optimization_df, aes(x = deposit_rate, y = final_balance)) +
    geom_line(color = "purple", size = 1.2) +
    geom_point(x = optimal_deposit, y = optimal_balance, 
               color = "red", size = 4) +
    labs(title = "Investment Optimization",
         x = "Annual Deposit Rate ($)",
         y = "Final Account Balance ($)") +
    theme_minimal() +
    scale_x_continuous(labels = scales::dollar_format()) +
    scale_y_continuous(labels = scales::dollar_format()) +
    annotate("text", x = optimal_deposit, y = optimal_balance + 5000,
             label = paste("Optimal: $", optimal_deposit, "/year"))
  
  return(p6)
}

opt_plot <- optimize_investment()
print(opt_plot)</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Physics Applications -->
        <section id="physics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-atom text-purple-600 mr-3"></i>Physics Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-physics mr-2"></i>Physical Systems and ODEs</h3>
                <p class="mb-4">Many physical phenomena are naturally described by first-order differential equations.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Newton's Law of Cooling</h4>
                        <p class="text-sm mb-2">$\frac{dT}{dt} = -k(T - T_{\text{env}})$</p>
                        <p class="text-xs">Temperature equilibration</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Radioactive Decay</h4>
                        <p class="text-sm mb-2">$\frac{dN}{dt} = -\lambda N$</p>
                        <p class="text-xs">Exponential decay law</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Falling with Drag</h4>
                        <p class="text-sm mb-2">$m\frac{dv}{dt} = mg - bv$</p>
                        <p class="text-xs">Terminal velocity approach</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-thermometer-half text-red-600 mr-2"></i>Physics Applications Visualization
                </h3>
                <div class="large-chart">
                    <canvas id="physicsChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Engineering Systems -->
        <section id="engineering" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cog text-orange-600 mr-3"></i>Engineering Systems
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-microchip mr-2"></i>Electrical and Mechanical Systems</h3>
                <p class="mb-4">Engineering systems often involve first-order dynamics in their analysis and control.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">RC Electrical Circuits:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li><strong>Capacitor Charging:</strong> $RC\frac{dV_C}{dt} + V_C = V_{in}$</li>
                            <li><strong>Time Constant:</strong> $\tau = RC$</li>
                            <li><strong>Response Types:</strong> Step, ramp, sinusoidal</li>
                            <li><strong>Applications:</strong> Filters, timing circuits</li>
                        </ul>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Mechanical Systems:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li><strong>Fluid Flow:</strong> Tank draining, pipe flow</li>
                            <li><strong>Heat Transfer:</strong> Thermal time constants</li>
                            <li><strong>Mass Transfer:</strong> Diffusion processes</li>
                            <li><strong>Control Systems:</strong> First-order plant dynamics</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-bolt text-yellow-600 mr-2"></i>Engineering Systems Visualization
                </h3>
                <div class="large-chart">
                    <canvas id="engineeringChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Advanced Analysis -->
        <section id="advanced" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-microscope text-red-600 mr-3"></i>Advanced Analysis Techniques
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-brain mr-2"></i>Advanced Modeling Concepts</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">Stability Analysis:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li><strong>Equilibrium Points:</strong> Solutions where $\frac{dy}{dt} = 0$</li>
                            <li><strong>Linear Stability:</strong> Analyze behavior near equilibria</li>
                            <li><strong>Phase Line Analysis:</strong> Graphical stability assessment</li>
                            <li><strong>Bifurcation Points:</strong> Parameter values where stability changes</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">Sensitivity Analysis:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li><strong>Parameter Perturbation:</strong> How solutions change with parameters</li>
                            <li><strong>Local Sensitivity:</strong> $\frac{\partial y}{\partial p}$ at parameter values</li>
                            <li><strong>Global Sensitivity:</strong> Effects over entire parameter space</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Model Validation:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li><strong>Residual Analysis:</strong> Model vs data comparison</li>
                            <li><strong>Cross-Validation:</strong> Out-of-sample testing</li>
                            <li><strong>Information Criteria:</strong> AIC, BIC for model selection</li>
                            <li><strong>Uncertainty Quantification:</strong> Parameter confidence intervals</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">Extensions:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li><strong>Stochastic ODEs:</strong> Adding random components</li>
                            <li><strong>Delay Equations:</strong> Including time delays</li>
                            <li><strong>System Coupling:</strong> Preparing for higher-order systems</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-project-diagram text-purple-600 mr-2"></i>Stability and Bifurcation Analysis
                </h3>
                <div class="large-chart">
                    <canvas id="stabilityChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Case Studies -->
        <section id="case-studies" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-file-alt text-indigo-600 mr-3"></i>Comprehensive Case Studies
            </h2>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-virus mr-2"></i>Case Study 1: COVID-19 Early Spread Model</h3>
                <p class="mb-3">Model the early exponential phase of COVID-19 spread using real data and estimate key parameters.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Model: Exponential Growth Phase</h4>
                    <p>For early epidemic phases: $\frac{dI}{dt} = rI$</p>
                    <p>Where $I(t)$ = cumulative infections, $r$ = growth rate</p>
                    <p><strong>Solution:</strong> $I(t) = I_0 e^{rt}$</p>
                    <p><strong>Doubling Time:</strong> $T_d = \frac{\ln(2)}{r}$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Analysis Goals:</h4>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>Estimate growth rate $r$ from early data</li>
                        <li>Calculate doubling time and basic reproduction number</li>
                        <li>Assess model validity and limitations</li>
                        <li>Compare different regions/countries</li>
                        <li>Predict short-term growth (with caveats)</li>
                    </ul>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-leaf mr-2"></i>Case Study 2: Carbon Dating Application</h3>
                <p class="mb-3">Determine the age of archaeological artifacts using radioactive decay principles.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Radioactive Decay Model</h4>
                    <p>$\frac{dN}{dt} = -\lambda N$ where $N(t)$ = number of $^{14}C$ atoms</p>
                    <p><strong>Solution:</strong> $N(t) = N_0 e^{-\lambda t}$</p>
                    <p><strong>Half-life of $^{14}C$:</strong> $t_{1/2} = 5730$ years</p>
                    <p><strong>Decay constant:</strong> $\lambda = \frac{\ln(2)}{t_{1/2}} = 1.21 \times 10^{-4}$ year$^{-1}$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Dating Procedure:</h4>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>Measure current $^{14}C$ activity in sample</li>
                        <li>Compare with activity in living organisms</li>
                        <li>Use decay law to calculate elapsed time</li>
                        <li>Account for measurement uncertainties</li>
                        <li>Consider calibration corrections</li>
                    </ul>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-pie text-green-600 mr-2"></i>Case Studies Results
                </h3>
                <div class="large-chart">
                    <canvas id="caseStudyChart"></canvas>
                </div>
            </div>

            <div class="bg-gray-100 p-6 rounded-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-lightbulb text-yellow-600 mr-2"></i>Key Insights from Case Studies
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">Modeling Lessons:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li><strong>Model Validity:</strong> Always check assumptions and limitations</li>
                            <li><strong>Parameter Estimation:</strong> Use statistical methods for robust estimates</li>
                            <li><strong>Uncertainty:</strong> Quantify and propagate uncertainties properly</li>
                            <li><strong>Validation:</strong> Test predictions against independent data</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Practical Considerations:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li><strong>Data Quality:</strong> Understand measurement errors and biases</li>
                            <li><strong>Model Selection:</strong> Choose appropriate complexity level</li>
                            <li><strong>Interpretation:</strong> Communicate results with proper context</li>
                            <li><strong>Limitations:</strong> Clearly state model boundaries and assumptions</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Summary and Connections -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-graduation-cap text-blue-600 mr-3"></i>Chapter Summary and Forward Connections
            </h2>

            <div class="definition-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-check-circle mr-2"></i>What You've Accomplished</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">Modeling Skills:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Systematic approach to problem formulation</li>
                            <li>Translation from word problems to ODEs</li>
                            <li>Parameter estimation from real data</li>
                            <li>Model validation and uncertainty analysis</li>
                            <li>Sensitivity and stability analysis</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">Application Domains:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Mixing and chemical processes</li>
                            <li>Population and biological dynamics</li>
                            <li>Economic and financial systems</li>
                            <li>Physical and engineering applications</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Technical Skills:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Computational implementation (Python & R)</li>
                            <li>Numerical solution techniques</li>
                            <li>Professional visualization</li>
                            <li>Statistical analysis of ODE solutions</li>
                            <li>Advanced analysis techniques</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">Problem-Solving Framework:</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Identify appropriate solution method</li>
                            <li>Implement and verify solutions</li>
                            <li>Interpret results in context</li>
                            <li>Assess model limitations</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-arrow-right mr-2"></i>Connection to Higher-Order Systems</h3>
                <p class="mb-4">The modeling principles and solution techniques you've mastered prepare you for more complex systems:</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Second-Order ODEs</h4>
                        <p class="text-sm">Spring-mass systems, RLC circuits, harmonic oscillators</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Systems of ODEs</h4>
                        <p class="text-sm">Predator-prey, epidemic models, coupled oscillators</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Partial PDEs</h4>
                        <p class="text-sm">Heat equation, wave equation, diffusion processes</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-map-signs text-purple-600 mr-2"></i>Next Steps in Your ODE Journey
                </h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-3">Immediate Next Topics (Part 3):</h4>
                        <ul class="list-disc list-inside space-y-2">
                            <li><strong>Second-Order Linear ODEs:</strong> Characteristic equations, oscillatory solutions</li>
                            <li><strong>Nonhomogeneous Equations:</strong> Method of undetermined coefficients, variation of parameters</li>
                            <li><strong>Applications:</strong> Spring-mass systems, RLC circuits, forced oscillations</li>
                            <li><strong>Numerical Methods:</strong> Euler, Runge-Kutta methods for higher-order systems</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-3">Advanced Topics (Parts 4-6):</h4>
                        <ul class="list-disc list-inside space-y-2">
                            <li><strong>Systems of ODEs:</strong> Matrix methods, phase plane analysis</li>
                            <li><strong>Series Solutions:</strong> Power series, Frobenius method</li>
                            <li><strong>Laplace Transforms:</strong> Transform methods for solving ODEs</li>
                            <li><strong>Special Topics:</strong> Boundary value problems, Sturm-Liouville theory</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

    </div>

    <!-- Footer -->
    <div class="bg-gradient-to-r from-gray-800 to-gray-900 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <h3 class="text-2xl font-bold mb-4">Chapter 10: Modeling with First-Order ODEs - Complete!</h3>
            <p class="text-lg mb-4">You've mastered comprehensive modeling with first-order differential equations</p>
            <div class="flex justify-center space-x-6 text-sm">
                <span><i class="fas fa-check mr-1"></i>Real-world applications</span>
                <span><i class="fas fa-check mr-1"></i>Parameter estimation</span>
                <span><i class="fas fa-check mr-1"></i>Model validation</span>
                <span><i class="fas fa-check mr-1"></i>Advanced analysis</span>
            </div>
        </div>
    </div>

    <script>
        // Classification Decision Chart
        const classificationCtx = document.getElementById('classificationChart').getContext('2d');
        new Chart(classificationCtx, {
            type: 'doughnut',
            data: {
                labels: ['Separable', 'Linear First-Order', 'Exact', 'Other Methods'],
                datasets: [{
                    data: [35, 40, 20, 5],
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom',
                    labels: {
                        fontSize: 12,
                        padding: 20
                    }
                },
                title: {
                    display: true,
                    text: 'Distribution of First-Order ODE Solution Methods',
                    fontSize: 16
                }
            }
        });

        // Mixing Problem Visualization
        const mixingCtx = document.getElementById('mixingChart').getContext('2d');
        const timePoints = Array.from({length: 501}, (_, i) => i);
        const concentrationData = timePoints.map(t => 0.5 * (1 - Math.exp(-0.01 * t)));
        const equilibriumLine = timePoints.map(t => 0.5);

        new Chart(mixingCtx, {
            type: 'line',
            data: {
                labels: timePoints,
                datasets: [{
                    label: 'Concentration C(t)',
                    data: concentrationData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderWidth: 2,
                    fill: true
                }, {
                    label: 'Equilibrium (0.5 kg/L)',
                    data: equilibriumLine,
                    borderColor: 'rgb(255, 99, 132)',
                    borderDash: [5, 5],
                    borderWidth: 2,
                    fill: false,
                    pointRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                title: {
                    display: true,
                    text: 'Salt Water Tank Mixing Problem - Concentration vs Time'
                },
                scales: {
                    xAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Time (minutes)'
                        }
                    }],
                    yAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Concentration (kg/L)'
                        },
                        ticks: {
                            min: 0,
                            max: 0.6
                        }
                    }]
                },
                legend: {
                    position: 'bottom'
                }
            }
        });

        // Population Dynamics Chart
        const populationCtx = document.getElementById('populationChart').getContext('2d');
        const popTime = Array.from({length: 501}, (_, i) => i * 0.1);
        const exponentialData = popTime.map(t => 50 * Math.exp(0.1 * t));
        const logisticData = popTime.map(t => 1000 / (1 + (1000/50 - 1) * Math.exp(-0.1 * t)));
        const carryingCapacity = popTime.map(t => 1000);

        new Chart(populationCtx, {
            type: 'line',
            data: {
                labels: popTime,
                datasets: [{
                    label: 'Exponential Growth',
                    data: exponentialData.slice(0, 200), // Limit to reasonable range
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderWidth: 2,
                    fill: false
                }, {
                    label: 'Logistic Growth',
                    data: logisticData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    borderWidth: 2,
                    fill: false
                }, {
                    label: 'Carrying Capacity K=1000',
                    data: carryingCapacity,
                    borderColor: 'rgb(75, 192, 192)',
                    borderDash: [10, 5],
                    borderWidth: 2,
                    fill: false,
                    pointRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                title: {
                    display: true,
                    text: 'Population Growth Models Comparison'
                },
                scales: {
                    xAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Time (years)'
                        }
                    }],
                    yAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Population'
                        },
                        ticks: {
                            min: 0,
                            max: 1200
                        }
                    }]
                },
                legend: {
                    position: 'bottom'
                }
            }
        });

        // Economics Chart
        const economicsCtx = document.getElementById('economicsChart').getContext('2d');
        const ecoTime = Array.from({length: 301}, (_, i) => i * 0.1);
        const compoundData = ecoTime.map(t => 1000 * Math.exp(0.05 * t));
        const depositsData = ecoTime.map(t => (1000/0.05) * (Math.exp(0.05 * t) - 1));
        const simpleData = ecoTime.map(t => 1000 + 0.05 * 1000 * t);

        new Chart(economicsCtx, {
            type: 'line',
            data: {
                labels: ecoTime,
                datasets: [{
                    label: 'Compound Interest Only',
                    data: compoundData,
                    borderColor: 'rgb(255, 206, 86)',
                    backgroundColor: 'rgba(255, 206, 86, 0.1)',
                    borderWidth: 2,
                    fill: false
                }, {
                    label: 'With Regular Deposits',
                    data: depositsData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderWidth: 2,
                    fill: false
                }, {
                    label: 'Simple Interest (Reference)',
                    data: simpleData,
                    borderColor: 'rgb(153, 102, 255)',
                    backgroundColor: 'rgba(153, 102, 255, 0.1)',
                    borderWidth: 1,
                    borderDash: [5, 5],
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                title: {
                    display: true,
                    text: 'Investment Growth Strategies (5% Annual Rate)'
                },
                scales: {
                    xAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Time (years)'
                        }
                    }],
                    yAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Account Balance ($)'
                        },
                        ticks: {
                            min: 0,
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }]
                },
                legend: {
                    position: 'bottom'
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            return data.datasets[tooltipItem.datasetIndex].label + 
                                   ': $' + tooltipItem.yLabel.toLocaleString();
                        }
                    }
                }
            }
        });

        // Physics Chart
        const physicsCtx = document.getElementById('physicsChart').getContext('2d');
        const physTime = Array.from({length: 301}, (_, i) => i * 0.1);
        const coolingData = physTime.map(t => 20 + (100 - 20) * Math.exp(-0.1 * t));
        const decayData = physTime.map(t => 1000 * Math.exp(-0.693/10 * t)); // Half-life = 10
        const velocityData = physTime.map(t => 50 * (1 - Math.exp(-0.2 * t))); // Terminal velocity = 50

        new Chart(physicsCtx, {
            type: 'line',
            data: {
                labels: physTime,
                datasets: [{
                    label: 'Newton\'s Cooling (°C)',
                    data: coolingData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    yAxisID: 'y-axis-1'
                }, {
                    label: 'Radioactive Decay (units)',
                    data: decayData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    yAxisID: 'y-axis-2'
                }, {
                    label: 'Falling with Drag (m/s)',
                    data: velocityData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    yAxisID: 'y-axis-1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                title: {
                    display: true,
                    text: 'Classic Physics Applications of First-Order ODEs'
                },
                scales: {
                    xAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'Time'
                        }
                    }],
                    yAxes: [{
                        type: 'linear',
                        display: true,
                        position: 'left',
                        id: 'y-axis-1',
                        scaleLabel: {
                            display: true,
                            labelString: 'Temperature (°C) / Velocity (m/s)'
                        }
                    }, {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        id: 'y-axis-2',
                        scaleLabel: {
                            display: true,
                            labelString: 'Radioactive Amount'
                        },
                        gridLines: {
                            drawOnChartArea: false
                        }
                    }]
                },
                legend: {
                    position: 'bottom'
                }
            }
        });

        // Engineering Chart
        const engineeringCtx = document.getElementById('engineeringChart').getContext('2d');
        const engTime = Array.from({length: 201}, (_, i) => i * 0.05);
        const rcChargingData = engTime.map(t => 12 * (1 - Math.exp(-t/5))); // RC charging, tau=5
        const rcDischargingData = engTime.map(t => 12 * Math.exp(-t/5)); // RC discharging
        const stepResponseData = engTime.map(t => t < 2 ? 0 : 12 * (1 - Math.exp(-(t-2)/3))); // Step response

        new Chart(engineeringCtx, {
            type: 'line',
            data: {
                labels: engTime,
                datasets: [{
                    label: 'RC Circuit Charging',
                    data: rcChargingData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderWidth: 2,
                    fill: false
                }, {
                    label: 'RC Circuit Discharging',
                    data: rcDischargingData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162,
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDj9eDkHp2RhcPdeJ7B31Uvx9z6Irw360L3eDpMnyMiwtTDepXvI%2Fj39Veh1eH%2FSyfxkLG65OOT24wJYVJCOrQ6ATAhwTIGVahHMN0fZ2tndFnXU8tMKo37i4sNAS1I0Uqj7rIszo%2F%2Fc4dw2hr4mSeIijMAqDL5HmcrPNYM76IjTg9ld5dknzF8XKZTlIDQQoFB9OYSi3eL1L3Ln4n%2BJBkM%2FdPN1emt89GgeB2a3bQ2ERv8ZmjdxT6EhhOAcHWmAHScI0XEuuDzO6sbpndb05GtCcLERljDb%2BSn6lfJJve2mhfUeFtt7x8btBfmUJXAHNQNHe%2FmYAqApFcz%2F6UywWE4Ty5T3cJsrdF7UrZGE2hjvc9e8b4sDvyiN45d5nbXOPl1Dyt8prAhaGFR5clzdyC4bRyjPW6v%2F%2FtWmKHCXUGrtGzb9mVbBMOoe4CDyLxF7Aojz1gDhSAbk0HlRLbwizA3763TC1sjzGdzNEgR%2FC16uKua6P%2F352WdtbKrZ4tvVsp5Qm6lc2qmZB%2FgwZ3YgBgwfY6HQ3o7JhMt9%2BgNJNOqZK";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDj9eDkHp2RhcPdeJ7B31Uvx9z6Irw360L3eDpMnyMiwtTDepXvI/j39Veh1eH/SyfxkLG65OOT24wJYVJCOrQ6ATAhwTIGVahHMN0fZ2tndFnXU8tMKo37i4sNAS1I0Uqj7rIszo//c4dw2hr4mSeIijMAqDL5HmcrPNYM76IjTg9ld5dknzF8XKZTlIDQQoFB9OYSi3eL1L3Ln4n+JBkM/dPN1emt89GgeB2a3bQ2ERv8ZmjdxT6EhhOAcHWmAHScI0XEuuDzO6sbpndb05GtCcLERljDb+Sn6lfJJve2mhfUeFtt7x8btBfmUJXAHNQNHe/mYAqApFcz/6UywWE4Ty5T3cJsrdF7UrZGE2hjvc9e8b4sDvyiN45d5nbXOPl1Dyt8prAhaGFR5clzdyC4bRyjPW6v//tWmKHCXUGrtGzb9mVbBMOoe4CDyLxF7Aojz1gDhSAbk0HlRLbwizA3763TC1sjzGdzNEgR/C16uKua6P/352WdtbKrZ4tvVsp5Qm6lc2qmZB/gwZ3YgBgwfY6HQ3o7JhMt9+gNJNOqZK";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    