<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 7: Separable Equations - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-r.min.js"></script>
    <style>
        .math-container { overflow-x: auto; }
        .code-container { max-height: none; }
        .chart-container { height: 400px; margin: 20px 0; }
    </style>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
</head>
<body class="bg-gray-50 text-gray-900 leading-relaxed">

<div class="container mx-auto px-4 py-8 max-w-6xl">

<!-- Header -->
<header class="text-center mb-12 bg-white rounded-lg shadow-lg p-8">
    <h1 class="text-4xl font-bold text-blue-800 mb-4">
        <i class="fas fa-chart-line mr-3"></i>Chapter 7: Separable Equations
    </h1>
    <p class="text-xl text-gray-600 mb-2">Part 2: First-Order Differential Equations</p>
    <p class="text-lg text-gray-500">Master the fundamental technique of separation of variables</p>
    <div class="mt-6 flex justify-center space-x-4 text-sm text-gray-500">
        <span><i class="fas fa-book mr-1"></i>Theory & Applications</span>
        <span><i class="fab fa-python mr-1"></i>Python Implementation</span>
        <span><i class="fab fa-r-project mr-1"></i>R Implementation</span>
    </div>
</header>

<!-- Learning Objectives -->
<section class="mb-12 bg-blue-50 rounded-lg p-6">
    <h2 class="text-2xl font-bold text-blue-800 mb-4">
        <i class="fas fa-bullseye mr-2"></i>Learning Objectives
    </h2>
    <div class="grid md:grid-cols-2 gap-4">
        <ul class="space-y-2">
            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Recognize separable differential equations</li>
            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Apply separation of variables technique</li>
            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Solve exponential growth and decay problems</li>
        </ul>
        <ul class="space-y-2">
            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Model real-world phenomena with separable ODEs</li>
            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Implement solutions computationally</li>
            <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i>Validate solutions and interpret results</li>
        </ul>
    </div>
</section>

<!-- Section 1: Definition and Recognition -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fas fa-search mr-3"></i>1. Definition and Recognition of Separable Equations
    </h2>
    
    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">1.1 What is a Separable Equation?</h3>
        <div class="bg-blue-50 p-6 rounded-lg mb-6">
            <p class="text-lg mb-4">A <strong>separable differential equation</strong> is a first-order ODE that can be written in the form:</p>
            <div class="math-container text-center text-xl">
                $$\frac{dy}{dx} = g(x) \cdot h(y)$$
            </div>
            <p class="mt-4 text-gray-600">where $g(x)$ is a function of $x$ only, and $h(y)$ is a function of $y$ only.</p>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">1.2 Standard Forms</h3>
        <div class="grid md:grid-cols-2 gap-6">
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2">Form 1: Multiplicative</h4>
                <div class="math-container">$$\frac{dy}{dx} = g(x) \cdot h(y)$$</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2">Form 2: Ratio</h4>
                <div class="math-container">$$\frac{dy}{dx} = \frac{g(x)}{f(y)}$$</div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">1.3 Recognition Examples</h3>
        <div class="space-y-4">
            <div class="bg-green-50 border-l-4 border-green-500 p-4">
                <h4 class="font-semibold text-green-800">✓ Separable:</h4>
                <p>$\frac{dy}{dx} = xy$ (where $g(x) = x$ and $h(y) = y$)</p>
            </div>
            <div class="bg-green-50 border-l-4 border-green-500 p-4">
                <h4 class="font-semibold text-green-800">✓ Separable:</h4>
                <p>$\frac{dy}{dx} = \frac{x^2}{y^2 + 1}$ (where $g(x) = x^2$ and $f(y) = y^2 + 1$)</p>
            </div>
            <div class="bg-red-50 border-l-4 border-red-500 p-4">
                <h4 class="font-semibold text-red-800">✗ Not Separable:</h4>
                <p>$\frac{dy}{dx} = x + y$ (cannot separate variables)</p>
            </div>
        </div>
    </div>
</section>

<!-- Section 2: Solution Method -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fas fa-cogs mr-3"></i>2. Solution Method: Separation of Variables
    </h2>
    
    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">2.1 Step-by-Step Procedure</h3>
        <div class="bg-blue-50 p-6 rounded-lg">
            <div class="space-y-4">
                <div class="flex items-start">
                    <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1">1</span>
                    <div>
                        <p class="font-semibold">Identify the separable form</p>
                        <p class="text-gray-600">Write the equation as $\frac{dy}{dx} = g(x) \cdot h(y)$</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1">2</span>
                    <div>
                        <p class="font-semibold">Separate the variables</p>
                        <p class="text-gray-600">Rearrange to get $\frac{1}{h(y)} \, dy = g(x) \, dx$</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1">3</span>
                    <div>
                        <p class="font-semibold">Integrate both sides</p>
                        <p class="text-gray-600">$\int \frac{1}{h(y)} \, dy = \int g(x) \, dx$</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1">4</span>
                    <div>
                        <p class="font-semibold">Solve for y (if possible)</p>
                        <p class="text-gray-600">Express $y$ explicitly in terms of $x$ and the constant</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">2.2 Detailed Example</h3>
        <div class="bg-gray-50 p-6 rounded-lg">
            <p class="font-semibold mb-4">Solve: $\frac{dy}{dx} = xy$</p>
            <div class="space-y-4">
                <div>
                    <p><strong>Step 1:</strong> Identify $g(x) = x$ and $h(y) = y$</p>
                </div>
                <div>
                    <p><strong>Step 2:</strong> Separate variables</p>
                    <div class="math-container ml-4">$$\frac{dy}{y} = x \, dx$$</div>
                </div>
                <div>
                    <p><strong>Step 3:</strong> Integrate both sides</p>
                    <div class="math-container ml-4">$$\int \frac{dy}{y} = \int x \, dx$$</div>
                    <div class="math-container ml-4">$$\ln|y| = \frac{x^2}{2} + C$$</div>
                </div>
                <div>
                    <p><strong>Step 4:</strong> Solve for $y$</p>
                    <div class="math-container ml-4">$$|y| = e^{x^2/2 + C} = A e^{x^2/2}$$</div>
                    <div class="math-container ml-4">$$y = \pm A e^{x^2/2}$$</div>
                    <p class="ml-4 text-gray-600">where $A = e^C > 0$, or letting $K = \pm A$:</p>
                    <div class="math-container ml-4 text-blue-600 font-semibold">$$y = K e^{x^2/2}$$</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section 3: Applications -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fas fa-rocket mr-3"></i>3. Applications of Separable Equations
    </h2>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">3.1 Exponential Growth and Decay</h3>
        <div class="bg-green-50 p-6 rounded-lg mb-6">
            <h4 class="text-xl font-semibold mb-3">The Fundamental Model</h4>
            <div class="math-container text-center text-lg">
                $$\frac{dP}{dt} = kP$$
            </div>
            <p class="mt-4">where:</p>
            <ul class="list-disc list-inside mt-2 space-y-1">
                <li>$P(t)$ = population (or quantity) at time $t$</li>
                <li>$k$ = growth rate constant ($k > 0$ for growth, $k < 0$ for decay)</li>
            </ul>
            <div class="mt-4 p-4 bg-white rounded border">
                <p class="font-semibold">Solution:</p>
                <div class="math-container">$$P(t) = P_0 e^{kt}$$</div>
                <p class="text-sm text-gray-600 mt-2">where $P_0 = P(0)$ is the initial value</p>
            </div>
        </div>

        <div class="grid md:grid-cols-2 gap-6 mb-6">
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2"><i class="fas fa-chart-line mr-2"></i>Population Growth</h4>
                <p class="text-sm">Bacteria, human populations, investments</p>
                <p class="text-xs text-gray-600 mt-2">$k > 0$: Exponential growth</p>
            </div>
            <div class="bg-red-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2"><i class="fas fa-chart-line-down mr-2"></i>Radioactive Decay</h4>
                <p class="text-sm">Carbon-14 dating, drug metabolism</p>
                <p class="text-xs text-gray-600 mt-2">$k < 0$: Exponential decay</p>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">3.2 Newton's Law of Cooling</h3>
        <div class="bg-orange-50 p-6 rounded-lg">
            <div class="math-container text-center text-lg mb-4">
                $$\frac{dT}{dt} = -k(T - T_{\text{ambient}})$$
            </div>
            <p class="mb-4">where:</p>
            <ul class="list-disc list-inside space-y-1 mb-4">
                <li>$T(t)$ = temperature of object at time $t$</li>
                <li>$T_{\text{ambient}}$ = ambient temperature (constant)</li>
                <li>$k > 0$ = cooling constant</li>
            </ul>
            <div class="p-4 bg-white rounded border">
                <p class="font-semibold">Solution:</p>
                <div class="math-container">$$T(t) = T_{\text{ambient}} + (T_0 - T_{\text{ambient}}) e^{-kt}$$</div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">3.3 Compound Interest (Continuous Compounding)</h3>
        <div class="bg-yellow-50 p-6 rounded-lg">
            <div class="math-container text-center text-lg mb-4">
                $$\frac{dA}{dt} = rA$$
            </div>
            <p class="mb-4">where:</p>
            <ul class="list-disc list-inside space-y-1 mb-4">
                <li>$A(t)$ = account balance at time $t$</li>
                <li>$r$ = annual interest rate (as decimal)</li>
            </ul>
            <div class="p-4 bg-white rounded border">
                <p class="font-semibold">Solution:</p>
                <div class="math-container">$$A(t) = A_0 e^{rt}$$</div>
                <p class="text-sm text-gray-600 mt-2">Compare with discrete compounding: $A(t) = A_0(1 + r/n)^{nt}$</p>
            </div>
        </div>
    </div>
</section>

<!-- Section 4: Python Implementation -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fab fa-python mr-3"></i>4. Python Implementation
    </h2>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">4.1 Symbolic Solutions with SymPy</h3>
        <div class="bg-gray-800 rounded-lg p-4 mb-4">
            <pre><code class="language-python">import sympy as sp
import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint

# Define symbols
x, y, t, k, C = sp.symbols('x y t k C')

# Example 1: Solve dy/dx = xy
print("Solving dy/dx = xy")
equation1 = sp.Eq(sp.Derivative(y, x), x * y)
solution1 = sp.dsolve(equation1, y)
print(f"General solution: {solution1}")

# Example 2: Exponential growth dP/dt = kP
P = sp.Function('P')
equation2 = sp.Eq(sp.Derivative(P(t), t), k * P(t))
solution2 = sp.dsolve(equation2, P(t))
print(f"Population growth solution: {solution2}")

# Example 3: Newton's cooling with specific values
T = sp.Function('T')
T_amb = 20  # ambient temperature
k_cool = 0.1  # cooling constant
equation3 = sp.Eq(sp.Derivative(T(t), t), -k_cool * (T(t) - T_amb))
solution3 = sp.dsolve(equation3, T(t))
print(f"Cooling solution: {solution3}")
</code></pre>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">4.2 Numerical Solutions and Visualization</h3>
        <div class="bg-gray-800 rounded-lg p-4 mb-4">
            <pre><code class="language-python"># Numerical solution for exponential growth
def exponential_growth(P, t, k):
    """ODE function for dP/dt = kP"""
    return k * P

# Parameters
k_values = [0.1, 0.2, 0.3]  # Different growth rates
P0 = 100  # Initial population
t_span = np.linspace(0, 20, 100)

# Create visualization
plt.figure(figsize=(12, 8))

# Plot analytical solutions
for i, k in enumerate(k_values):
    P_analytical = P0 * np.exp(k * t_span)
    plt.subplot(2, 2, 1)
    plt.plot(t_span, P_analytical, label=f'k = {k}', linewidth=2)

plt.subplot(2, 2, 1)
plt.title('Exponential Growth: P(t) = P₀e^(kt)')
plt.xlabel('Time')
plt.ylabel('Population')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot decay example
k_decay = -0.1
P_decay = P0 * np.exp(k_decay * t_span)
plt.subplot(2, 2, 2)
plt.plot(t_span, P_decay, 'r-', linewidth=2, label='Decay (k = -0.1)')
plt.title('Exponential Decay')
plt.xlabel('Time')
plt.ylabel('Population')
plt.legend()
plt.grid(True, alpha=0.3)

# Newton's cooling example
def cooling_function(T, t, k, T_amb):
    return -k * (T - T_amb)

T0 = 100  # Initial temperature
T_ambient = 20
k_cooling = 0.1
T_solution = odeint(cooling_function, T0, t_span, args=(k_cooling, T_ambient))

plt.subplot(2, 2, 3)
plt.plot(t_span, T_solution, 'g-', linewidth=2, label='Numerical')
T_analytical = T_ambient + (T0 - T_ambient) * np.exp(-k_cooling * t_span)
plt.plot(t_span, T_analytical, 'g--', linewidth=2, label='Analytical')
plt.axhline(y=T_ambient, color='gray', linestyle=':', alpha=0.7, label='Ambient')
plt.title("Newton's Law of Cooling")
plt.xlabel('Time')
plt.ylabel('Temperature')
plt.legend()
plt.grid(True, alpha=0.3)

# Phase portrait for dy/dx = xy
x_vals = np.linspace(-2, 2, 20)
y_vals = np.linspace(-3, 3, 20)
X, Y = np.meshgrid(x_vals, y_vals)
DX = np.ones_like(X)
DY = X * Y  # dy/dx = xy

plt.subplot(2, 2, 4)
plt.quiver(X, Y, DX, DY, angles='xy', scale_units='xy', scale=1, alpha=0.6)
# Add solution curves
x_curve = np.linspace(-1.5, 1.5, 100)
for C in [-2, -1, 0.5, 1, 2]:
    y_curve = C * np.exp(x_curve**2 / 2)
    plt.plot(x_curve, y_curve, 'r-', alpha=0.8)
    if C != 0:
        y_curve_neg = -C * np.exp(x_curve**2 / 2)
        plt.plot(x_curve, y_curve_neg, 'r-', alpha=0.8)

plt.title('Direction Field for dy/dx = xy')
plt.xlabel('x')
plt.ylabel('y')
plt.grid(True, alpha=0.3)
plt.xlim(-2, 2)
plt.ylim(-3, 3)

plt.tight_layout()
plt.show()
</code></pre>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">4.3 Parameter Estimation from Data</h3>
        <div class="bg-gray-800 rounded-lg p-4 mb-4">
            <pre><code class="language-python">from scipy.optimize import curve_fit
from sklearn.metrics import r2_score

# Simulate experimental data with noise
np.random.seed(42)
t_data = np.array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
k_true = 0.15
P0_true = 50
P_true = P0_true * np.exp(k_true * t_data)
P_data = P_true + np.random.normal(0, 2, len(t_data))  # Add noise

# Define model function
def exponential_model(t, P0, k):
    return P0 * np.exp(k * t)

# Fit the model
popt, pcov = curve_fit(exponential_model, t_data, P_data)
P0_fit, k_fit = popt

# Calculate confidence intervals
param_errors = np.sqrt(np.diag(pcov))
P0_error, k_error = param_errors

print(f"True parameters: P0 = {P0_true}, k = {k_true}")
print(f"Fitted parameters: P0 = {P0_fit:.3f} ± {P0_error:.3f}")
print(f"                   k = {k_fit:.3f} ± {k_error:.3f}")

# Model predictions
t_fine = np.linspace(0, 12, 100)
P_fit = exponential_model(t_fine, P0_fit, k_fit)
P_true_curve = exponential_model(t_fine, P0_true, k_true)

# Calculate R-squared
P_pred_data = exponential_model(t_data, P0_fit, k_fit)
r2 = r2_score(P_data, P_pred_data)
print(f"R-squared: {r2:.4f}")

# Visualization
plt.figure(figsize=(10, 6))
plt.scatter(t_data, P_data, color='red', s=50, label='Experimental Data', zorder=5)
plt.plot(t_fine, P_true_curve, 'g--', linewidth=2, label=f'True Model (k={k_true})', alpha=0.7)
plt.plot(t_fine, P_fit, 'b-', linewidth=2, label=f'Fitted Model (k={k_fit:.3f})')
plt.fill_between(t_fine, 
                 exponential_model(t_fine, P0_fit - P0_error, k_fit - k_error),
                 exponential_model(t_fine, P0_fit + P0_error, k_fit + k_error),
                 alpha=0.2, color='blue', label='Confidence Band')
plt.xlabel('Time')
plt.ylabel('Population')
plt.title('Parameter Estimation for Exponential Growth')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()
</code></pre>
        </div>
    </div>
</section>

<!-- Section 5: R Implementation -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fab fa-r-project mr-3"></i>5. R Implementation
    </h2>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">5.1 Symbolic Solutions with Ryacas</h3>
        <div class="bg-gray-800 rounded-lg p-4 mb-4">
            <pre><code class="language-r"># Load required libraries
library(Ryacas)
library(deSolve)
library(ggplot2)
library(gridExtra)

# Symbolic solution for dy/dx = xy
cat("Solving dy/dx = xy symbolically:\n")
yac_str("OdeSolve(y' == x*y, x, y)")

# Symbolic solution for exponential growth
cat("\nSolving dP/dt = k*P:\n")
yac_str("OdeSolve(P' == k*P, t, P)")

# More complex example: Newton's cooling
cat("\nSolving Newton's cooling dT/dt = -k*(T - T_amb):\n")
yac_str("OdeSolve(T' == -k*(T - T_amb), t, T)")
</code></pre>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">5.2 Numerical Solutions with deSolve</h3>
        <div class="bg-gray-800 rounded-lg p-4 mb-4">
            <pre><code class="language-r"># Define ODE functions
exponential_growth <- function(t, P, parms) {
  with(parms, {
    dP <- k * P
    return(list(dP))
  })
}

newton_cooling <- function(t, T, parms) {
  with(parms, {
    dT <- -k * (T - T_amb)
    return(list(dT))
  })
}

# Parameters and initial conditions
times <- seq(0, 20, by = 0.1)
P0 <- 100
T0 <- 100
T_amb <- 20

# Solve exponential growth for different k values
k_values <- c(0.1, 0.2, 0.3)
growth_solutions <- list()

for (i in seq_along(k_values)) {
  parms <- list(k = k_values[i])
  sol <- ode(y = P0, times = times, func = exponential_growth, parms = parms)
  growth_solutions[[i]] <- data.frame(
    time = sol[, 1],
    population = sol[, 2],
    k = k_values[i]
  )
}

# Combine results
growth_data <- do.call(rbind, growth_solutions)
growth_data$k <- factor(growth_data$k)

# Solve Newton's cooling
parms_cooling <- list(k = 0.1, T_amb = T_amb)
cooling_sol <- ode(y = T0, times = times, func = newton_cooling, parms = parms_cooling)
cooling_data <- data.frame(
  time = cooling_sol[, 1],
  temperature = cooling_sol[, 2],
  type = "Numerical"
)

# Add analytical solution for comparison
cooling_analytical <- data.frame(
  time = times,
  temperature = T_amb + (T0 - T_amb) * exp(-0.1 * times),
  type = "Analytical"
)

cooling_combined <- rbind(cooling_data, cooling_analytical)
</code></pre>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">5.3 Advanced Visualization</h3>
        <div class="bg-gray-800 rounded-lg p-4 mb-4">
            <pre><code class="language-r"># Create comprehensive visualization
p1 <- ggplot(growth_data, aes(x = time, y = population, color = k)) +
  geom_line(size = 1.2) +
  scale_y_log10() +
  labs(title = "Exponential Growth: P(t) = P₀e^(kt)",
       x = "Time", y = "Population (log scale)",
       color = "Growth Rate k") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5))

p2 <- ggplot(cooling_combined, aes(x = time, y = temperature, color = type, linetype = type)) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = T_amb, linetype = "dotted", color = "gray", size = 1) +
  annotate("text", x = 15, y = T_amb + 2, label = "Ambient Temperature") +
  labs(title = "Newton's Law of Cooling",
       x = "Time", y = "Temperature",
       color = "Solution Type", linetype = "Solution Type") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5))

# Direction field for dy/dx = xy
create_direction_field <- function() {
  x_vals <- seq(-2, 2, by = 0.2)
  y_vals <- seq(-3, 3, by = 0.3)
  
  field_data <- expand.grid(x = x_vals, y = y_vals)
  field_data$dx <- 1
  field_data$dy <- field_data$x * field_data$y
  
  # Normalize arrows
  arrow_length <- sqrt(field_data$dx^2 + field_data$dy^2)
  scale_factor <- 0.1
  field_data$dx_norm <- field_data$dx / arrow_length * scale_factor
  field_data$dy_norm <- field_data$dy / arrow_length * scale_factor
  
  # Create solution curves
  x_curve <- seq(-1.5, 1.5, by = 0.05)
  C_values <- c(-2, -1, 0.5, 1, 2)
  solution_curves <- list()
  
  for (i in seq_along(C_values)) {
    C <- C_values[i]
    y_pos <- C * exp(x_curve^2 / 2)
    y_neg <- -C * exp(x_curve^2 / 2)
    
    solution_curves[[2*i-1]] <- data.frame(x = x_curve, y = y_pos, curve = i)
    if (C != 0) {
      solution_curves[[2*i]] <- data.frame(x = x_curve, y = y_neg, curve = -i)
    }
  }
  
  curves_data <- do.call(rbind, solution_curves)
  curves_data <- curves_data[abs(curves_data$y) <= 3, ]  # Filter extreme values
  
  p3 <- ggplot() +
    geom_segment(data = field_data, 
                 aes(x = x, y = y, xend = x + dx_norm, yend = y + dy_norm),
                 arrow = arrow(length = unit(0.02, "npc")), 
                 color = "blue", alpha = 0.6) +
    geom_path(data = curves_data, aes(x = x, y = y, group = curve), 
              color = "red", size = 1, alpha = 0.8) +
    labs(title = "Direction Field for dy/dx = xy",
         x = "x", y = "y") +
    coord_cartesian(xlim = c(-2, 2), ylim = c(-3, 3)) +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5))
  
  return(p3)
}

p3 <- create_direction_field()

# Model fitting example
set.seed(42)
t_data <- 0:10
k_true <- 0.15
P0_true <- 50
P_true <- P0_true * exp(k_true * t_data)
P_data <- P_true + rnorm(length(t_data), 0, 2)

# Fit exponential model
fit_data <- data.frame(t = t_data, P = P_data)
model <- nls(P ~ P0 * exp(k * t), data = fit_data, 
             start = list(P0 = 40, k = 0.1))

# Extract parameters
P0_fit <- coef(model)["P0"]
k_fit <- coef(model)["k"]

# Predictions
t_fine <- seq(0, 12, by = 0.1)
P_pred <- P0_fit * exp(k_fit * t_fine)

# Create fitting plot
fitting_data <- data.frame(
  time = c(t_data, t_fine),
  population = c(P_data, P_pred),
  type = c(rep("Data", length(t_data)), rep("Fitted", length(t_fine)))
)

p4 <- ggplot() +
  geom_point(data = subset(fitting_data, type == "Data"), 
             aes(x = time, y = population), color = "red", size = 3) +
  geom_line(data = subset(fitting_data, type == "Fitted"), 
            aes(x = time, y = population), color = "blue", size = 1.2) +
  labs(title = sprintf("Parameter Estimation\nFitted: P₀=%.1f, k=%.3f", P0_fit, k_fit),
       x = "Time", y = "Population") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5))

# Combine all plots
grid.arrange(p1, p2, p3, p4, ncol = 2)
</code></pre>
        </div>
    </div>
</section>

<!-- Interactive Chart Section -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fas fa-chart-area mr-3"></i>6. Interactive Visualization
    </h2>
    
    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">Exponential Growth/Decay Comparison</h3>
        <div class="chart-container">
            <canvas id="exponentialChart"></canvas>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">Newton's Cooling Model</h3>
        <div class="chart-container">
            <canvas id="coolingChart"></canvas>
        </div>
    </div>
</section>

<!-- Section 6: Advanced Topics -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fas fa-graduation-cap mr-3"></i>7. Advanced Topics and Special Cases
    </h2>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">7.1 Logistic Growth Model</h3>
        <div class="bg-purple-50 p-6 rounded-lg mb-6">
            <p class="mb-4">The logistic equation is a modification of exponential growth that accounts for carrying capacity:</p>
            <div class="math-container text-center text-lg">
                $$\frac{dP}{dt} = rP\left(1 - \frac{P}{K}\right)$$
            </div>
            <p class="mt-4">where:</p>
            <ul class="list-disc list-inside space-y-1 mb-4">
                <li>$r$ = intrinsic growth rate</li>
                <li>$K$ = carrying capacity</li>
                <li>$P(t)$ = population at time $t$</li>
            </ul>
            <p class="mb-4">This is separable! We can write it as:</p>
            <div class="math-container text-center">
                $$\frac{dP}{dt} = \frac{rP(K-P)}{K}$$
            </div>
            <div class="p-4 bg-white rounded border mt-4">
                <p class="font-semibold">Solution:</p>
                <div class="math-container">$$P(t) = \frac{K}{1 + \left(\frac{K-P_0}{P_0}\right)e^{-rt}}$$</div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">7.2 Mixing Problems</h3>
        <div class="bg-cyan-50 p-6 rounded-lg">
            <p class="mb-4"><strong>Setup:</strong> A tank contains solution with changing concentration due to inflow and outflow.</p>
            <div class="math-container text-center text-lg">
                $$\frac{dA}{dt} = (\text{rate in}) - (\text{rate out})$$
            </div>
            <p class="mt-4 mb-4">For a tank with volume $V$, inflow rate $r_{\text{in}}$ with concentration $c_{\text{in}}$, and outflow rate $r_{\text{out}}$:</p>
            <div class="math-container text-center">
                $$\frac{dA}{dt} = r_{\text{in}} \cdot c_{\text{in}} - r_{\text{out}} \cdot \frac{A}{V}$$
            </div>
            <p class="mt-4 text-gray-600">This becomes separable when the volume is constant!</p>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">7.3 Orthogonal Trajectories</h3>
        <div class="bg-pink-50 p-6 rounded-lg">
            <p class="mb-4">Given a family of curves, we can find their orthogonal trajectories using separable equations.</p>
            <div class="p-4 bg-white rounded border">
                <p class="font-semibold mb-2">Method:</p>
                <ol class="list-decimal list-inside space-y-2">
                    <li>Find the differential equation of the given family</li>
                    <li>Replace $\frac{dy}{dx}$ with $-\frac{dx}{dy}$ (negative reciprocal)</li>
                    <li>Solve the resulting separable equation</li>
                </ol>
            </div>
        </div>
    </div>
</section>

<!-- Section 7: Problem Solving Strategies -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fas fa-lightbulb mr-3"></i>8. Problem-Solving Strategies
    </h2>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">8.1 Recognition Checklist</h3>
        <div class="bg-yellow-50 p-6 rounded-lg">
            <h4 class="font-semibold mb-3">Is the equation separable?</h4>
            <div class="space-y-3">
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                    <span>Can you write it as $\frac{dy}{dx} = g(x) \cdot h(y)$?</span>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                    <span>Are all $x$ terms on one side and all $y$ terms on the other after separation?</span>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                    <span>Can you integrate both sides?</span>
                </div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">8.2 Common Integration Techniques Needed</h3>
        <div class="grid md:grid-cols-2 gap-6">
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2">Basic Integrals</h4>
                <ul class="text-sm space-y-1">
                    <li>$\int \frac{dy}{y} = \ln|y| + C$</li>
                    <li>$\int y \, dy = \frac{y^2}{2} + C$</li>
                    <li>$\int e^y \, dy = e^y + C$</li>
                </ul>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
                <h4 class="font-semibold mb-2">Substitution</h4>
                <ul class="text-sm space-y-1">
                    <li>$\int \frac{dy}{ay + b} = \frac{1}{a}\ln|ay + b| + C$</li>
                    <li>$\int \frac{dy}{y^2 + a^2} = \frac{1}{a}\arctan\left(\frac{y}{a}\right) + C$</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">8.3 Verification Methods</h3>
        <div class="bg-orange-50 p-6 rounded-lg">
            <h4 class="font-semibold mb-3">Always verify your solution!</h4>
            <div class="space-y-3">
                <div>
                    <span class="font-medium">1. Direct substitution:</span>
                    <p class="text-sm text-gray-600 ml-4">Substitute $y$ and $\frac{dy}{dx}$ back into the original equation</p>
                </div>
                <div>
                    <span class="font-medium">2. Initial condition check:</span>
                    <p class="text-sm text-gray-600 ml-4">Verify that $y(x_0) = y_0$ when given</p>
                </div>
                <div>
                    <span class="font-medium">3. Behavior analysis:</span>
                    <p class="text-sm text-gray-600 ml-4">Check if the solution behavior matches physical expectations</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section 8: Exercises -->
<section class="mb-12 bg-white rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fas fa-dumbbell mr-3"></i>9. Practice Exercises
    </h2>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">9.1 Basic Separable Equations</h3>
        <div class="space-y-4">
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="font-semibold">Exercise 1:</p>
                <p>Solve $\frac{dy}{dx} = \frac{x}{y}$ with initial condition $y(0) = 2$</p>
                <details class="mt-2">
                    <summary class="cursor-pointer text-blue-600">Show solution</summary>
                    <div class="mt-2 p-3 bg-white rounded border">
                        <p>Separating: $y \, dy = x \, dx$</p>
                        <p>Integrating: $\frac{y^2}{2} = \frac{x^2}{2} + C$</p>
                        <p>General solution: $y^2 = x^2 + C$</p>
                        <p>Using $y(0) = 2$: $4 = 0 + C$, so $C = 4$</p>
                        <p><strong>Particular solution:</strong> $y = \sqrt{x^2 + 4}$ (taking positive root)</p>
                    </div>
                </details>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="font-semibold">Exercise 2:</p>
                <p>Solve $\frac{ds}{dt} = ks$ where $s(0) = s_0$ and $k$ is constant</p>
                <details class="mt-2">
                    <summary class="cursor-pointer text-blue-600">Show solution</summary>
                    <div class="mt-2 p-3 bg-white rounded border">
                        <p>This is the standard exponential model</p>
                        <p>Separating: $\frac{ds}{s} = k \, dt$</p>
                        <p>Integrating: $\ln|s| = kt + C$</p>
                        <p>Exponentiating: $s = Ae^{kt}$</p>
                        <p>Using $s(0) = s_0$: $s_0 = A$</p>
                        <p><strong>Solution:</strong> $s(t) = s_0 e^{kt}$</p>
                    </div>
                </details>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-2xl font-semibold text-gray-700 mb-4">9.2 Application Problems</h3>
        <div class="space-y-4">
            <div class="bg-blue-50 p-4 rounded-lg">
                <p class="font-semibold">Exercise 3: Population Growth</p>
                <p>A bacterial culture grows at a rate proportional to its size. If the population doubles in 3 hours and starts with 1000 bacteria, find the population after 8 hours.</p>
                <details class="mt-2">
                    <summary class="cursor-pointer text-blue-600">Show solution</summary>
                    <div class="mt-2 p-3 bg-white rounded border">
                        <p>Model: $\frac{dP}{dt} = kP$, $P(0) = 1000$</p>
                        <p>Solution: $P(t) = 1000e^{kt}$</p>
                        <p>Given $P(3) = 2000$: $2000 = 1000e^{3k}$</p>
                        <p>So $e^{3k} = 2$, thus $k = \frac{\ln 2}{3}$</p>
                        <p>Therefore: $P(t) = 1000 \cdot 2^{t/3}$</p>
                        <p><strong>After 8 hours:</strong> $P(8) = 1000 \cdot 2^{8/3} \approx 6350$ bacteria</p>
                    </div>
                </details>
            </div>

            <div class="bg-green-50 p-4 rounded-lg">
                <p class="font-semibold">Exercise 4: Newton's Cooling</p>
                <p>A cup of coffee at 90°C is placed in a room at 20°C. After 10 minutes, the temperature is 70°C. When will the coffee reach 30°C?</p>
                <details class="mt-2">
                    <summary class="cursor-pointer text-blue-600">Show solution</summary>
                    <div class="mt-2 p-3 bg-white rounded border">
                        <p>Model: $\frac{dT}{dt} = -k(T - 20)$</p>
                        <p>Solution: $T(t) = 20 + (90 - 20)e^{-kt} = 20 + 70e^{-kt}$</p>
                        <p>Given $T(10) = 70$: $70 = 20 + 70e^{-10k}$</p>
                        <p>So $50 = 70e^{-10k}$, thus $e^{-10k} = \frac{5}{7}$</p>
                        <p>Therefore $k = -\frac{1}{10}\ln\left(\frac{5}{7}\right) = \frac{\ln(7/5)}{10}$</p>
                        <p>For $T = 30$: $30 = 20 + 70e^{-kt}$</p>
                        <p>$10 = 70e^{-kt}$, so $e^{-kt} = \frac{1}{7}$</p>
                        <p>$t = \frac{\ln 7}{k} = \frac{10\ln 7}{\ln(7/5)} \approx 52.1$ minutes</p>
                    </div>
                </details>
            </div>
        </div>
    </div>
</section>

<!-- Summary Section -->
<section class="mb-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg shadow-lg p-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">
        <i class="fas fa-star mr-3"></i>Chapter Summary
    </h2>
    
    <div class="grid md:grid-cols-2 gap-8">
        <div>
            <h3 class="text-xl font-semibold mb-4">Key Concepts Mastered</h3>
            <ul class="space-y-2">
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                    <span>Recognition of separable equations</span>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                    <span>Separation of variables technique</span>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                    <span>Exponential growth and decay models</span>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                    <span>Newton's law of cooling applications</span>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                    <span>Computational implementation in Python and R</span>
                </li>
            </ul>
        </div>
        
        <div>
            <h3 class="text-xl font-semibold mb-4">Next Steps</h3>
            <div class="bg-white p-4 rounded-lg">
                <p class="mb-3"><strong>Chapter 8: Linear First-Order Equations</strong></p>
                <p class="text-gray-600 text-sm mb-3">Learn to solve equations of the form $\frac{dy}{dx} + P(x)y = Q(x)$ using integrating factors.</p>
                <p class="text-blue-600 font-medium">You're now ready to tackle non-separable linear equations!</p>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="text-center py-8 bg-gray-800 text-white rounded-lg mt-12">
    <div class="mb-4">
        <h3 class="text-xl font-semibold">Comprehensive ODE Tutorial</h3>
        <p class="text-gray-300">Chapter 7: Separable Equations</p>
    </div>
    <div class="flex justify-center space-x-6 text-sm">
        <span><i class="fas fa-book mr-1"></i>Mathematical Theory</span>
        <span><i class="fab fa-python mr-1"></i>Python Programming</span>
        <span><i class="fab fa-r-project mr-1"></i>R Statistical Computing</span>
        <span><i class="fas fa-chart-line mr-1"></i>Data Visualization</span>
    </div>
</footer>

</div>

<script>
// Chart.js implementations
document.addEventListener('DOMContentLoaded', function() {
    // Exponential Growth/Decay Chart
    const expCtx = document.getElementById('exponentialChart').getContext('2d');
    
    // Generate data for different k values
    const t = [];
    for (let i = 0; i <= 20; i += 0.5) {
        t.push(i);
    }
    
    const P0 = 100;
    const datasets = [];
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'];
    const kValues = [0.1, 0.2, -0.1, -0.2];
    const labels = ['Growth (k=0.1)', 'Growth (k=0.2)', 'Decay (k=-0.1)', 'Decay (k=-0.2)'];
    
    kValues.forEach((k, index) => {
        const data = t.map(time => P0 * Math.exp(k * time));
        datasets.push({
            label: labels[index],
            data: data,
            borderColor: colors[index],
            backgroundColor: colors[index] + '20',
            fill: false,
            tension: 0.1
        });
    });
    
    new Chart(expCtx, {
        type: 'line',
        data: {
            labels: t,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Exponential Growth and Decay: P(t) = P₀e^(kt)'
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Population'
                    },
                    type: 'logarithmic',
                    min: 1
                }
            }
        }
    });

    // Newton's Cooling Chart
    const coolCtx = document.getElementById('coolingChart').getContext('2d');
    
    const T0 = 100;
    const T_amb = 20;
    const k_cooling = 0.1;
    
    const coolingData = t.map(time => T_amb + (T0 - T_amb) * Math.exp(-k_cooling * time));
    const ambientData = t.map(() => T_amb);
    
    new Chart(coolCtx, {
        type: 'line',
        data: {
            labels: t,
            datasets: [
                {
                    label: 'Object Temperature',
                    data: coolingData,
                    borderColor: '#DC2626',
                    backgroundColor: '#DC262620',
                    fill: false,
                    tension: 0.1
                },
                {
                    label: 'Ambient Temperature',
                    data: ambientData,
                    borderColor: '#6B7280',
                    backgroundColor: '#6B728020',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: "Newton's Law of Cooling: T(t) = T_amb + (T₀ - T_amb)e^(-kt)"
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Temperature (°C)'
                    }
                }
            }
        }
    });
});
</script>

</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDnLrnrHFeEvlkQo4I27%2FYp1mbUq7VuudLx5KlJxgi3qXjB3tq1QLB%2FUVhke8SJDkN7UnWdHGByuuACtGbzUMiwUKalQq%2B1p%2F%2FYahdXcff3HfTCnirEd8Dt34ueG87i6Hf0yuGV1XcfOz1qIq%2BE0CtcA%2F4l2hm%2Fwa%2FkG%2Bnl6Hj74UMz5u5t3gErP7MMMlgeCjrPG4avij79INP3ZCirUTxGx1n9m7xTBQbGq7tYLOBWDb1QF94eqtlTNl0gD%2FiPY0juqfXWirrT0xywUQN7uStExWWQoV9kC4qo5CGD8wOuZQvx4%2FQ73NALGrBkzfrecO9BCoeFuTGbDWJKzlxq8QTQpH3qwkrnj88IjQbGUB0msjVS%2F2%2FKovL2h2k%2Fl2CuHJJeoNK%2FBvzTuZ4e6xhwiVPh3Zvm%2Fb61UYlyOy53Yq1rNf2mI7TsZThDdrEqk6s6Ztn8ZDynSq4HbdNAKLQ%2FmNxmuqD6H4eu3xvh1pZu%2BpQUkUen6ty17ORYnlS4MQ6Wr2PFWxwDhTvXklW0Wpet4lGXumvbP60Cm1WmPaqN9o50JB";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDnLrnrHFeEvlkQo4I27/Yp1mbUq7VuudLx5KlJxgi3qXjB3tq1QLB/UVhke8SJDkN7UnWdHGByuuACtGbzUMiwUKalQq+1p//YahdXcff3HfTCnirEd8Dt34ueG87i6Hf0yuGV1XcfOz1qIq+E0CtcA/4l2hm/wa/kG+nl6Hj74UMz5u5t3gErP7MMMlgeCjrPG4avij79INP3ZCirUTxGx1n9m7xTBQbGq7tYLOBWDb1QF94eqtlTNl0gD/iPY0juqfXWirrT0xywUQN7uStExWWQoV9kC4qo5CGD8wOuZQvx4/Q73NALGrBkzfrecO9BCoeFuTGbDWJKzlxq8QTQpH3qwkrnj88IjQbGUB0msjVS/2/KovL2h2k/l2CuHJJeoNK/BvzTuZ4e6xhwiVPh3Zvm/b61UYlyOy53Yq1rNf2mI7TsZThDdrEqk6s6Ztn8ZDynSq4HbdNAKLQ/mNxmuqD6H4eu3xvh1pZu+pQUkUen6ty17ORYnlS4MQ6Wr2PFWxwDhTvXklW0Wpet4lGXumvbP60Cm1WmPaqN9o50JB";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    